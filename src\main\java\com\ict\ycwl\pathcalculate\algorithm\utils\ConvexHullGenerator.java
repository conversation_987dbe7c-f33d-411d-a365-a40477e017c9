package com.ict.ycwl.pathcalculate.algorithm.utils;

import com.ict.ycwl.pathcalculate.algorithm.entity.CoordinatePoint;
import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;

import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LinearRing;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.algorithm.ConvexHull;

/**
 * 凸包生成工具类
 * 使用Jarvis步进算法生成凸包，确保凸包不重叠
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ConvexHullGenerator {
    
    /**
     * 使用JTS库生成凸包
     * 
     * @param points 聚集区坐标点数组
     * @return 凸包坐标点列表（逆时针顺序），如果点数不足则返回原点列表
     */
    public static List<CoordinatePoint> generateConvexHull(CoordinatePoint[] points) {
        if (points == null || points.length == 0) {
            return new ArrayList<>();
        }
        List<CoordinatePoint> uniquePoints = removeDuplicates(points);
        if (uniquePoints.size() < 3) {
            return new ArrayList<>(uniquePoints);
        }
        // 使用JTS生成凸包
        GeometryFactory gf = new GeometryFactory();
        Coordinate[] jtsCoords = uniquePoints.stream()
                .map(p -> new Coordinate(p.getLongitude(), p.getLatitude()))
                .toArray(Coordinate[]::new);
        ConvexHull hull = new ConvexHull(jtsCoords, gf);
        Polygon poly = (Polygon) hull.getConvexHull();
        Coordinate[] coords = poly.getExteriorRing().getCoordinates();
        List<CoordinatePoint> result = new ArrayList<>();
        for (Coordinate c : coords) {
            result.add(CoordinatePoint.builder().longitude(c.x).latitude(c.y).build());
        }
        return result;
    }
    
    /**
     * 从聚集区列表生成凸包
     * 
     * @param accumulations 聚集区列表
     * @return 凸包坐标点列表
     */
    public static List<CoordinatePoint> generateConvexHull(List<Accumulation> accumulations) {
        if (accumulations == null || accumulations.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取有效的坐标点
        List<CoordinatePoint> points = new ArrayList<>();
        for (Accumulation acc : accumulations) {
            if (acc != null && acc.isValid()) {
                points.add(acc.getCoordinate());
            }
        }
        
        return generateConvexHull(points.toArray(new CoordinatePoint[0]));
    }
    
    /**
     * Jarvis步进算法实现
     * 
     * @param points 去重后的坐标点列表
     * @return 凸包顶点列表
     */
    private static List<CoordinatePoint> jarvisMarch(List<CoordinatePoint> points) {
        int n = points.size();
        if (n < 3) {
            return new ArrayList<>(points);
        }
        
        List<CoordinatePoint> hull = new ArrayList<>();
        
        // 1. 找到最左边的点作为起点
        int leftMost = findLeftMostPoint(points);
        
        int p = leftMost, q;
        do {
            // 将当前点加入凸包
            hull.add(points.get(p));
            
            // 寻找下一个凸包点
            q = (p + 1) % n;
            
            for (int i = 0; i < n; i++) {
                // 如果点i比当前选择的点q更"左"（逆时针方向），则选择点i
                if (orientation(points.get(p), points.get(i), points.get(q)) == 2) {
                    q = i;
                }
            }
            
            p = q;
            
        } while (p != leftMost); // 直到回到起点
        
        return hull;
    }
    
    /**
     * 找到最左边的点的索引
     * 
     * @param points 坐标点列表
     * @return 最左边点的索引
     */
    private static int findLeftMostPoint(List<CoordinatePoint> points) {
        int leftMost = 0;
        for (int i = 1; i < points.size(); i++) {
            CoordinatePoint current = points.get(i);
            CoordinatePoint leftMostPoint = points.get(leftMost);
            
            if (current.getLongitude() < leftMostPoint.getLongitude() ||
                (Math.abs(current.getLongitude() - leftMostPoint.getLongitude()) < 1e-9 &&
                 current.getLatitude() < leftMostPoint.getLatitude())) {
                leftMost = i;
            }
        }
        return leftMost;
    }
    
    /**
     * 判断三个点的方向关系
     * 
     * @param p 第一个点
     * @param q 第二个点  
     * @param r 第三个点
     * @return 0: 共线, 1: 顺时针, 2: 逆时针
     */
    private static int orientation(CoordinatePoint p, CoordinatePoint q, CoordinatePoint r) {
        // 计算向量叉积：(q-p) × (r-q)
        double val = (q.getLatitude() - p.getLatitude()) * (r.getLongitude() - q.getLongitude()) -
                     (q.getLongitude() - p.getLongitude()) * (r.getLatitude() - q.getLatitude());
        
        if (Math.abs(val) < 1e-9) {
            return 0; // 共线
        }
        
        return (val > 0) ? 1 : 2; // 顺时针或逆时针
    }
    
    /**
     * 去除重复的坐标点
     * 
     * @param points 原始坐标点数组
     * @return 去重后的坐标点列表
     */
    private static List<CoordinatePoint> removeDuplicates(CoordinatePoint[] points) {
        List<CoordinatePoint> uniquePoints = new ArrayList<>();
        
        for (CoordinatePoint point : points) {
            if (point != null && point.isValid()) {
                boolean isDuplicate = false;
                for (CoordinatePoint existing : uniquePoints) {
                    if (point.equals(existing)) {
                        isDuplicate = true;
                        break;
                    }
                }
                if (!isDuplicate) {
                    uniquePoints.add(point);
                }
            }
        }
        
        return uniquePoints;
    }
    
    /**
     * 检测两个凸包是否重叠（JTS实现）
     */
    public static boolean isConvexHullsOverlapping(List<CoordinatePoint> hull1, List<CoordinatePoint> hull2) {
        if (hull1 == null || hull1.size() < 3 || hull2 == null || hull2.size() < 3) return false;
        GeometryFactory gf = new GeometryFactory();
        Coordinate[] coords1 = hull1.stream().map(p -> new Coordinate(p.getLongitude(), p.getLatitude())).toArray(Coordinate[]::new);
        Coordinate[] coords2 = hull2.stream().map(p -> new Coordinate(p.getLongitude(), p.getLatitude())).toArray(Coordinate[]::new);
        LinearRing ring1 = gf.createLinearRing(coords1);
        LinearRing ring2 = gf.createLinearRing(coords2);
        Polygon poly1 = gf.createPolygon(ring1, null);
        Polygon poly2 = gf.createPolygon(ring2, null);
        return poly1.intersects(poly2);
    }
    
    /**
     * 检查点是否在凸包内（JTS实现）
     */
    public static boolean isPointInConvexHull(CoordinatePoint point, List<CoordinatePoint> hull) {
        if (point == null || hull == null || hull.size() < 3) return false;
        GeometryFactory gf = new GeometryFactory();
        Coordinate[] coords = hull.stream().map(p -> new Coordinate(p.getLongitude(), p.getLatitude())).toArray(Coordinate[]::new);
        LinearRing ring = gf.createLinearRing(coords);
        Polygon poly = gf.createPolygon(ring, null);
        Coordinate c = new Coordinate(point.getLongitude(), point.getLatitude());
        return poly.contains(gf.createPoint(c));
    }

    private static boolean isRayIntersectingEdge(CoordinatePoint point, 
                                               CoordinatePoint edgeStart, 
                                               CoordinatePoint edgeEnd) {
        // NOTE: 当前为水平射线与线段相交的简化实现，复杂多边形建议用JTS等库
        double px = point.getLongitude();
        double py = point.getLatitude();
        double sx = edgeStart.getLongitude();
        double sy = edgeStart.getLatitude();
        double ex = edgeEnd.getLongitude();
        double ey = edgeEnd.getLatitude();
        if ((sy > py) == (ey > py)) {
            return false;
        }
        double intersectionX = sx + (py - sy) / (ey - sy) * (ex - sx);
        return intersectionX > px;
    }

    /**
     * 调整凸包以消除重叠
     * 当检测到凸包重叠时，通过微调凸包边界来消除重叠
     * 
     * @param hull1 第一个凸包
     * @param hull2 第二个凸包
     * @return 调整后的凸包对，[0]为调整后的hull1，[1]为调整后的hull2
     */
    public static List<CoordinatePoint>[] adjustOverlappingHulls(List<CoordinatePoint> hull1, 
                                                                 List<CoordinatePoint> hull2) {
        // OPTIMIZE: 这里只是返回原凸包，实际可用JTS等库做更精确的凸包调整
        @SuppressWarnings("unchecked")
        List<CoordinatePoint>[] result = new List[2];
        result[0] = new ArrayList<>(hull1);
        result[1] = new ArrayList<>(hull2);
        return result;
    }
    
    /**
     * 验证凸包的有效性（JTS实现）
     */
    public static boolean isValidConvexHull(List<CoordinatePoint> hull) {
        if (hull == null || hull.size() < 3) return false;
        GeometryFactory gf = new GeometryFactory();
        Coordinate[] coords = hull.stream().map(p -> new Coordinate(p.getLongitude(), p.getLatitude())).toArray(Coordinate[]::new);
        LinearRing ring = gf.createLinearRing(coords);
        Polygon poly = gf.createPolygon(ring, null);
        return poly.isValid();
    }
    
    /**
     * 检查多边形是否为凸包
     * 
     * @param points 多边形顶点
     * @return 是否为凸包
     */
    private static boolean isConvex(List<CoordinatePoint> points) {
        // NOTE: 当前为简单凸性检查，复杂场景建议用JTS等库
        int n = points.size();
        if (n < 3) {
            return false;
        }
        int sign = 0;
        for (int i = 0; i < n; i++) {
            CoordinatePoint p1 = points.get(i);
            CoordinatePoint p2 = points.get((i + 1) % n);
            CoordinatePoint p3 = points.get((i + 2) % n);
            int orientation = orientation(p1, p2, p3);
            if (orientation != 0) {
                if (sign == 0) {
                    sign = orientation;
                } else if (sign != orientation) {
                    return false;
                }
            }
        }
        return true;
    }
} 