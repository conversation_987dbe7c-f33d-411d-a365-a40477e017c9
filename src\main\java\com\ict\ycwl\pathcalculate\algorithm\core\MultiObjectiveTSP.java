package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 多目标TSP优化框架
 * 支持不同业务场景的多目标优化策略
 */
@Slf4j
@Component
public class MultiObjectiveTSP {
    
    /**
     * 优化目标枚举
     */
    public enum OptimizationGoal {
        TIME_FIRST("时间优先", "最小化总配送时间"),
        DISTANCE_FIRST("距离优先", "最小化总行驶距离"),
        FUEL_FIRST("燃油优先", "最小化燃油消耗"),
        COST_FIRST("成本优先", "最小化总运营成本"),
        BALANCED("平衡优化", "多因素权衡优化"),
        EFFICIENCY_FIRST("效率优先", "最大化配送效率"),
        GREEN_FIRST("绿色优先", "最小化碳排放");
        
        private final String displayName;
        private final String description;
        
        OptimizationGoal(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 路线评估指标类
     */
    public static class RouteMetrics {
        public double totalTime;           // 总时间（分钟）
        public double totalDistance;       // 总距离（公里）
        public double fuelConsumption;     // 燃油消耗（升）
        public double totalCost;           // 总成本（元）
        public double carbonEmission;      // 碳排放（kg CO2）
        public double efficiency;          // 配送效率（点/小时）
        public double routeComplexity;     // 路线复杂度
        public int trafficLights;          // 交通灯数量
        public double avgSpeed;            // 平均速度（km/h）
        public double workloadBalance;     // 工作负载平衡度
        
        // 归一化指标（0-1之间）
        public double normalizedTime;
        public double normalizedDistance;
        public double normalizedFuel;
        public double normalizedCost;
        public double normalizedEmission;
        public double normalizedEfficiency;
        
        @Override
        public String toString() {
            return String.format(
                "RouteMetrics{time=%.2f, distance=%.2f, fuel=%.2f, cost=%.2f, efficiency=%.2f}",
                totalTime, totalDistance, fuelConsumption, totalCost, efficiency
            );
        }
    }
    
    /**
     * 目标权重配置
     */
    public static class ObjectiveWeights {
        public double timeWeight = 0.3;      // 时间权重
        public double distanceWeight = 0.2;  // 距离权重
        public double fuelWeight = 0.2;      // 燃油权重
        public double costWeight = 0.15;     // 成本权重
        public double efficiencyWeight = 0.1; // 效率权重
        public double emissionWeight = 0.05; // 排放权重
        
        public static ObjectiveWeights forGoal(OptimizationGoal goal) {
            ObjectiveWeights weights = new ObjectiveWeights();
            
            switch (goal) {
                case TIME_FIRST:
                    weights.timeWeight = 0.7;
                    weights.distanceWeight = 0.15;
                    weights.fuelWeight = 0.1;
                    weights.costWeight = 0.05;
                    break;
                    
                case DISTANCE_FIRST:
                    weights.distanceWeight = 0.6;
                    weights.timeWeight = 0.2;
                    weights.fuelWeight = 0.15;
                    weights.costWeight = 0.05;
                    break;
                    
                case FUEL_FIRST:
                    weights.fuelWeight = 0.5;
                    weights.distanceWeight = 0.3;
                    weights.timeWeight = 0.15;
                    weights.costWeight = 0.05;
                    break;
                    
                case COST_FIRST:
                    weights.costWeight = 0.5;
                    weights.timeWeight = 0.2;
                    weights.fuelWeight = 0.2;
                    weights.distanceWeight = 0.1;
                    break;
                    
                case EFFICIENCY_FIRST:
                    weights.efficiencyWeight = 0.4;
                    weights.timeWeight = 0.3;
                    weights.distanceWeight = 0.2;
                    weights.costWeight = 0.1;
                    break;
                    
                case GREEN_FIRST:
                    weights.emissionWeight = 0.4;
                    weights.fuelWeight = 0.3;
                    weights.distanceWeight = 0.2;
                    weights.timeWeight = 0.1;
                    break;
                    
                case BALANCED:
                default:
                    // 使用默认的平衡权重
                    break;
            }
            
            return weights;
        }
    }
    
    // 成本计算参数
    private static final double TIME_COST_FACTOR = 2.0;      // 2元/分钟
    private static final double FUEL_COST_FACTOR = 7.5;      // 7.5元/升
    private static final double DISTANCE_COST_FACTOR = 0.8;  // 0.8元/公里
    private static final double VEHICLE_COST_PER_HOUR = 50.0; // 50元/小时车辆成本
    
    // 环境参数
    private static final double CO2_PER_LITER = 2.31;        // 2.31 kg CO2/升
    private static final double FUEL_CONSUMPTION_RATE = 0.08; // 0.08升/公里
    
    private final TSPSolver orToolsSolver;
    private final EnhancedGeneticTSP geneticSolver;
    private final BranchAndBoundTSP branchBoundSolver;
    
    /**
     * 无参构造器 - 用于向后兼容
     */
    public MultiObjectiveTSP() {
        this.geneticSolver = new EnhancedGeneticTSP();
        this.branchBoundSolver = new BranchAndBoundTSP();
        this.orToolsSolver = new SafeORToolsTSP(geneticSolver);
    }
    
    /**
     * 依赖注入构造器 - 用于Spring环境
     */
    public MultiObjectiveTSP(TSPSolver orToolsSolver, EnhancedGeneticTSP geneticSolver, 
                           BranchAndBoundTSP branchBoundSolver) {
        this.orToolsSolver = orToolsSolver;
        this.geneticSolver = geneticSolver;
        this.branchBoundSolver = branchBoundSolver;
    }
    
    /**
     * 多目标TSP求解
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix, OptimizationGoal goal) {
        
        log.debug("开始多目标TSP求解，目标: {}, 节点数: {}", goal.getDisplayName(), cluster.size());
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        // 根据目标选择最佳求解策略
        List<List<Long>> candidates = generateCandidateSolutions(depot, cluster, timeMatrix, goal);
        
        // 评估所有候选解
        List<SolutionEvaluation> evaluations = new ArrayList<>();
        ObjectiveWeights weights = ObjectiveWeights.forGoal(goal);
        
        for (List<Long> solution : candidates) {
            RouteMetrics metrics = calculateRouteMetrics(solution, depot, cluster, timeMatrix);
            double score = calculateObjectiveScore(metrics, weights);
            evaluations.add(new SolutionEvaluation(solution, metrics, score));
        }
        
        // 归一化处理
        normalizeMetrics(evaluations);
        
        // 重新计算归一化后的分数
        for (SolutionEvaluation eval : evaluations) {
            eval.score = calculateNormalizedScore(eval.metrics, weights);
        }
        
        // 选择最优解
        SolutionEvaluation best = evaluations.stream()
                .min(Comparator.comparingDouble(e -> e.score))
                .orElse(evaluations.get(0));
        
        log.debug("多目标优化完成，最优解: {}, 目标: {}", best.metrics, goal.getDisplayName());
        
        return best.solution;
    }
    
    /**
     * 生成候选解
     */
    private List<List<Long>> generateCandidateSolutions(TransitDepot depot, List<Accumulation> cluster, 
                                                       Map<String, TimeInfo> timeMatrix, OptimizationGoal goal) {
        
        List<List<Long>> candidates = new ArrayList<>();
        
        try {
            // 策略1：OR-Tools求解（如果可用）
            if (orToolsSolver.isORToolsAvailable() && cluster.size() <= 100) {
                List<Long> orToolsSolution = orToolsSolver.solve(depot, cluster, timeMatrix, 30000);
                if (!orToolsSolution.isEmpty()) {
                    candidates.add(orToolsSolution);
                }
            }
            
            // 策略2：分支定界（中等规模）
            if (cluster.size() <= 20) {
                List<Long> branchBoundSolution = branchBoundSolver.solve(depot, cluster, timeMatrix, 20000);
                if (!branchBoundSolution.isEmpty()) {
                    candidates.add(branchBoundSolution);
                }
            }
            
            // 策略3：遗传算法（大规模）
            if (cluster.size() > 10) {
                List<Long> geneticSolution = geneticSolver.solve(depot, cluster, timeMatrix);
                if (!geneticSolution.isEmpty()) {
                    candidates.add(geneticSolution);
                }
            }
            
            // 策略4：针对特定目标的启发式算法
            candidates.addAll(generateTargetSpecificSolutions(depot, cluster, timeMatrix, goal));
            
        } catch (Exception e) {
            log.error("生成候选解时出错: {}", e.getMessage());
        }
        
        // 如果没有候选解，使用简单贪心算法
        if (candidates.isEmpty()) {
            candidates.add(greedySolution(depot, cluster, timeMatrix));
        }
        
        return candidates;
    }
    
    /**
     * 生成针对特定目标的启发式解
     */
    private List<List<Long>> generateTargetSpecificSolutions(TransitDepot depot, List<Accumulation> cluster, 
                                                            Map<String, TimeInfo> timeMatrix, OptimizationGoal goal) {
        
        List<List<Long>> solutions = new ArrayList<>();
        
        switch (goal) {
            case TIME_FIRST:
                // 时间优先：选择最快路径
                solutions.add(timeFocusedGreedy(depot, cluster, timeMatrix));
                break;
                
            case DISTANCE_FIRST:
                // 距离优先：选择最短路径
                solutions.add(distanceFocusedGreedy(depot, cluster, timeMatrix));
                break;
                
            case FUEL_FIRST:
                // 燃油优先：最小化燃油消耗
                solutions.add(fuelFocusedGreedy(depot, cluster, timeMatrix));
                break;
                
            case EFFICIENCY_FIRST:
                // 效率优先：最大化配送密度
                solutions.add(efficiencyFocusedGreedy(depot, cluster, timeMatrix));
                break;
                
            case GREEN_FIRST:
                // 绿色优先：最小化碳排放
                solutions.add(emissionFocusedGreedy(depot, cluster, timeMatrix));
                break;
                
            case BALANCED:
            default:
                // 平衡策略：多种贪心算法
                solutions.add(balancedGreedy(depot, cluster, timeMatrix));
                solutions.add(nearestNeighborFromCenter(depot, cluster, timeMatrix));
                break;
        }
        
        return solutions;
    }
    
    /**
     * 时间优先贪心算法
     */
    private List<Long> timeFocusedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                        Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation fastest = null;
            double minTime = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                // 时间优先：只考虑行驶时间，忽略配送时间
                if (travelTime < minTime) {
                    minTime = travelTime;
                    fastest = acc;
                }
            }
            
            if (fastest != null) {
                sequence.add(fastest.getAccumulationId());
                visited.add(fastest.getAccumulationId());
                currentPos = fastest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 距离优先贪心算法
     */
    private List<Long> distanceFocusedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                           Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minDistance = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                // 使用欧几里得距离作为距离的近似
                double distance = calculateEuclideanDistance(currentPos, acc.getCoordinate());
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 燃油优先贪心算法
     */
    private List<Long> fuelFocusedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                        Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation best = null;
            double minFuelConsumption = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                TimeInfo timeInfo = getTimeInfo(currentPos, acc.getCoordinate(), timeMatrix);
                double distance = timeInfo != null ? timeInfo.getDistance() : 
                                calculateEuclideanDistance(currentPos, acc.getCoordinate());
                
                // 燃油消耗主要由距离决定，但也考虑交通状况
                double fuelConsumption = distance * FUEL_CONSUMPTION_RATE;
                if (timeInfo != null && timeInfo.getAverageSpeed() < 30.0) {
                    fuelConsumption *= 1.2; // 低速拥堵时燃油消耗增加20%
                }
                
                if (fuelConsumption < minFuelConsumption) {
                    minFuelConsumption = fuelConsumption;
                    best = acc;
                }
            }
            
            if (best != null) {
                sequence.add(best.getAccumulationId());
                visited.add(best.getAccumulationId());
                currentPos = best.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 效率优先贪心算法
     */
    private List<Long> efficiencyFocusedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                             Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation best = null;
            double maxEfficiency = Double.MIN_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double totalTime = travelTime + acc.getDeliveryTime();
                
                // 效率 = 工作量 / 总时间
                double efficiency = acc.getDeliveryTime() / Math.max(totalTime, 1.0);
                
                if (efficiency > maxEfficiency) {
                    maxEfficiency = efficiency;
                    best = acc;
                }
            }
            
            if (best != null) {
                sequence.add(best.getAccumulationId());
                visited.add(best.getAccumulationId());
                currentPos = best.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 碳排放优先贪心算法
     */
    private List<Long> emissionFocusedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                           Map<String, TimeInfo> timeMatrix) {
        
        // 碳排放主要与燃油消耗相关，因此复用燃油优先算法
        return fuelFocusedGreedy(depot, cluster, timeMatrix);
    }
    
    /**
     * 平衡贪心算法
     */
    private List<Long> balancedGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                    Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation best = null;
            double minScore = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double distance = calculateEuclideanDistance(currentPos, acc.getCoordinate());
                
                // 平衡评分：时间40% + 距离30% + 配送时间权重30%
                double score = 0.4 * travelTime + 0.3 * distance + 0.3 * acc.getDeliveryTime();
                
                if (score < minScore) {
                    minScore = score;
                    best = acc;
                }
            }
            
            if (best != null) {
                sequence.add(best.getAccumulationId());
                visited.add(best.getAccumulationId());
                currentPos = best.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 从中心点开始的最近邻算法
     */
    private List<Long> nearestNeighborFromCenter(TransitDepot depot, List<Accumulation> cluster, 
                                               Map<String, TimeInfo> timeMatrix) {
        
        // 计算几何中心
        double centerLat = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLatitude()).average().orElse(0.0);
        double centerLon = cluster.stream().mapToDouble(acc -> acc.getCoordinate().getLongitude()).average().orElse(0.0);
        CoordinatePoint center = new CoordinatePoint(centerLon, centerLat);
        
        // 找到距离中心最近的点作为起点
        Accumulation startPoint = cluster.stream()
                .min(Comparator.comparingDouble(acc -> 
                    calculateEuclideanDistance(center, acc.getCoordinate())))
                .orElse(cluster.get(0));
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = startPoint.getCoordinate();
        
        sequence.add(startPoint.getAccumulationId());
        visited.add(startPoint.getAccumulationId());
        
        // 从起点开始最近邻搜索
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minDistance = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double distance = calculateEuclideanDistance(currentPos, acc.getCoordinate());
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 简单贪心算法（备用）
     */
    private List<Long> greedySolution(TransitDepot depot, List<Accumulation> cluster, 
                                    Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minCost = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double totalCost = travelTime + acc.getDeliveryTime();
                
                if (totalCost < minCost) {
                    minCost = totalCost;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 计算路线指标
     */
    public RouteMetrics calculateRouteMetrics(List<Long> sequence, TransitDepot depot, 
                                            List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix) {
        
        RouteMetrics metrics = new RouteMetrics();
        
        if (sequence.isEmpty()) {
            metrics.totalTime = AlgorithmParameters.LOADING_TIME_MINUTES;
            return metrics;
        }
        
        metrics.totalTime = AlgorithmParameters.LOADING_TIME_MINUTES;
        CoordinatePoint currentPos = depot.getCoordinate();
        
        for (Long accId : sequence) {
            Accumulation acc = cluster.stream()
                    .filter(a -> a.getAccumulationId().equals(accId))
                    .findFirst()
                    .orElse(null);
            
            if (acc != null) {
                TimeInfo timeInfo = getTimeInfo(currentPos, acc.getCoordinate(), timeMatrix);
                
                // 时间计算
                double travelTime = timeInfo != null ? timeInfo.getTravelTime() : 
                                  estimateTravelTime(currentPos, acc.getCoordinate());
                metrics.totalTime += travelTime;
                metrics.totalTime += acc.getDeliveryTime();
                
                // 距离计算
                double distance = timeInfo != null ? timeInfo.getDistance() : 
                                calculateEuclideanDistance(currentPos, acc.getCoordinate());
                metrics.totalDistance += distance;
                
                // 燃油消耗计算
                double segmentFuel = distance * FUEL_CONSUMPTION_RATE;
                if (timeInfo != null && timeInfo.getAverageSpeed() < 30.0) {
                    segmentFuel *= 1.2; // 低速拥堵增加20%燃油消耗
                }
                metrics.fuelConsumption += segmentFuel;
                
                // 交通灯数量（估算）
                metrics.trafficLights += (int) (distance * 2); // 平均每公里2个交通灯
                
                currentPos = acc.getCoordinate();
            }
        }
        
        // 回到起点
        TimeInfo returnInfo = getTimeInfo(currentPos, depot.getCoordinate(), timeMatrix);
        double returnTime = returnInfo != null ? returnInfo.getTravelTime() : 
                          estimateTravelTime(currentPos, depot.getCoordinate());
        double returnDistance = returnInfo != null ? returnInfo.getDistance() : 
                              calculateEuclideanDistance(currentPos, depot.getCoordinate());
        
        metrics.totalTime += returnTime;
        metrics.totalDistance += returnDistance;
        metrics.fuelConsumption += returnDistance * FUEL_CONSUMPTION_RATE;
        
        // 计算派生指标
        metrics.carbonEmission = metrics.fuelConsumption * CO2_PER_LITER;
        metrics.avgSpeed = metrics.totalDistance / (metrics.totalTime / 60.0); // km/h
        metrics.efficiency = sequence.size() / (metrics.totalTime / 60.0); // 点/小时
        
        // 成本计算
        metrics.totalCost = metrics.totalTime * TIME_COST_FACTOR + 
                          metrics.fuelConsumption * FUEL_COST_FACTOR + 
                          metrics.totalDistance * DISTANCE_COST_FACTOR +
                          (metrics.totalTime / 60.0) * VEHICLE_COST_PER_HOUR;
        
        // 路线复杂度（转弯次数估算）
        metrics.routeComplexity = calculateRouteComplexity(sequence, cluster);
        
        // 工作负载平衡度
        metrics.workloadBalance = calculateWorkloadBalance(sequence, cluster);
        
        return metrics;
    }
    
    /**
     * 计算目标分数
     */
    private double calculateObjectiveScore(RouteMetrics metrics, ObjectiveWeights weights) {
        return weights.timeWeight * metrics.totalTime +
               weights.distanceWeight * metrics.totalDistance +
               weights.fuelWeight * metrics.fuelConsumption +
               weights.costWeight * metrics.totalCost +
               weights.efficiencyWeight * (1.0 / Math.max(metrics.efficiency, 0.1)) +
               weights.emissionWeight * metrics.carbonEmission;
    }
    
    /**
     * 计算归一化分数
     */
    private double calculateNormalizedScore(RouteMetrics metrics, ObjectiveWeights weights) {
        return weights.timeWeight * metrics.normalizedTime +
               weights.distanceWeight * metrics.normalizedDistance +
               weights.fuelWeight * metrics.normalizedFuel +
               weights.costWeight * metrics.normalizedCost +
               weights.efficiencyWeight * (1.0 - metrics.normalizedEfficiency) +
               weights.emissionWeight * metrics.normalizedEmission;
    }
    
    /**
     * 归一化指标
     */
    private void normalizeMetrics(List<SolutionEvaluation> evaluations) {
        if (evaluations.isEmpty()) return;
        
        // 找到各指标的最大最小值
        double minTime = evaluations.stream().mapToDouble(e -> e.metrics.totalTime).min().orElse(0.0);
        double maxTime = evaluations.stream().mapToDouble(e -> e.metrics.totalTime).max().orElse(1.0);
        
        double minDistance = evaluations.stream().mapToDouble(e -> e.metrics.totalDistance).min().orElse(0.0);
        double maxDistance = evaluations.stream().mapToDouble(e -> e.metrics.totalDistance).max().orElse(1.0);
        
        double minFuel = evaluations.stream().mapToDouble(e -> e.metrics.fuelConsumption).min().orElse(0.0);
        double maxFuel = evaluations.stream().mapToDouble(e -> e.metrics.fuelConsumption).max().orElse(1.0);
        
        double minCost = evaluations.stream().mapToDouble(e -> e.metrics.totalCost).min().orElse(0.0);
        double maxCost = evaluations.stream().mapToDouble(e -> e.metrics.totalCost).max().orElse(1.0);
        
        double minEfficiency = evaluations.stream().mapToDouble(e -> e.metrics.efficiency).min().orElse(0.0);
        double maxEfficiency = evaluations.stream().mapToDouble(e -> e.metrics.efficiency).max().orElse(1.0);
        
        double minEmission = evaluations.stream().mapToDouble(e -> e.metrics.carbonEmission).min().orElse(0.0);
        double maxEmission = evaluations.stream().mapToDouble(e -> e.metrics.carbonEmission).max().orElse(1.0);
        
        // 归一化
        for (SolutionEvaluation eval : evaluations) {
            RouteMetrics m = eval.metrics;
            
            m.normalizedTime = normalize(m.totalTime, minTime, maxTime);
            m.normalizedDistance = normalize(m.totalDistance, minDistance, maxDistance);
            m.normalizedFuel = normalize(m.fuelConsumption, minFuel, maxFuel);
            m.normalizedCost = normalize(m.totalCost, minCost, maxCost);
            m.normalizedEfficiency = normalize(m.efficiency, minEfficiency, maxEfficiency);
            m.normalizedEmission = normalize(m.carbonEmission, minEmission, maxEmission);
        }
    }
    
    /**
     * 归一化函数
     */
    private double normalize(double value, double min, double max) {
        if (max == min) return 0.0;
        return (value - min) / (max - min);
    }
    
    /**
     * 计算路线复杂度
     */
    private double calculateRouteComplexity(List<Long> sequence, List<Accumulation> cluster) {
        if (sequence.size() < 3) return 0.0;
        
        double complexity = 0.0;
        
        for (int i = 1; i < sequence.size() - 1; i++) {
            // 计算转弯角度
            CoordinatePoint prev = getAccumulationById(sequence.get(i - 1), cluster).getCoordinate();
            CoordinatePoint curr = getAccumulationById(sequence.get(i), cluster).getCoordinate();
            CoordinatePoint next = getAccumulationById(sequence.get(i + 1), cluster).getCoordinate();
            
            double angle = calculateTurnAngle(prev, curr, next);
            complexity += Math.abs(angle - 180.0) / 180.0; // 归一化到0-1
        }
        
        return complexity / Math.max(sequence.size() - 2, 1);
    }
    
    /**
     * 计算工作负载平衡度
     */
    private double calculateWorkloadBalance(List<Long> sequence, List<Accumulation> cluster) {
        if (sequence.isEmpty()) return 1.0;
        
        List<Double> workloads = sequence.stream()
                .map(id -> getAccumulationById(id, cluster).getDeliveryTime())
                .collect(Collectors.toList());
        
        double mean = workloads.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = workloads.stream()
                .mapToDouble(w -> Math.pow(w - mean, 2))
                .average().orElse(0.0);
        
        // 返回标准化的平衡度（越接近1越平衡）
        return 1.0 / (1.0 + Math.sqrt(variance) / Math.max(mean, 1.0));
    }
    
    // 辅助类：解评估
    private static class SolutionEvaluation {
        List<Long> solution;
        RouteMetrics metrics;
        double score;
        
        SolutionEvaluation(List<Long> solution, RouteMetrics metrics, double score) {
            this.solution = solution;
            this.metrics = metrics;
            this.score = score;
        }
    }
    
    // 辅助方法
    private Accumulation getAccumulationById(Long id, List<Accumulation> cluster) {
        return cluster.stream()
                .filter(acc -> acc.getAccumulationId().equals(id))
                .findFirst()
                .orElse(null);
    }
    
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 
               estimateTravelTime(from, to);
    }
    
    private TimeInfo getTimeInfo(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        return timeMatrix.get(key);
    }
    
    private double estimateTravelTime(CoordinatePoint from, CoordinatePoint to) {
        double distance = calculateEuclideanDistance(from, to);
        return distance / 50.0 * 60.0; // 假设平均速度50km/h，转换为分钟
    }
    
    private double calculateEuclideanDistance(CoordinatePoint p1, CoordinatePoint p2) {
        double dLat = Math.toRadians(p2.getLatitude() - p1.getLatitude());
        double dLon = Math.toRadians(p2.getLongitude() - p1.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.getLatitude())) * Math.cos(Math.toRadians(p2.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return 6371.0 * c; // 地球半径6371km
    }
    
    private double calculateTurnAngle(CoordinatePoint p1, CoordinatePoint p2, CoordinatePoint p3) {
        // 简化的转弯角度计算
        double dx1 = p2.getLongitude() - p1.getLongitude();
        double dy1 = p2.getLatitude() - p1.getLatitude();
        double dx2 = p3.getLongitude() - p2.getLongitude();
        double dy2 = p3.getLatitude() - p2.getLatitude();
        
        double angle1 = Math.atan2(dy1, dx1);
        double angle2 = Math.atan2(dy2, dx2);
        double turnAngle = Math.abs(Math.toDegrees(angle2 - angle1));
        
        return turnAngle > 180 ? 360 - turnAngle : turnAngle;
    }
}