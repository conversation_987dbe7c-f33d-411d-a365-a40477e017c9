package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 聚类质量评估器
 * 用于评估聚类算法的效果和质量
 */
@Slf4j
@Component
public class ClusteringQualityEvaluator {
    
    /**
     * 评估聚类质量
     */
    public ClusteringQualityMetrics evaluateQuality(
            List<List<Accumulation>> clusters, 
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        ClusteringQualityMetrics metrics = new ClusteringQualityMetrics();
        
        // 1. 路径交叉指数
        metrics.setPathCrossingIndex(calculatePathCrossingIndex(clusters, depot));
        
        // 2. 时间均衡指数
        metrics.setTimeBalanceIndex(calculateTimeBalanceIndex(clusters));
        
        // 3. 地理紧凑性指数
        metrics.setGeographicCompactnessIndex(calculateGeographicCompactnessIndex(clusters, depot));
        
        // 4. 服务半径违反率
        metrics.setServiceRadiusViolationRate(calculateServiceRadiusViolationRate(clusters, depot));
        
        // 5. 配送效率指数
        metrics.setDeliveryEfficiencyIndex(calculateDeliveryEfficiencyIndex(clusters, depot, timeMatrix));
        
        // 6. 综合质量分数
        metrics.setOverallQualityScore(calculateOverallQualityScore(metrics));
        
        return metrics;
    }
    
    /**
     * 计算路径交叉指数 (0-1, 越小越好)
     */
    private double calculatePathCrossingIndex(List<List<Accumulation>> clusters, TransitDepot depot) {
        int totalCrossings = 0;
        int totalPairs = 0;
        
        for (int i = 0; i < clusters.size(); i++) {
            for (int j = i + 1; j < clusters.size(); j++) {
                List<Accumulation> cluster1 = clusters.get(i);
                List<Accumulation> cluster2 = clusters.get(j);
                
                for (Accumulation acc1 : cluster1) {
                    for (Accumulation acc2 : cluster2) {
                        totalPairs++;
                        if (pathsCross(depot, acc1, acc2)) {
                            totalCrossings++;
                        }
                    }
                }
            }
        }
        
        return totalPairs > 0 ? (double) totalCrossings / totalPairs : 0.0;
    }
    
    /**
     * 计算时间均衡指数 (0-1, 越大越好)
     */
    private double calculateTimeBalanceIndex(List<List<Accumulation>> clusters) {
        List<Double> workloads = new ArrayList<>();
        for (List<Accumulation> cluster : clusters) {
            double workload = cluster.stream()
                    .mapToDouble(Accumulation::getDeliveryTime)
                    .sum();
            workloads.add(workload);
        }
        
        if (workloads.isEmpty()) return 1.0;
        
        double mean = workloads.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = workloads.stream()
                .mapToDouble(w -> Math.pow(w - mean, 2))
                .average()
                .orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        // 标准差越小，均衡指数越大
        double cv = mean > 0 ? stdDev / mean : 0.0; // 变异系数
        return Math.max(0.0, 1.0 - cv);
    }
    
    /**
     * 计算地理紧凑性指数 (0-1, 越大越好)
     */
    private double calculateGeographicCompactnessIndex(List<List<Accumulation>> clusters, TransitDepot depot) {
        double totalCompactness = 0.0;
        int validClusters = 0;
        
        for (List<Accumulation> cluster : clusters) {
            if (cluster.size() > 1) {
                double compactness = calculateClusterCompactness(cluster, depot);
                totalCompactness += compactness;
                validClusters++;
            }
        }
        
        return validClusters > 0 ? totalCompactness / validClusters : 1.0;
    }
    
    /**
     * 计算服务半径违反率 (0-1, 越小越好)
     */
    private double calculateServiceRadiusViolationRate(List<List<Accumulation>> clusters, TransitDepot depot) {
        int totalAccumulations = 0;
        int violations = 0;
        
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation acc : cluster) {
                totalAccumulations++;
                double distance = calculateDistance(
                        acc.getLongitude(), acc.getLatitude(),
                        depot.getLongitude(), depot.getLatitude());
                if (distance > AlgorithmParameters.MAX_SERVICE_RADIUS) {
                    violations++;
                }
            }
        }
        
        return totalAccumulations > 0 ? (double) violations / totalAccumulations : 0.0;
    }
    
    /**
     * 计算配送效率指数 (0-1, 越大越好)
     */
    private double calculateDeliveryEfficiencyIndex(
            List<List<Accumulation>> clusters, 
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double totalEfficiency = 0.0;
        int validClusters = 0;
        
        for (List<Accumulation> cluster : clusters) {
            if (!cluster.isEmpty()) {
                double efficiency = calculateClusterEfficiency(cluster, depot, timeMatrix);
                totalEfficiency += efficiency;
                validClusters++;
            }
        }
        
        return validClusters > 0 ? totalEfficiency / validClusters : 1.0;
    }
    
    /**
     * 计算综合质量分数 (0-1, 越大越好)
     */
    private double calculateOverallQualityScore(ClusteringQualityMetrics metrics) {
        // 权重配置
        double pathCrossingWeight = 0.3;
        double timeBalanceWeight = 0.2;
        double compactnessWeight = 0.2;
        double radiusWeight = 0.15;
        double efficiencyWeight = 0.15;
        
        return (1.0 - metrics.getPathCrossingIndex()) * pathCrossingWeight +
               metrics.getTimeBalanceIndex() * timeBalanceWeight +
               metrics.getGeographicCompactnessIndex() * compactnessWeight +
               (1.0 - metrics.getServiceRadiusViolationRate()) * radiusWeight +
               metrics.getDeliveryEfficiencyIndex() * efficiencyWeight;
    }
    
    /**
     * 检测路径是否交叉
     */
    private boolean pathsCross(TransitDepot depot, Accumulation acc1, Accumulation acc2) {
        // 简化检测：如果两个聚集区相对于中转站的角度差大于90度，可能交叉
        double dx1 = acc1.getLongitude() - depot.getLongitude();
        double dy1 = acc1.getLatitude() - depot.getLatitude();
        double dx2 = acc2.getLongitude() - depot.getLongitude();
        double dy2 = acc2.getLatitude() - depot.getLatitude();
        
        double angle1 = Math.atan2(dy1, dx1);
        double angle2 = Math.atan2(dy2, dx2);
        double angleDiff = Math.abs(angle1 - angle2);
        
        if (angleDiff > Math.PI) {
            angleDiff = 2 * Math.PI - angleDiff;
        }
        
        return angleDiff > Math.PI / 2;
    }
    
    /**
     * 计算聚类紧凑性
     */
    private double calculateClusterCompactness(List<Accumulation> cluster, TransitDepot depot) {
        if (cluster.size() <= 1) return 1.0;
        
        // 计算聚类质心
        double centerLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        
        // 计算平均半径
        double avgRadius = cluster.stream()
                .mapToDouble(acc -> calculateDistance(acc.getLongitude(), acc.getLatitude(), centerLng, centerLat))
                .average()
                .orElse(0.0);
        
        // 计算到中转站的距离
        double distanceToDepot = calculateDistance(centerLng, centerLat, depot.getLongitude(), depot.getLatitude());
        
        // 紧凑性 = 1 - (平均半径 / 到中转站距离)，限制在[0,1]
        return distanceToDepot > 0 ? Math.max(0.0, 1.0 - avgRadius / distanceToDepot) : 1.0;
    }
    
    /**
     * 计算聚类效率
     */
    private double calculateClusterEfficiency(
            List<Accumulation> cluster, 
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (cluster.isEmpty()) return 1.0;
        
        // 简化效率计算：配送时间占比 vs 行驶时间占比
        double totalDeliveryTime = cluster.stream()
                .mapToDouble(Accumulation::getDeliveryTime)
                .sum();
        
        // 估算总行驶时间（简化为到中转站距离之和）
        double totalTravelTime = cluster.stream()
                .mapToDouble(acc -> calculateDistance(
                        acc.getLongitude(), acc.getLatitude(),
                        depot.getLongitude(), depot.getLatitude()) * 2.0) // 往返
                .sum();
        
        double totalTime = totalDeliveryTime + totalTravelTime;
        return totalTime > 0 ? totalDeliveryTime / totalTime : 1.0;
    }
    
    /**
     * 计算两点间距离
     */
    private double calculateDistance(double lng1, double lat1, double lng2, double lat2) {
        double earthRadius = 6371.0;
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLngRad = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return earthRadius * c;
    }
    
    /**
     * 聚类质量指标数据结构
     */
    @Data
    public static class ClusteringQualityMetrics {
        private double pathCrossingIndex;           // 路径交叉指数 (0-1, 越小越好)
        private double timeBalanceIndex;            // 时间均衡指数 (0-1, 越大越好)
        private double geographicCompactnessIndex;  // 地理紧凑性指数 (0-1, 越大越好)
        private double serviceRadiusViolationRate;  // 服务半径违反率 (0-1, 越小越好)
        private double deliveryEfficiencyIndex;     // 配送效率指数 (0-1, 越大越好)
        private double overallQualityScore;         // 综合质量分数 (0-1, 越大越好)
        
        /**
         * 生成质量报告
         */
        public String generateReport() {
            StringBuilder sb = new StringBuilder();
            sb.append("聚类质量评估报告:\n");
            sb.append(String.format("路径交叉指数: %.3f (目标: < 0.100)\n", pathCrossingIndex));
            sb.append(String.format("时间均衡指数: %.3f (目标: > 0.800)\n", timeBalanceIndex));
            sb.append(String.format("地理紧凑性: %.3f (目标: > 0.700)\n", geographicCompactnessIndex));
            sb.append(String.format("半径违反率: %.3f (目标: < 0.050)\n", serviceRadiusViolationRate));
            sb.append(String.format("配送效率: %.3f (目标: > 0.750)\n", deliveryEfficiencyIndex));
            sb.append(String.format("综合质量分数: %.3f\n", overallQualityScore));
            
            // 质量等级评定
            String grade;
            if (overallQualityScore >= 0.85) grade = "优秀";
            else if (overallQualityScore >= 0.70) grade = "良好";
            else if (overallQualityScore >= 0.55) grade = "中等";
            else if (overallQualityScore >= 0.40) grade = "较差";
            else grade = "差";
            
            sb.append(String.format("质量等级: %s\n", grade));
            
            return sb.toString();
        }
    }
}