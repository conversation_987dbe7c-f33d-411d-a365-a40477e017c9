package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.lang.reflect.Field;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * JNI修复服务 - 修复类初始化缓存问题
 * 必须在任何OR-Tools类首次访问前执行修复
 * 解决JVM类初始化失败缓存机制导致的"Could not initialize class"问题
 */
@Slf4j
public class JNIFixService {
    
    private static final AtomicBoolean fixed = new AtomicBoolean(false);
    private static final AtomicBoolean orToolsWorking = new AtomicBoolean(false);
    private static final AtomicBoolean classesAlreadyTried = new AtomicBoolean(false);
    
    /**
     * 执行JNI修复
     * 必须在任何OR-Tools类加载前调用
     */
    public static synchronized boolean performJNIFix() {
        if (fixed.get()) {
            return orToolsWorking.get();
        }
        
        log.info("🔧 开始JNI修复服务...");
        
        try {
            // 阶段1：环境修复
            fixEnvironment();
            
            // 阶段2：库路径修复
            fixLibraryPath();
            
            // 阶段3：系统属性修复
            fixSystemProperties();
            
            // 阶段4：JVM状态清理
            cleanJVMState();
            
            // 阶段5：验证基础环境（不预判OR-Tools状态）
            boolean envReady = verifyWithoutLoading();
            
            fixed.set(true);
            // 🔧 修复：不在这里设置orToolsWorking状态，让safeORToolsTest()进行真正测试
            
            if (envReady) {
                log.info("✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试");
            } else {
                log.warn("⚠️  JNI修复服务完成 - 基础环境有问题，OR-Tools可能无法工作");
            }
            
            return envReady;
            
        } catch (Exception e) {
            log.error("JNI修复服务失败: {}", e.getMessage());
            fixed.set(true);
            orToolsWorking.set(false);
            return false;
        }
    }
    
    /**
     * 修复环境
     */
    private static void fixEnvironment() {
        try {
            log.debug("修复环境...");
            
            // 设置正确的编码
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            
            // 设置JNI相关属性
            System.setProperty("java.awt.headless", "true");
            
            // Windows特定修复
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                // 确保Windows DLL搜索路径正确
                String systemRoot = System.getenv("SystemRoot");
                if (systemRoot != null) {
                    String system32 = systemRoot + "\\System32";
                    String currentPath = System.getProperty("java.library.path");
                    if (!currentPath.contains(system32)) {
                        System.setProperty("java.library.path", currentPath + File.pathSeparator + system32);
                    }
                }
            }
            
            log.debug("✅ 环境修复完成");
            
        } catch (Exception e) {
            log.debug("环境修复异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 修复库路径
     */
    private static void fixLibraryPath() {
        try {
            log.debug("修复库路径...");
            
            // 构建增强的库路径
            String javaLibraryPath = System.getProperty("java.library.path", "");
            String tempDir = System.getProperty("java.io.tmpdir", "");
            String userDir = System.getProperty("user.dir", "");
            String javaHome = System.getProperty("java.home", "");
            
            StringBuilder enhancedPath = new StringBuilder(javaLibraryPath);
            
            // 添加临时目录
            if (!tempDir.isEmpty() && !javaLibraryPath.contains(tempDir)) {
                enhancedPath.append(File.pathSeparator).append(tempDir);
            }
            
            // 添加用户目录
            if (!userDir.isEmpty() && !javaLibraryPath.contains(userDir)) {
                enhancedPath.append(File.pathSeparator).append(userDir);
            }
            
            // 添加Java home的lib目录
            if (!javaHome.isEmpty()) {
                String javaLibDir = javaHome + File.separator + "lib";
                if (new File(javaLibDir).exists() && !javaLibraryPath.contains(javaLibDir)) {
                    enhancedPath.append(File.pathSeparator).append(javaLibDir);
                }
            }
            
            // 设置增强的路径
            System.setProperty("java.library.path", enhancedPath.toString());
            
            // 强制JVM重新读取library path
            Field fieldSysPath = ClassLoader.class.getDeclaredField("sys_paths");
            fieldSysPath.setAccessible(true);
            fieldSysPath.set(null, null);
            
            log.debug("✅ 库路径修复完成");
            
        } catch (Exception e) {
            log.debug("库路径修复异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 修复系统属性
     */
    private static void fixSystemProperties() {
        try {
            log.debug("修复系统属性...");
            
            // JNI相关属性
            System.setProperty("java.library.path.native", "true");
            
            // 内存相关属性
            long maxMemory = Runtime.getRuntime().maxMemory();
            if (maxMemory < 512 * 1024 * 1024) { // 小于512MB
                log.warn("可用内存较少: {}MB，可能影响OR-Tools性能", maxMemory / 1024 / 1024);
            }
            
            // 临时文件属性
            String tmpDir = System.getProperty("java.io.tmpdir");
            File tmpDirFile = new File(tmpDir);
            if (!tmpDirFile.exists() || !tmpDirFile.canWrite()) {
                log.warn("临时目录不可用: {}", tmpDir);
                // 尝试设置备用临时目录
                String userHome = System.getProperty("user.home");
                if (userHome != null) {
                    String altTmpDir = userHome + File.separator + ".tmp";
                    File altTmpDirFile = new File(altTmpDir);
                    if (!altTmpDirFile.exists()) {
                        altTmpDirFile.mkdirs();
                    }
                    if (altTmpDirFile.exists() && altTmpDirFile.canWrite()) {
                        System.setProperty("java.io.tmpdir", altTmpDir);
                        log.info("设置备用临时目录: {}", altTmpDir);
                    }
                }
            }
            
            log.debug("✅ 系统属性修复完成");
            
        } catch (Exception e) {
            log.debug("系统属性修复异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 清理JVM状态
     */
    private static void cleanJVMState() {
        try {
            log.debug("清理JVM状态...");
            
            // 强制垃圾回收
            System.gc();
            System.runFinalization();
            
            // 清理可能的缓存
            try {
                // 尝试清理反射缓存
                Class<?> reflectionFactoryClass = Class.forName("sun.reflect.ReflectionFactory");
                // 这里可以添加更多的缓存清理逻辑
            } catch (Exception e) {
                // 忽略，不是所有JVM都有这些类
            }
            
            // 等待一小段时间让JVM稳定
            Thread.sleep(100);
            
            log.debug("✅ JVM状态清理完成");
            
        } catch (Exception e) {
            log.debug("JVM状态清理异常（继续）: {}", e.getMessage());
        }
    }
    
    /**
     * 验证修复效果（不加载可能失败的类）
     */
    private static boolean verifyWithoutLoading() {
        try {
            log.debug("验证修复效果...");
            
            // 检查关键路径是否存在
            String tmpDir = System.getProperty("java.io.tmpdir");
            if (tmpDir == null || tmpDir.isEmpty()) {
                log.warn("临时目录未设置");
                return false;
            }
            
            File tmpDirFile = new File(tmpDir);
            if (!tmpDirFile.exists() || !tmpDirFile.canWrite()) {
                log.warn("临时目录不可用: {}", tmpDir);
                return false;
            }
            
            // 检查是否存在OR-Tools相关的临时文件
            File[] orToolsFiles = tmpDirFile.listFiles((dir, name) -> 
                name.toLowerCase().contains("ortools"));
            
            if (orToolsFiles != null && orToolsFiles.length > 0) {
                log.debug("发现{}个OR-Tools临时文件", orToolsFiles.length);
            }
            
            // 基本的类加载能力检查（只检查Java标准库）
            try {
                Class.forName("java.lang.String");
                Class.forName("java.util.List");
                log.debug("基本类加载能力正常");
            } catch (Exception e) {
                log.error("基本类加载能力异常: {}", e.getMessage());
                return false;
            }
            
            log.debug("✅ 验证通过");
            return true;
            
        } catch (Exception e) {
            log.debug("验证异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全的OR-Tools测试
     * 只有在JNI修复完成后才调用，现在使用反射避免类初始化缓存问题
     */
    public static boolean safeORToolsTest() {
        if (!fixed.get()) {
            performJNIFix();
        }
        
        // 检查类加载保护器状态
        if (ORToolsClassLoadGuard.isORToolsClassesPolluted()) {
            log.error("❌ [OR-Tools测试] 检测到OR-Tools类已被污染，无法进行测试");
            orToolsWorking.set(false);
            return false;
        }
        
        log.info("🔍 [OR-Tools诊断] 开始使用反射安全测试OR-Tools库可用性...");
        
        try {
            log.debug("执行安全的OR-Tools反射测试...");
            
            // 分步测试，每一步都有异常处理，使用反射避免直接类引用
            
            // 步骤1：使用安全加载器检查类是否存在
            log.info("🔍 [OR-Tools测试] 步骤1: 安全检查OR-Tools类是否存在...");
            Class<?> loaderClass = ORToolsClassLoadGuard.safeLoadORToolsClass("com.google.ortools.Loader");
            log.info("✅ [OR-Tools测试] Loader类安全加载成功");
            
            Class<?> managerClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.constraintsolver.RoutingIndexManager");  
            log.info("✅ [OR-Tools测试] RoutingIndexManager类安全加载成功");
            
            Class<?> modelClass = ORToolsClassLoadGuard.safeLoadORToolsClass(
                "com.google.ortools.constraintsolver.RoutingModel");
            log.info("✅ [OR-Tools测试] RoutingModel类安全加载成功");
            
            // 步骤2：加载原生库
            log.info("🔍 [OR-Tools测试] 步骤2: 加载JNI原生库...");
            java.lang.reflect.Method loadMethod = loaderClass.getMethod("loadNativeLibraries");
            loadMethod.invoke(null);
            log.info("✅ [OR-Tools测试] JNI原生库加载成功！");
            
            // 步骤3：创建简单对象
            log.info("🔍 [OR-Tools测试] 步骤3: 创建OR-Tools对象...");
            java.lang.reflect.Constructor<?> managerConstructor = managerClass.getConstructor(
                int.class, int.class, int.class);
            Object manager = managerConstructor.newInstance(2, 1, 0);
            log.info("✅ [OR-Tools测试] RoutingIndexManager创建成功");
            
            java.lang.reflect.Constructor<?> modelConstructor = modelClass.getConstructor(managerClass);
            Object model = modelConstructor.newInstance(manager);
            log.info("✅ [OR-Tools测试] RoutingModel创建成功");
            
            // 步骤4：基本求解测试
            log.info("🔍 [OR-Tools测试] 步骤4: 执行基本求解测试...");
            java.lang.reflect.Method solveMethod = modelClass.getMethod("solve");
            Object solution = solveMethod.invoke(model);
            log.info("✅ [OR-Tools测试] 基本求解成功，解状态: {}", solution != null ? "有解" : "无解");
            
            // 🎉 所有测试通过，更新状态
            orToolsWorking.set(true);
            log.info("🎉 [OR-Tools成功] Google OR-Tools 9.8.3296 通过反射测试！所有功能验证通过！");
            return true;
            
        } catch (UnsatisfiedLinkError e) {
            log.error("❌ [OR-Tools失败] JNI原生库加载失败 - {}", e.getMessage());
            log.error("🔧 [解决建议] 请检查: 1) Visual C++ Redistributable是否安装 2) 系统架构是否为x64 3) java.library.path设置");
            log.error("📍 [当前环境] OS: {}, Arch: {}, Java: {}", 
                    System.getProperty("os.name"), System.getProperty("os.arch"), System.getProperty("java.version"));
            orToolsWorking.set(false);
            return false;
        } catch (NoClassDefFoundError e) {
            log.error("❌ [OR-Tools失败] OR-Tools类定义未找到 - {}", e.getMessage());
            log.error("🔧 [解决建议] 请检查: 1) ortools-java依赖是否正确 2) Maven依赖是否下载完整");
            orToolsWorking.set(false);
            return false;
        } catch (ExceptionInInitializerError e) {
            String causeMsg = e.getCause() != null ? e.getCause().getMessage() : e.getMessage();
            log.error("❌ [OR-Tools失败] OR-Tools类初始化失败 - {}", causeMsg);
            log.error("🔧 [解决建议] 请检查: 1) 系统权限 2) 临时目录权限 3) 防病毒软件是否阻止");
            orToolsWorking.set(false);
            return false;
        } catch (ClassNotFoundException e) {
            log.error("❌ [OR-Tools失败] OR-Tools类未找到 - {}", e.getMessage());
            log.error("🔧 [解决建议] Maven依赖配置可能有问题，请检查ortools-java和ortools-win32-x86-64依赖");
            orToolsWorking.set(false);
            return false;
        } catch (Exception e) {
            log.error("❌ [OR-Tools失败] 未知错误 - {} ({})", e.getMessage(), e.getClass().getSimpleName());
            log.error("🔧 [解决建议] 请查看完整异常堆栈，可能需要重启应用或检查环境配置");
            if (log.isDebugEnabled()) {
                log.debug("OR-Tools测试异常详情:", e);
            }
            orToolsWorking.set(false);
            return false;
        }
    }
    
    /**
     * 检查OR-Tools是否工作正常
     */
    public static boolean isORToolsWorking() {
        if (!fixed.get()) {
            performJNIFix();
        }
        return orToolsWorking.get();
    }
    
    /**
     * 重置服务状态
     */
    public static void reset() {
        fixed.set(false);
        orToolsWorking.set(false);
        log.debug("JNI修复服务状态已重置");
    }
    
    /**
     * 获取状态信息
     */
    public static String getStatus() {
        return String.format("JNIFixService[fixed=%s, orToolsWorking=%s]", 
                           fixed.get(), orToolsWorking.get());
    }
}