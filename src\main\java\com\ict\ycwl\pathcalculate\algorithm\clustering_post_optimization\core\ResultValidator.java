package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 结果验证器
 * 
 * 验证优化结果的数据完整性和约束满足度
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class ResultValidator {
    
    /**
     * 验证数据完整性
     */
    public ValidationResult validateDataIntegrity(
        List<List<Accumulation>> originalClusters,
        List<List<Accumulation>> optimizedClusters
    ) {
        ValidationResult result = new ValidationResult();
        
        // 1. 验证聚集区总数不变
        int originalCount = originalClusters.stream().mapToInt(List::size).sum();
        int optimizedCount = optimizedClusters.stream().mapToInt(List::size).sum();
        result.addCheck("聚集区总数一致性", originalCount == optimizedCount);
        
        // 2. 验证聚集区ID完整性
        Set<Long> originalIds = extractAllAccumulationIds(originalClusters);
        Set<Long> optimizedIds = extractAllAccumulationIds(optimizedClusters);
        result.addCheck("聚集区ID完整性", originalIds.equals(optimizedIds));
        
        // 3. 验证无重复分配
        result.addCheck("无重复分配", hasNoDuplicateAssignments(optimizedClusters));
        
        // 4. 验证无空聚类
        result.addCheck("无空聚类", hasNoEmptyClusters(optimizedClusters));
        
        log.debug("📋 数据完整性验证结果: {}/{} 项通过", 
            result.getChecks().stream().filter(ValidationResult.ValidationCheck::isPassed).count(),
            result.getChecks().size());
        
        return result;
    }
    
    /**
     * 提取所有聚集区ID
     */
    private Set<Long> extractAllAccumulationIds(List<List<Accumulation>> clusters) {
        return clusters.stream()
            .flatMap(List::stream)
            .map(Accumulation::getAccumulationId)
            .collect(Collectors.toSet());
    }
    
    /**
     * 检查是否有重复分配
     */
    private boolean hasNoDuplicateAssignments(List<List<Accumulation>> clusters) {
        Set<Long> allIds = new HashSet<>();
        
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation acc : cluster) {
                if (!allIds.add(acc.getAccumulationId())) {
                    return false; // 发现重复
                }
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否有空聚类
     */
    private boolean hasNoEmptyClusters(List<List<Accumulation>> clusters) {
        return clusters.stream().allMatch(cluster -> !cluster.isEmpty());
    }
}