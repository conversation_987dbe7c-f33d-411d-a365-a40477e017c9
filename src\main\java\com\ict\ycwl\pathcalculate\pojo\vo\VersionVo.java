package com.ict.ycwl.pathcalculate.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Map;

@Data
public class VersionVo {
    //版本id
    @TableId
    private int versionId;
    //版本名称
    private String versionName;
    //实际对应的数据库
    private String versionDb;
    //备注
    private String versionInfo;
    //更新时间
    private String updateTime;
    //是否显示在列表中(1显示，0不显示）
    private int isShow;

}
