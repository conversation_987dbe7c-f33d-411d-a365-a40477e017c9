package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.config;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain.VRPConstraints;

import com.graphhopper.jsprit.core.algorithm.VehicleRoutingAlgorithm;
import com.graphhopper.jsprit.core.algorithm.box.Jsprit;
import com.graphhopper.jsprit.core.algorithm.state.StateManager;
import com.graphhopper.jsprit.core.algorithm.termination.IterationWithoutImprovementTermination;
import com.graphhopper.jsprit.core.algorithm.termination.TimeTermination;
import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.constraint.ConstraintManager;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * JSPRIT算法配置器
 * 
 * 配置JSPRIT求解器的各种参数，包括Ruin & Recreate策略、终止条件等
 * 针对聚类二次优化场景进行专门调优
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Slf4j
@Component
public class JSPRITAlgorithmConfig {
    
    /**
     * JSPRIT算法参数配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JSPRITParameters {
        
        /**
         * 最大迭代次数
         */
        @Builder.Default
        private Integer maxIterations = 2000;
        
        /**
         * 最大运行时间（秒）
         */
        @Builder.Default
        private Integer maxRuntimeSeconds = 60;
        
        /**
         * 无改进终止迭代次数
         */
        @Builder.Default
        private Integer maxIterationsWithoutImprovement = 200;
        
        /**
         * 线程数
         */
        @Builder.Default
        private Integer numberOfThreads = 4;
        
        /**
         * Ruin策略权重配置
         */
        @Builder.Default
        private RuinStrategyWeights ruinWeights = RuinStrategyWeights.createDefault();
        
        /**
         * Recreate策略权重配置
         */
        @Builder.Default
        private RecreateStrategyWeights recreateWeights = RecreateStrategyWeights.createDefault();
        
        /**
         * 接受策略类型
         */
        @Builder.Default
        private AcceptanceStrategy acceptanceStrategy = AcceptanceStrategy.THRESHOLD_ACCEPTANCE;
        
        /**
         * 内存策略（保留搜索历史）
         */
        @Builder.Default
        private Boolean enableMemoryStrategy = true;
        
        /**
         * 是否启用快速模式
         */
        @Builder.Default
        private Boolean fastMode = false;
        
        /**
         * 构造启发式类型
         */
        @Builder.Default
        private ConstructionHeuristic constructionHeuristic = ConstructionHeuristic.BEST_INSERTION;
        
        /**
         * 解决方案池大小
         */
        @Builder.Default
        private Integer solutionPoolSize = 10;
    }
    
    /**
     * Ruin策略权重
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuinStrategyWeights {
        
        /**
         * 随机ruin权重
         */
        @Builder.Default
        private Double randomRuin = 0.3;
        
        /**
         * 最差ruin权重（移除成本最高的工作）
         */
        @Builder.Default
        private Double worstRuin = 0.2;
        
        /**
         * 时间导向ruin权重
         */
        @Builder.Default
        private Double timeOrientedRuin = 0.25;
        
        /**
         * 邻域ruin权重（地理相近的工作）
         */
        @Builder.Default
        private Double neighborhoodRuin = 0.15;
        
        /**
         * 集群ruin权重
         */
        @Builder.Default
        private Double clusterRuin = 0.1;
        
        public static RuinStrategyWeights createDefault() {
            return RuinStrategyWeights.builder().build();
        }
        
        public static RuinStrategyWeights createTimeBalanceFocused() {
            return RuinStrategyWeights.builder()
                .randomRuin(0.2)
                .worstRuin(0.35) // 提高最差ruin权重，优先移除时间成本高的
                .timeOrientedRuin(0.3) // 提高时间导向ruin权重
                .neighborhoodRuin(0.1)
                .clusterRuin(0.05)
                .build();
        }
        
        public static RuinStrategyWeights createGeographicFocused() {
            return RuinStrategyWeights.builder()
                .randomRuin(0.15)
                .worstRuin(0.15)
                .timeOrientedRuin(0.2)
                .neighborhoodRuin(0.35) // 提高邻域ruin权重
                .clusterRuin(0.15) // 提高集群ruin权重
                .build();
        }
    }
    
    /**
     * Recreate策略权重
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecreateStrategyWeights {
        
        /**
         * 最佳插入权重
         */
        @Builder.Default
        private Double bestInsertion = 0.4;
        
        /**
         * 贪心插入权重
         */
        @Builder.Default
        private Double greedyInsertion = 0.3;
        
        /**
         * regret插入权重
         */
        @Builder.Default
        private Double regretInsertion = 0.2;
        
        /**
         * 随机插入权重
         */
        @Builder.Default
        private Double randomInsertion = 0.1;
        
        public static RecreateStrategyWeights createDefault() {
            return RecreateStrategyWeights.builder().build();
        }
        
        public static RecreateStrategyWeights createQualityFocused() {
            return RecreateStrategyWeights.builder()
                .bestInsertion(0.5) // 提高最佳插入权重
                .greedyInsertion(0.2)
                .regretInsertion(0.25) // 提高regret插入权重
                .randomInsertion(0.05)
                .build();
        }
        
        public static RecreateStrategyWeights createSpeedFocused() {
            return RecreateStrategyWeights.builder()
                .bestInsertion(0.2)
                .greedyInsertion(0.5) // 提高贪心插入权重
                .regretInsertion(0.1)
                .randomInsertion(0.2)
                .build();
        }
    }
    
    /**
     * 接受策略枚举
     */
    public enum AcceptanceStrategy {
        THRESHOLD_ACCEPTANCE("阈值接受"),
        SCHRIMPF_ACCEPTANCE("Schrimpf接受"),
        GREEDY_ACCEPTANCE("贪心接受");
        
        private final String description;
        
        AcceptanceStrategy(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造启发式枚举
     */
    public enum ConstructionHeuristic {
        BEST_INSERTION("最佳插入"),
        CHEAPEST_INSERTION("最便宜插入"),
        REGRET_INSERTION("regret插入");
        
        private final String description;
        
        ConstructionHeuristic(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 根据VRP约束创建默认JSPRIT参数
     * 
     * @param constraints VRP约束
     * @return JSPRIT参数
     */
    public JSPRITParameters createDefaultParameters(VRPConstraints constraints) {
        JSPRITParameters.JSPRITParametersBuilder builder = JSPRITParameters.builder();
        
        // 根据约束动态调整参数
        if (constraints.getTimeBalanceWeight() > 0.7) {
            // 时间平衡优先
            builder.ruinWeights(RuinStrategyWeights.createTimeBalanceFocused());
            builder.recreateWeights(RecreateStrategyWeights.createQualityFocused());
            builder.maxIterations(2500); // 增加迭代次数以获得更好的时间平衡
        } else if (constraints.getGeographicConstraintWeight() > 0.5) {
            // 地理优化优先
            builder.ruinWeights(RuinStrategyWeights.createGeographicFocused());
            builder.recreateWeights(RecreateStrategyWeights.createQualityFocused());
        } else {
            // 默认平衡策略
            builder.ruinWeights(RuinStrategyWeights.createDefault());
            builder.recreateWeights(RecreateStrategyWeights.createDefault());
        }
        
        return builder.build();
    }
    
    /**
     * 创建快速优化参数（适合时间敏感场景）
     * 
     * @return 快速优化参数
     */
    public JSPRITParameters createQuickOptimizationParameters() {
        return JSPRITParameters.builder()
            .maxIterations(1000) // 减少迭代次数
            .maxRuntimeSeconds(30) // 减少运行时间
            .maxIterationsWithoutImprovement(100)
            .numberOfThreads(4)
            .ruinWeights(RuinStrategyWeights.createDefault())
            .recreateWeights(RecreateStrategyWeights.createSpeedFocused())
            .acceptanceStrategy(AcceptanceStrategy.GREEDY_ACCEPTANCE) // 使用贪心接受策略
            .enableMemoryStrategy(false) // 禁用内存策略以提高速度
            .fastMode(true)
            .constructionHeuristic(ConstructionHeuristic.CHEAPEST_INSERTION)
            .solutionPoolSize(5)
            .build();
    }
    
    /**
     * 创建深度优化参数（适合质量优先场景）
     * 
     * @return 深度优化参数
     */
    public JSPRITParameters createDeepOptimizationParameters() {
        return JSPRITParameters.builder()
            .maxIterations(5000) // 增加迭代次数
            .maxRuntimeSeconds(300) // 增加运行时间
            .maxIterationsWithoutImprovement(500)
            .numberOfThreads(6) // 增加线程数
            .ruinWeights(RuinStrategyWeights.createTimeBalanceFocused())
            .recreateWeights(RecreateStrategyWeights.createQualityFocused())
            .acceptanceStrategy(AcceptanceStrategy.THRESHOLD_ACCEPTANCE)
            .enableMemoryStrategy(true)
            .fastMode(false)
            .constructionHeuristic(ConstructionHeuristic.BEST_INSERTION)
            .solutionPoolSize(20)
            .build();
    }
    
    /**
     * 配置JSPRIT算法
     * 
     * @param problem VRP问题
     * @param parameters JSPRIT参数
     * @return 配置好的JSPRIT算法
     */
    public VehicleRoutingAlgorithm configureAlgorithm(VehicleRoutingProblem problem, JSPRITParameters parameters) {
        log.debug("🔧 配置JSPRIT算法 - 迭代数: {}, 运行时间: {}秒, 线程数: {}", 
            parameters.getMaxIterations(), parameters.getMaxRuntimeSeconds(), parameters.getNumberOfThreads());
        
        Jsprit.Builder algorithmBuilder = Jsprit.Builder.newInstance(problem);
        
        // 基本参数配置
        algorithmBuilder.setProperty(Jsprit.Parameter.ITERATIONS, parameters.getMaxIterations().toString());
        algorithmBuilder.setProperty(Jsprit.Parameter.THREADS, parameters.getNumberOfThreads().toString());
        
        // 构造启发式配置
        switch (parameters.getConstructionHeuristic()) {
            case BEST_INSERTION:
                algorithmBuilder.setProperty(Jsprit.Parameter.CONSTRUCTION, "best_insertion");
                break;
            case CHEAPEST_INSERTION:
                algorithmBuilder.setProperty(Jsprit.Parameter.CONSTRUCTION, "cheapest_insertion");
                break;
            case REGRET_INSERTION:
                algorithmBuilder.setProperty(Jsprit.Parameter.CONSTRUCTION, "regret_insertion");
                break;
        }
        
        // 内存策略配置（使用字符串常量代替枚举）
        if (parameters.getEnableMemoryStrategy()) {
            algorithmBuilder.setProperty("strategy.memory", "1");
        } else {
            algorithmBuilder.setProperty("strategy.memory", "0");
        }
        
        // 快速模式配置
        if (parameters.getFastMode()) {
            algorithmBuilder.setProperty("fastMode", "true");
        }
        
        // 构建算法
        VehicleRoutingAlgorithm algorithm = algorithmBuilder.buildAlgorithm();
        
        // 添加终止条件
        addTerminationCriteria(algorithm, parameters);
        
        // 配置Ruin和Recreate策略权重
        configureRuinAndRecreateWeights(algorithm, parameters);
        
        log.debug("✅ JSPRIT算法配置完成");
        
        return algorithm;
    }
    
    /**
     * 添加终止条件
     * 
     * @param algorithm 算法实例
     * @param parameters 参数配置
     */
    private void addTerminationCriteria(VehicleRoutingAlgorithm algorithm, JSPRITParameters parameters) {
        // 时间终止条件
        algorithm.addTerminationCriterion(new TimeTermination(parameters.getMaxRuntimeSeconds().longValue() * 1000));
        
        // 无改进终止条件
        algorithm.addTerminationCriterion(new IterationWithoutImprovementTermination(
            parameters.getMaxIterationsWithoutImprovement()));
    }
    
    /**
     * 配置Ruin和Recreate策略权重
     * 
     * @param algorithm 算法实例
     * @param parameters 参数配置
     */
    private void configureRuinAndRecreateWeights(VehicleRoutingAlgorithm algorithm, JSPRITParameters parameters) {
        // 这里可以通过算法的StateManager和ConstraintManager进行更精细的配置
        // 由于JSPRIT的策略权重配置相对复杂，这里保留接口以备将来扩展
        
        log.debug("🎯 配置Ruin策略权重 - 随机: {}, 最差: {}, 时间导向: {}, 邻域: {}, 集群: {}",
            parameters.getRuinWeights().getRandomRuin(),
            parameters.getRuinWeights().getWorstRuin(),
            parameters.getRuinWeights().getTimeOrientedRuin(),
            parameters.getRuinWeights().getNeighborhoodRuin(),
            parameters.getRuinWeights().getClusterRuin());
        
        log.debug("🎯 配置Recreate策略权重 - 最佳插入: {}, 贪心插入: {}, Regret插入: {}, 随机插入: {}",
            parameters.getRecreateWeights().getBestInsertion(),
            parameters.getRecreateWeights().getGreedyInsertion(),
            parameters.getRecreateWeights().getRegretInsertion(),
            parameters.getRecreateWeights().getRandomInsertion());
    }
    
    /**
     * 根据问题特征自动选择参数配置
     * 
     * @param vehicleCount 车辆数量
     * @param serviceCount 服务数量
     * @param constraints VRP约束
     * @return 推荐的参数配置
     */
    public JSPRITParameters selectParametersForProblem(int vehicleCount, int serviceCount, VRPConstraints constraints) {
        log.debug("🤖 自动选择JSPRIT参数 - 车辆数: {}, 服务数: {}", vehicleCount, serviceCount);
        
        // 大规模问题
        if (serviceCount > 100 || vehicleCount > 20) {
            log.debug("   📏 识别为大规模问题，使用快速优化参数");
            return createQuickOptimizationParameters();
        }
        
        // 小规模但高质量要求
        if (serviceCount < 30 && constraints.getTimeBalanceWeight() > 0.8) {
            log.debug("   🎯 识别为高质量要求问题，使用深度优化参数");
            return createDeepOptimizationParameters();
        }
        
        // 默认问题
        log.debug("   ⚖️ 使用默认平衡参数");
        return createDefaultParameters(constraints);
    }
    
    /**
     * 获取参数配置摘要
     * 
     * @param parameters 参数配置
     * @return 摘要描述
     */
    public String getParameterSummary(JSPRITParameters parameters) {
        return String.format("JSPRIT参数{迭代:%d, 时间:%ds, 线程:%d, 构造:%s, 接受:%s, 内存:%s}", 
            parameters.getMaxIterations(),
            parameters.getMaxRuntimeSeconds(),
            parameters.getNumberOfThreads(),
            parameters.getConstructionHeuristic().getDescription(),
            parameters.getAcceptanceStrategy().getDescription(),
            parameters.getEnableMemoryStrategy() ? "启用" : "禁用");
    }
}