package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import lombok.Builder;
import lombok.Data;

/**
 * 优化性能指标
 * 
 * 封装降级算法优化过程中的各项性能指标，用于评估优化效果
 * 包括时间改进、约束满足、违反情况等关键指标
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class OptimizationMetrics {
    
    /**
     * 原始总时间（分钟）
     */
    private double originalTotalTime;
    
    /**
     * 优化后总时间（分钟）
     */
    private double optimizedTotalTime;
    
    /**
     * 时间改进百分比
     */
    private double timeImprovement;
    
    /**
     * 原始约束违反数量
     */
    private int originalViolations;
    
    /**
     * 优化后约束违反数量
     */
    private int optimizedViolations;
    
    /**
     * 约束违反减少数量
     */
    private int violationReduction;
    
    /**
     * 约束满足率（0.0-1.0）
     */
    private double constraintSatisfactionRate;
    
    /**
     * 原始时间标准差
     */
    private double originalTimeStdDev;
    
    /**
     * 优化后时间标准差
     */
    private double optimizedTimeStdDev;
    
    /**
     * 时间均衡改进百分比
     */
    private double timeBalanceImprovement;
    
    /**
     * 地理合理性评分（0.0-1.0）
     */
    private double geographicRationalityScore;
    
    /**
     * 算法收敛性评分（0.0-1.0）
     */
    private double convergenceScore;
    
    /**
     * 计算综合优化评分
     * 
     * @return 综合评分（0.0-100.0）
     */
    public double calculateOverallScore() {
        double score = 0.0;
        
        // 时间改进权重：40%
        if (timeImprovement > 0) {
            score += Math.min(timeImprovement, 30.0) * 40.0 / 30.0;
        }
        
        // 约束违反减少权重：30%
        if (violationReduction > 0) {
            score += Math.min(violationReduction, 10) * 30.0 / 10.0;
        }
        
        // 时间均衡改进权重：20%
        if (timeBalanceImprovement > 0) {
            score += Math.min(timeBalanceImprovement, 50.0) * 20.0 / 50.0;
        }
        
        // 约束满足率权重：10%
        score += constraintSatisfactionRate * 10.0;
        
        return score;
    }
    
    /**
     * 判断是否为优秀的优化结果
     */
    public boolean isExcellentResult() {
        return calculateOverallScore() >= 80.0 && 
               optimizedViolations == 0 && 
               timeImprovement > 10.0;
    }
    
    /**
     * 判断是否为良好的优化结果
     */
    public boolean isGoodResult() {
        return calculateOverallScore() >= 60.0 && 
               violationReduction >= 0 && 
               timeImprovement > 5.0;
    }
    
    /**
     * 判断是否为可接受的优化结果
     */
    public boolean isAcceptableResult() {
        return calculateOverallScore() >= 40.0 && 
               violationReduction >= 0;
    }
    
    /**
     * 获取优化结果等级
     */
    public String getOptimizationGrade() {
        if (isExcellentResult()) return "优秀";
        if (isGoodResult()) return "良好";
        if (isAcceptableResult()) return "可接受";
        return "需改进";
    }
    
    /**
     * 计算时间效率
     */
    public double getTimeEfficiency() {
        if (originalTotalTime == 0) return 0.0;
        return optimizedTotalTime / originalTotalTime;
    }
    
    /**
     * 计算约束改进率
     */
    public double getConstraintImprovementRate() {
        if (originalViolations == 0) return 0.0;
        return (double) violationReduction / originalViolations * 100.0;
    }
    
    /**
     * 计算时间方差减少率
     */
    public double getTimeVarianceReductionRate() {
        if (originalTimeStdDev == 0) return 0.0;
        double variance_reduction = originalTimeStdDev - optimizedTimeStdDev;
        return variance_reduction / originalTimeStdDev * 100.0;
    }
    
    /**
     * 生成性能指标摘要
     */
    public String generateSummary() {
        return String.format(
            "综合评分:%.1f | %s | 时间改进:%.1f%% | 约束减少:%d个 | 满足率:%.1f%% | 均衡改进:%.1f%%",
            calculateOverallScore(),
            getOptimizationGrade(),
            timeImprovement,
            violationReduction,
            constraintSatisfactionRate * 100.0,
            timeBalanceImprovement
        );
    }
    
    /**
     * 生成详细的性能报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("📊 优化性能指标详细报告\n");
        report.append("─────────────────────────────────────────────────────\n");
        
        // 总体评价
        report.append(String.format("🎯 综合评分: %.1f/100.0 (%s)\n", 
            calculateOverallScore(), getOptimizationGrade()));
        
        // 时间指标
        report.append("\n⏱️ 时间优化指标:\n");
        report.append(String.format("   原始总时间: %.1f分钟\n", originalTotalTime));
        report.append(String.format("   优化后总时间: %.1f分钟\n", optimizedTotalTime));
        report.append(String.format("   时间改进: %.1f%% (节省%.1f分钟)\n", 
            timeImprovement, originalTotalTime - optimizedTotalTime));
        report.append(String.format("   时间效率: %.3f\n", getTimeEfficiency()));
        
        // 约束指标
        report.append("\n🚫 约束违反指标:\n");
        report.append(String.format("   原始违反数: %d个\n", originalViolations));
        report.append(String.format("   优化后违反数: %d个\n", optimizedViolations));
        report.append(String.format("   违反减少: %d个 (改进率:%.1f%%)\n", 
            violationReduction, getConstraintImprovementRate()));
        report.append(String.format("   约束满足率: %.1f%%\n", constraintSatisfactionRate * 100.0));
        
        // 均衡指标
        report.append("\n⚖️ 时间均衡指标:\n");
        report.append(String.format("   原始时间标准差: %.2f分钟\n", originalTimeStdDev));
        report.append(String.format("   优化后时间标准差: %.2f分钟\n", optimizedTimeStdDev));
        report.append(String.format("   时间均衡改进: %.1f%%\n", timeBalanceImprovement));
        report.append(String.format("   方差减少率: %.1f%%\n", getTimeVarianceReductionRate()));
        
        // 质量指标
        report.append("\n📍 质量评估指标:\n");
        report.append(String.format("   地理合理性: %.1f%%\n", geographicRationalityScore * 100.0));
        report.append(String.format("   算法收敛性: %.1f%%\n", convergenceScore * 100.0));
        
        // 结果判定
        report.append("\n✅ 结果判定:\n");
        report.append(String.format("   优秀结果: %s\n", isExcellentResult() ? "是" : "否"));
        report.append(String.format("   良好结果: %s\n", isGoodResult() ? "是" : "否"));
        report.append(String.format("   可接受结果: %s\n", isAcceptableResult() ? "是" : "否"));
        
        report.append("─────────────────────────────────────────────────────\n");
        
        return report.toString();
    }
    
    /**
     * 与基准指标比较
     */
    public ComparisonResult compareWith(OptimizationMetrics baseline) {
        if (baseline == null) {
            return ComparisonResult.builder()
                .timeComparisonResult("无基准数据")
                .constraintComparisonResult("无基准数据")
                .overallComparison("无基准数据")
                .build();
        }
        
        double timeCompare = this.timeImprovement - baseline.timeImprovement;
        double constraintCompare = this.violationReduction - baseline.violationReduction;
        double overallCompare = this.calculateOverallScore() - baseline.calculateOverallScore();
        
        return ComparisonResult.builder()
            .timeComparisonResult(String.format("%+.1f%%", timeCompare))
            .constraintComparisonResult(String.format("%+d个", (int)constraintCompare))
            .overallComparison(String.format("%+.1f分", overallCompare))
            .build();
    }
    
    /**
     * 比较结果内部类
     */
    @Data
    @Builder
    public static class ComparisonResult {
        private String timeComparisonResult;
        private String constraintComparisonResult;
        private String overallComparison;
    }
}