package com.ict.ycwl.pathcalculate.algorithm.dto;

import com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeBalanceStats;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 路径规划结果数据结构
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PathPlanningResult {
    
    /**
     * 计算状态
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 路径规划结果
     * 包含所有优化后的路线信息
     */
    private List<RouteResult> routes;
    
    /**
     * 计算耗时（毫秒）
     */
    private Long executionTime;
    
    /**
     * 时间均衡统计（用于评估算法效果）
     */
    private TimeBalanceStats timeBalanceStats;
    
    /**
     * 创建成功的结果
     * 
     * @param routes 路线结果列表
     * @param executionTime 执行时间
     * @param timeBalanceStats 时间均衡统计
     * @return 成功的结果对象
     */
    public static PathPlanningResult success(List<RouteResult> routes, 
                                           Long executionTime, 
                                           TimeBalanceStats timeBalanceStats) {
        return PathPlanningResult.builder()
                .success(true)
                .errorMessage(null)
                .routes(routes)
                .executionTime(executionTime)
                .timeBalanceStats(timeBalanceStats)
                .build();
    }
    
    /**
     * 创建失败的结果
     * 
     * @param errorMessage 错误信息
     * @param executionTime 执行时间
     * @return 失败的结果对象
     */
    public static PathPlanningResult failure(String errorMessage, Long executionTime) {
        return PathPlanningResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .routes(null)
                .executionTime(executionTime)
                .timeBalanceStats(null)
                .build();
    }
    
    /**
     * 创建失败的结果（带异常）
     * 
     * @param exception 异常对象
     * @param executionTime 执行时间
     * @return 失败的结果对象
     */
    public static PathPlanningResult failure(Exception exception, Long executionTime) {
        String errorMessage = exception != null ? 
                              exception.getMessage() : 
                              "未知错误";
        return failure(errorMessage, executionTime);
    }
    
    /**
     * 获取路线总数
     * 
     * @return 路线总数
     */
    public int getRouteCount() {
        return routes != null ? routes.size() : 0;
    }
    
    /**
     * 获取聚集区总数
     * 
     * @return 聚集区总数
     */
    public int getAccumulationCount() {
        if (routes == null) {
            return 0;
        }
        
        return routes.stream()
                .mapToInt(route -> route.getAccumulationSequence() != null ? 
                                 route.getAccumulationSequence().size() : 0)
                .sum();
    }
    
    /**
     * 获取所有路线的总工作时长
     * 
     * @return 总工作时长（分钟）
     */
    public double getTotalWorkTime() {
        if (routes == null) {
            return 0.0;
        }
        
        return routes.stream()
                .mapToDouble(route -> route.getTotalWorkTime() != null ? 
                                    route.getTotalWorkTime() : 0.0)
                .sum();
    }
    
    /**
     * 获取平均路线工作时长
     * 
     * @return 平均工作时长（分钟）
     */
    public double getAverageWorkTime() {
        int routeCount = getRouteCount();
        if (routeCount == 0) {
            return 0.0;
        }
        
        return getTotalWorkTime() / routeCount;
    }
    
    /**
     * 检查结果是否有效
     * 
     * @return 结果是否有效
     */
    public boolean isValid() {
        if (!success) {
            return errorMessage != null && !errorMessage.trim().isEmpty();
        }
        
        return routes != null && !routes.isEmpty() &&
               executionTime != null && executionTime >= 0 &&
               timeBalanceStats != null;
    }
    
    /**
     * 获取算法执行的性能摘要
     * 
     * @return 性能摘要字符串
     */
    public String getPerformanceSummary() {
        if (!success) {
            return String.format("算法执行失败: %s (耗时: %dms)", 
                                errorMessage, executionTime != null ? executionTime : 0);
        }
        
        return String.format("算法执行成功: %d条路线, %d个聚集区, 总时长%.1f分钟, 平均时长%.1f分钟 (耗时: %dms)",
                           getRouteCount(),
                           getAccumulationCount(),
                           getTotalWorkTime(),
                           getAverageWorkTime(),
                           executionTime != null ? executionTime : 0);
    }
    
    /**
     * 获取时间均衡效果摘要
     * 
     * @return 时间均衡摘要字符串
     */
    public String getBalanceSummary() {
        if (timeBalanceStats == null) {
            return "无时间均衡统计数据";
        }
        
        double teamTimeGap = timeBalanceStats.getTeamTimeGap() != null ? 
                           timeBalanceStats.getTeamTimeGap() : 0.0;
        
        double avgDepotTimeGap = 0.0;
        if (timeBalanceStats.getDepotTimeGapByTeam() != null) {
            avgDepotTimeGap = timeBalanceStats.getDepotTimeGapByTeam().values().stream()
                                   .mapToDouble(Double::doubleValue)
                                   .average()
                                   .orElse(0.0);
        }
        
        return String.format("班组间最大时间差: %.1f分钟, 平均中转站时间差: %.1f分钟",
                           teamTimeGap, avgDepotTimeGap);
    }
} 