package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 路线分割算法
 * 
 * 实现智能路线分割，基于K-means++聚类和负载均衡算法
 * 确保分割后的子路线满足时间约束和地理合理性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class RouteSplittingAlgorithm {
    
    /**
     * 分割路线
     * 
     * @param route 要分割的路线
     * @param splitCount 分割数量
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 分割后的子路线列表
     */
    public List<List<Accumulation>> splitRoute(List<Accumulation> route, 
                                             int splitCount,
                                             TransitDepot depot,
                                             Map<String, TimeInfo> timeMatrix) {
        
        log.debug("🔪 开始分割路线：{} → {} 个子路线", route.size(), splitCount);
        
        // 输入验证
        if (route == null || route.isEmpty()) {
            log.warn("⚠️ 输入路线为空，无法分割");
            return Arrays.asList(route);
        }
        
        if (splitCount <= 1) {
            log.warn("⚠️ 分割数量 <= 1，返回原路线");
            return Arrays.asList(route);
        }
        
        if (route.size() < splitCount) {
            log.warn("⚠️ 聚集区数量({})少于分割数量({})，无法分割", route.size(), splitCount);
            return Arrays.asList(route);
        }
        
        try {
            // 方法1：基于负载均衡的简单分割
            List<List<Accumulation>> balancedSplit = splitByLoadBalancing(route, splitCount, depot, timeMatrix);
            
            // 方法2：基于地理位置的K-means分割（如果有坐标信息）
            List<List<Accumulation>> geographicSplit = splitByGeographicClustering(route, splitCount, depot, timeMatrix);
            
            // 选择更优的分割方案
            List<List<Accumulation>> bestSplit = selectBestSplit(balancedSplit, geographicSplit, depot, timeMatrix);
            
            // 验证分割结果
            if (validateSplitResult(bestSplit, route, depot, timeMatrix)) {
                log.info("✅ 路线分割成功：{} → {} 个子路线", route.size(), bestSplit.size());
                return bestSplit;
            } else {
                log.warn("⚠️ 分割结果验证失败，返回原路线");
                return Arrays.asList(route);
            }
            
        } catch (Exception e) {
            log.error("❌ 路线分割失败", e);
            return Arrays.asList(route);
        }
    }
    
    /**
     * 基于负载均衡的路线分割
     */
    private List<List<Accumulation>> splitByLoadBalancing(List<Accumulation> route, 
                                                        int splitCount,
                                                        TransitDepot depot,
                                                        Map<String, TimeInfo> timeMatrix) {
        
        log.debug("⚖️ 使用负载均衡算法分割路线");
        
        // 计算每个聚集区的工作时间权重
        List<AccumulationWeight> weights = route.stream()
            .map(acc -> AccumulationWeight.builder()
                .accumulation(acc)
                .workTime(calculateAccumulationWorkTime(acc, depot, timeMatrix))
                .travelTime(calculateAccumulationTravelTime(acc, depot, timeMatrix))
                .totalWeight(calculateAccumulationWorkTime(acc, depot, timeMatrix) + 
                           calculateAccumulationTravelTime(acc, depot, timeMatrix))
                .build())
            .collect(Collectors.toList());
        
        // 按权重排序（从大到小）
        weights.sort((a, b) -> Double.compare(b.getTotalWeight(), a.getTotalWeight()));
        
        // 使用贪心算法进行负载均衡分配
        List<List<Accumulation>> subRoutes = new ArrayList<>();
        List<Double> subRouteWeights = new ArrayList<>();
        
        // 初始化子路线
        for (int i = 0; i < splitCount; i++) {
            subRoutes.add(new ArrayList<>());
            subRouteWeights.add(0.0);
        }
        
        // 贪心分配：每次将聚集区分配给当前负载最轻的子路线
        for (AccumulationWeight weight : weights) {
            // 找到负载最轻的子路线
            int minIndex = 0;
            double minWeight = subRouteWeights.get(0);
            
            for (int i = 1; i < subRouteWeights.size(); i++) {
                if (subRouteWeights.get(i) < minWeight) {
                    minWeight = subRouteWeights.get(i);
                    minIndex = i;
                }
            }
            
            // 分配到负载最轻的子路线
            subRoutes.get(minIndex).add(weight.getAccumulation());
            subRouteWeights.set(minIndex, minWeight + weight.getTotalWeight());
        }
        
        // 移除空的子路线
        subRoutes.removeIf(List::isEmpty);
        
        log.debug("   负载均衡分割结果：{} 个子路线", subRoutes.size());
        return subRoutes;
    }
    
    /**
     * 基于地理位置的K-means聚类分割
     */
    private List<List<Accumulation>> splitByGeographicClustering(List<Accumulation> route, 
                                                               int splitCount,
                                                               TransitDepot depot,
                                                               Map<String, TimeInfo> timeMatrix) {
        
        log.debug("🗺️ 使用地理聚类算法分割路线");
        
        // 检查是否有坐标信息
        boolean hasCoordinates = route.stream()
            .anyMatch(acc -> acc.getLatitude() != null && acc.getLongitude() != null);
        
        if (!hasCoordinates) {
            log.debug("   缺少坐标信息，使用时间距离进行聚类");
            return splitByTimeDistanceClustering(route, splitCount, depot, timeMatrix);
        }
        
        // K-means++初始化
        List<Accumulation> centroids = initializeCentroidsKMeansPlusPlus(route, splitCount);
        
        List<List<Accumulation>> clusters = new ArrayList<>();
        for (int i = 0; i < splitCount; i++) {
            clusters.add(new ArrayList<>());
        }
        
        // 迭代聚类（最多10次迭代）
        for (int iteration = 0; iteration < 10; iteration++) {
            // 清空聚类
            clusters.forEach(List::clear);
            
            // 分配点到最近的聚类中心
            for (Accumulation acc : route) {
                int nearestCentroid = findNearestCentroid(acc, centroids);
                clusters.get(nearestCentroid).add(acc);
            }
            
            // 更新聚类中心
            boolean converged = updateCentroids(centroids, clusters);
            if (converged) {
                log.debug("   K-means聚类在第{}次迭代收敛", iteration + 1);
                break;
            }
        }
        
        // 移除空的聚类
        clusters.removeIf(List::isEmpty);
        
        log.debug("   地理聚类分割结果：{} 个子路线", clusters.size());
        return clusters;
    }
    
    /**
     * 基于时间距离的聚类分割（当没有坐标信息时使用）
     */
    private List<List<Accumulation>> splitByTimeDistanceClustering(List<Accumulation> route, 
                                                                 int splitCount,
                                                                 TransitDepot depot,
                                                                 Map<String, TimeInfo> timeMatrix) {
        
        // 构建时间距离矩阵
        Map<String, Map<String, Double>> timeDistanceMatrix = buildTimeDistanceMatrix(route, depot, timeMatrix);
        
        // 使用层次聚类算法
        return performHierarchicalClustering(route, splitCount, timeDistanceMatrix);
    }
    
    /**
     * K-means++初始化聚类中心
     */
    private List<Accumulation> initializeCentroidsKMeansPlusPlus(List<Accumulation> route, int k) {
        List<Accumulation> centroids = new ArrayList<>();
        Random random = new Random();
        
        // 随机选择第一个中心
        centroids.add(route.get(random.nextInt(route.size())));
        
        // 选择剩余的k-1个中心
        for (int i = 1; i < k; i++) {
            List<Double> distances = new ArrayList<>();
            double totalDistance = 0;
            
            // 计算每个点到最近中心的距离平方
            for (Accumulation acc : route) {
                double minDistance = Double.MAX_VALUE;
                for (Accumulation centroid : centroids) {
                    double distance = calculateGeographicDistance(acc, centroid);
                    minDistance = Math.min(minDistance, distance);
                }
                double distanceSquared = minDistance * minDistance;
                distances.add(distanceSquared);
                totalDistance += distanceSquared;
            }
            
            // 按概率选择下一个中心
            double randomValue = random.nextDouble() * totalDistance;
            double cumulativeDistance = 0;
            
            for (int j = 0; j < route.size(); j++) {
                cumulativeDistance += distances.get(j);
                if (cumulativeDistance >= randomValue) {
                    centroids.add(route.get(j));
                    break;
                }
            }
        }
        
        return centroids;
    }
    
    /**
     * 找到最近的聚类中心
     */
    private int findNearestCentroid(Accumulation acc, List<Accumulation> centroids) {
        int nearestIndex = 0;
        double minDistance = calculateGeographicDistance(acc, centroids.get(0));
        
        for (int i = 1; i < centroids.size(); i++) {
            double distance = calculateGeographicDistance(acc, centroids.get(i));
            if (distance < minDistance) {
                minDistance = distance;
                nearestIndex = i;
            }
        }
        
        return nearestIndex;
    }
    
    /**
     * 更新聚类中心
     */
    private boolean updateCentroids(List<Accumulation> centroids, List<List<Accumulation>> clusters) {
        boolean converged = true;
        
        for (int i = 0; i < centroids.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            if (cluster.isEmpty()) {
                continue;
            }
            
            // 计算聚类中心（质心）
            double avgLat = cluster.stream()
                .filter(acc -> acc.getLatitude() != null)
                .mapToDouble(Accumulation::getLatitude)
                .average().orElse(centroids.get(i).getLatitude() != null ? centroids.get(i).getLatitude() : 0.0);
            
            double avgLng = cluster.stream()
                .filter(acc -> acc.getLongitude() != null)
                .mapToDouble(Accumulation::getLongitude)
                .average().orElse(centroids.get(i).getLongitude() != null ? centroids.get(i).getLongitude() : 0.0);
            
            // 找到最接近质心的实际点作为新中心
            Accumulation newCentroid = cluster.stream()
                .min((a, b) -> {
                    double distA = Math.sqrt(Math.pow(a.getLatitude() != null ? a.getLatitude() - avgLat : 0, 2) + 
                                           Math.pow(a.getLongitude() != null ? a.getLongitude() - avgLng : 0, 2));
                    double distB = Math.sqrt(Math.pow(b.getLatitude() != null ? b.getLatitude() - avgLat : 0, 2) + 
                                           Math.pow(b.getLongitude() != null ? b.getLongitude() - avgLng : 0, 2));
                    return Double.compare(distA, distB);
                })
                .orElse(centroids.get(i));
            
            if (!newCentroid.equals(centroids.get(i))) {
                centroids.set(i, newCentroid);
                converged = false;
            }
        }
        
        return converged;
    }
    
    /**
     * 选择最佳分割方案
     */
    private List<List<Accumulation>> selectBestSplit(List<List<Accumulation>> balancedSplit,
                                                   List<List<Accumulation>> geographicSplit,
                                                   TransitDepot depot,
                                                   Map<String, TimeInfo> timeMatrix) {
        
        // 评估负载均衡分割方案
        double balancedScore = evaluateSplitQuality(balancedSplit, depot, timeMatrix);
        
        // 评估地理聚类分割方案
        double geographicScore = evaluateSplitQuality(geographicSplit, depot, timeMatrix);
        
        log.debug("   分割方案评分 - 负载均衡: {:.3f}, 地理聚类: {:.3f}", balancedScore, geographicScore);
        
        return balancedScore >= geographicScore ? balancedSplit : geographicSplit;
    }
    
    /**
     * 评估分割质量
     */
    private double evaluateSplitQuality(List<List<Accumulation>> split, 
                                      TransitDepot depot,
                                      Map<String, TimeInfo> timeMatrix) {
        
        if (split == null || split.isEmpty()) {
            return 0.0;
        }
        
        // 计算各子路线的工作时间
        List<Double> workTimes = split.stream()
            .mapToDouble(subRoute -> calculateRouteWorkTime(subRoute, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        // 评估指标
        double avgWorkTime = workTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double variance = workTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgWorkTime, 2))
            .average().orElse(0);
        double standardDeviation = Math.sqrt(variance);
        
        // 约束满足度
        long constraintViolations = workTimes.stream()
            .mapToLong(time -> time > 450.0 ? 1 : 0)
            .sum();
        
        // 综合评分（平衡性权重0.6，约束满足度权重0.4）
        double balanceScore = 1.0 - (standardDeviation / avgWorkTime); // 平衡性评分
        double constraintScore = 1.0 - ((double) constraintViolations / split.size()); // 约束满足度评分
        
        return balanceScore * 0.6 + constraintScore * 0.4;
    }
    
    /**
     * 验证分割结果
     */
    private boolean validateSplitResult(List<List<Accumulation>> split, 
                                      List<Accumulation> originalRoute,
                                      TransitDepot depot,
                                      Map<String, TimeInfo> timeMatrix) {
        
        if (split == null || split.isEmpty()) {
            return false;
        }
        
        // 验证1：聚集区总数保持不变
        int totalAccumulations = split.stream().mapToInt(List::size).sum();
        if (totalAccumulations != originalRoute.size()) {
            log.warn("⚠️ 分割后聚集区总数不匹配：{} != {}", totalAccumulations, originalRoute.size());
            return false;
        }
        
        // 验证2：没有重复的聚集区
        Set<Long> allAccIds = new HashSet<>();
        for (List<Accumulation> subRoute : split) {
            for (Accumulation acc : subRoute) {
                if (!allAccIds.add(acc.getAccumulationId())) {
                    log.warn("⚠️ 发现重复的聚集区：{}", acc.getAccumulationId());
                    return false;
                }
            }
        }
        
        // 验证3：没有空的子路线
        if (split.stream().anyMatch(List::isEmpty)) {
            log.warn("⚠️ 存在空的子路线");
            return false;
        }
        
        // 验证4：尽量避免超时子路线
        long overtimeRoutes = split.stream()
            .mapToLong(subRoute -> calculateRouteWorkTime(subRoute, depot, timeMatrix) > 450.0 ? 1 : 0)
            .sum();
        
        if (overtimeRoutes > split.size() / 2) { // 超过一半的子路线超时
            log.warn("⚠️ 过多子路线超时：{}/{}", overtimeRoutes, split.size());
            return false;
        }
        
        return true;
    }
    
    /**
     * 构建时间距离矩阵
     */
    private Map<String, Map<String, Double>> buildTimeDistanceMatrix(List<Accumulation> route,
                                                                   TransitDepot depot,
                                                                   Map<String, TimeInfo> timeMatrix) {
        
        Map<String, Map<String, Double>> distanceMatrix = new HashMap<>();
        
        for (Accumulation acc1 : route) {
            Map<String, Double> distances = new HashMap<>();
            for (Accumulation acc2 : route) {
                if (!acc1.equals(acc2)) {
                    // 使用时间信息作为距离度量
                    double distance = calculateTimeDistance(acc1, acc2, depot, timeMatrix);
                    distances.put(String.valueOf(acc2.getAccumulationId()), distance);
                }
            }
            distanceMatrix.put(String.valueOf(acc1.getAccumulationId()), distances);
        }
        
        return distanceMatrix;
    }
    
    /**
     * 执行层次聚类
     */
    private List<List<Accumulation>> performHierarchicalClustering(List<Accumulation> route,
                                                                 int splitCount,
                                                                 Map<String, Map<String, Double>> distanceMatrix) {
        
        // 简化实现：使用平均分割
        List<List<Accumulation>> result = new ArrayList<>();
        int routeSize = route.size();
        int subRouteSize = routeSize / splitCount;
        int remainder = routeSize % splitCount;
        
        int startIndex = 0;
        for (int i = 0; i < splitCount; i++) {
            int currentSubRouteSize = subRouteSize + (i < remainder ? 1 : 0);
            int endIndex = startIndex + currentSubRouteSize;
            
            if (endIndex <= routeSize) {
                result.add(new ArrayList<>(route.subList(startIndex, endIndex)));
                startIndex = endIndex;
            }
        }
        
        return result;
    }
    
    /**
     * 计算聚集区工作时间
     */
    private double calculateAccumulationWorkTime(Accumulation acc, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        return acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 30.0; // 默认30分钟
    }
    
    /**
     * 计算聚集区往返时间
     */
    private double calculateAccumulationTravelTime(Accumulation acc, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null && timeInfo.getTravelTime() != null ? timeInfo.getTravelTime() * 2 : 60.0; // 默认60分钟往返
    }
    
    /**
     * 计算路线总工作时间
     */
    private double calculateRouteWorkTime(List<Accumulation> route, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        for (Accumulation acc : route) {
            totalTime += calculateAccumulationWorkTime(acc, depot, timeMatrix);
            totalTime += calculateAccumulationTravelTime(acc, depot, timeMatrix);
        }
        
        return totalTime;
    }
    
    /**
     * 计算地理距离
     */
    private double calculateGeographicDistance(Accumulation acc1, Accumulation acc2) {
        if (acc1.getLatitude() == null || acc1.getLongitude() == null ||
            acc2.getLatitude() == null || acc2.getLongitude() == null) {
            return Math.random() * 100; // 随机距离，实际应该使用其他度量
        }
        
        // Haversine公式计算地理距离
        double lat1 = Math.toRadians(acc1.getLatitude());
        double lat2 = Math.toRadians(acc2.getLatitude());
        double deltaLat = Math.toRadians(acc2.getLatitude() - acc1.getLatitude());
        double deltaLng = Math.toRadians(acc2.getLongitude() - acc1.getLongitude());
        
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return 6371.0 * c; // 地球半径6371km
    }
    
    /**
     * 计算时间距离
     */
    private double calculateTimeDistance(Accumulation acc1, Accumulation acc2, 
                                       TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 简化实现：使用到中转站的时间差作为距离度量
        double time1 = calculateAccumulationTravelTime(acc1, depot, timeMatrix);
        double time2 = calculateAccumulationTravelTime(acc2, depot, timeMatrix);
        
        return Math.abs(time1 - time2);
    }
}