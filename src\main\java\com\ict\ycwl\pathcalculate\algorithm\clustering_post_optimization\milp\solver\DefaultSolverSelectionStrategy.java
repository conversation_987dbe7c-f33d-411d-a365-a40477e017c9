package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPVariable;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认求解器选择策略
 * 
 * 基于求解器能力评分、问题特征和性能预期来选择求解器
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0  
 * @since 2025-08-03
 */
@Slf4j
public class DefaultSolverSelectionStrategy implements SolverSelectionStrategy {
    
    @Override
    public MILPSolver selectSolver(List<MILPSolver> candidateSolvers, 
                                  MILPProblem problem, 
                                  SolverParameters parameters) {
        
        if (candidateSolvers.isEmpty()) {
            return null;
        }
        
        if (candidateSolvers.size() == 1) {
            return candidateSolvers.get(0);
        }
        
        // 使用排序逻辑选择第一个（最佳）求解器
        List<MILPSolver> rankedSolvers = rankSolvers(candidateSolvers, problem, parameters);
        return rankedSolvers.get(0);
    }
    
    @Override
    public List<MILPSolver> rankSolvers(List<MILPSolver> candidateSolvers, 
                                       MILPProblem problem, 
                                       SolverParameters parameters) {
        
        log.debug("🎯 对{}个候选求解器进行排序", candidateSolvers.size());
        
        return candidateSolvers.stream()
            .sorted(createSolverComparator(problem, parameters))
            .collect(Collectors.toList());
    }
    
    /**
     * 创建求解器比较器
     */
    private Comparator<MILPSolver> createSolverComparator(MILPProblem problem, SolverParameters parameters) {
        return Comparator
            // 1. 首先按问题适配度排序
            .comparingInt((MILPSolver solver) -> calculateProblemFitScore(solver, problem))
            .reversed()
            // 2. 然后按求解器层级排序
            .thenComparing((MILPSolver solver) -> solver.getCapabilities().getTier())
            // 3. 最后按能力评分排序
            .thenComparingInt((MILPSolver solver) -> solver.getCapabilities().getCapabilityScore())
            .reversed();
    }
    
    /**
     * 计算问题适配度评分
     */
    private int calculateProblemFitScore(MILPSolver solver, MILPProblem problem) {
        SolverCapabilities capabilities = solver.getCapabilities();
        int score = 0;
        
        // 基础适配性检查
        if (!capabilities.canSolve(problem)) {
            return -1000; // 不能求解的求解器得负分
        }
        
        // 问题规模适配性
        score += calculateScaleScore(capabilities, problem);
        
        // 变量类型适配性
        score += calculateVariableTypeScore(capabilities, problem);
        
        // 特殊功能需求
        score += calculateSpecialFeatureScore(capabilities, problem);
        
        log.trace("求解器 {} 适配度评分: {}", solver.getSolverName(), score);
        return score;
    }
    
    /**
     * 计算规模适配评分
     */
    private int calculateScaleScore(SolverCapabilities capabilities, MILPProblem problem) {
        int score = 0;
        
        int variableCount = problem.getVariables().size();
        int constraintCount = problem.getConstraints().size();
        
        // 变量数适配性
        if (capabilities.getMaxVariables() != null) {
            double variableRatio = (double) variableCount / capabilities.getMaxVariables();
            if (variableRatio <= 0.5) {
                score += 20; // 变量数远低于上限
            } else if (variableRatio <= 0.8) {
                score += 10; // 变量数适中
            } else if (variableRatio <= 1.0) {
                score += 0;  // 变量数接近上限
            } else {
                score -= 50; // 超出变量数限制
            }
        }
        
        // 约束数适配性
        if (capabilities.getMaxConstraints() != null) {
            double constraintRatio = (double) constraintCount / capabilities.getMaxConstraints();
            if (constraintRatio <= 0.5) {
                score += 20; // 约束数远低于上限
            } else if (constraintRatio <= 0.8) {
                score += 10; // 约束数适中
            } else if (constraintRatio <= 1.0) {
                score += 0;  // 约束数接近上限
            } else {
                score -= 50; // 超出约束数限制
            }
        }
        
        return score;
    }
    
    /**
     * 计算变量类型适配评分
     */
    private int calculateVariableTypeScore(SolverCapabilities capabilities, MILPProblem problem) {
        int score = 0;
        
        boolean hasIntegerVars = false;
        boolean hasBinaryVars = false;
        boolean hasContinuousVars = false;
        
        for (MILPVariable variable : problem.getVariables().values()) {
            switch (variable.getType()) {
                case INTEGER:
                    hasIntegerVars = true;
                    break;
                case BINARY:
                    hasBinaryVars = true;
                    break;
                case CONTINUOUS:
                    hasContinuousVars = true;
                    break;
            }
        }
        
        // 整数规划需求
        if ((hasIntegerVars || hasBinaryVars) && capabilities.isSupportsIntegerProgramming()) {
            score += 30; // 支持整数规划的求解器获得高分
        } else if ((hasIntegerVars || hasBinaryVars) && !capabilities.isSupportsIntegerProgramming()) {
            score -= 100; // 需要整数规划但不支持的求解器严重扣分
        }
        
        // 纯线性规划的情况
        if (!hasIntegerVars && !hasBinaryVars && hasContinuousVars) {
            // 对于纯线性规划，专业的LP求解器可能更合适
            if (!capabilities.isSupportsIntegerProgramming()) {
                score += 10; // 专业LP求解器小幅加分
            }
        }
        
        return score;
    }
    
    /**
     * 计算特殊功能需求评分
     */
    private int calculateSpecialFeatureScore(SolverCapabilities capabilities, MILPProblem problem) {
        int score = 0;
        
        // 这里可以根据问题的特殊需求给予加分
        // 例如：如果问题需要对偶解、敏感性分析等
        
        // 并行求解能力
        if (capabilities.isSupportsParallel()) {
            score += 5; // 支持并行的求解器小幅加分
        }
        
        // 敏感性分析能力
        if (capabilities.isSupportsSensitivityAnalysis()) {
            score += 3;
        }
        
        // 对偶解能力
        if (capabilities.isSupportsDualSolution()) {
            score += 3;
        }
        
        return score;
    }
}