package com.ict.ycwl.pathcalculate.algorithm.dto;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.Team;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.Map;

/**
 * 路径规划请求数据结构
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PathPlanningRequest {
    
    /**
     * 聚集区列表
     * 包含所有需要进行路径规划的聚集区信息，包括坐标、配送时间、所属中转站等
     */
    private List<Accumulation> accumulations;
    
    /**
     * 中转站列表
     * 包含所有中转站信息，包括坐标、所属班组、规划路线数量等
     */
    private List<TransitDepot> transitDepots;
    
    /**
     * 班组列表（用于均衡计算）
     * 包含班组层级关系，用于多层级时间均衡算法
     */
    private List<Team> teams;
    
    /**
     * 时间矩阵（边权）
     * Key格式："起点经度,起点纬度->终点经度,终点纬度"
     * Value：对应的时间信息，包含行驶时间等
     */
    private Map<String, TimeInfo> timeMatrix;
    
    /**
     * 验证请求数据的完整性和有效性
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        // 基础数据检查
        if (accumulations == null || accumulations.isEmpty() ||
            transitDepots == null || transitDepots.isEmpty() ||
            teams == null || teams.isEmpty() ||
            timeMatrix == null || timeMatrix.isEmpty()) {
            return false;
        }
        
        // 检查各个实体的数据有效性
        for (Accumulation acc : accumulations) {
            if (!acc.isValid()) {
                return false;
            }
        }
        
        for (TransitDepot depot : transitDepots) {
            if (!depot.isValid()) {
                return false;
            }
        }
        
        for (Team team : teams) {
            if (!team.isValid()) {
                return false;
            }
        }
        
        // OPTIMIZE: 可以进一步验证ID关联关系的完整性和时间矩阵的覆盖度
        return true;
    }
    
    /**
     * 获取指定聚集区的时间信息
     * 
     * @param fromLng 起点经度
     * @param fromLat 起点纬度  
     * @param toLng 终点经度
     * @param toLat 终点纬度
     * @return 时间信息，如果不存在则返回null
     */
    public TimeInfo getTimeInfo(Double fromLng, Double fromLat, Double toLng, Double toLat) {
        if (fromLng == null || fromLat == null || toLng == null || toLat == null) {
            return null;
        }
        String key = String.format("%.6f,%.6f->%.6f,%.6f", fromLng, fromLat, toLng, toLat);
        return timeMatrix.get(key);
    }
    
    /**
     * 根据班组ID获取班组信息
     * 
     * @param teamId 班组ID
     * @return 班组信息，如果不存在则返回null
     */
    public Team getTeamById(Long teamId) {
        if (teamId == null || teams == null) {
            return null;
        }
        return teams.stream()
                .filter(team -> team.getTeamId().equals(teamId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据中转站ID获取中转站信息
     * 
     * @param transitDepotId 中转站ID
     * @return 中转站信息，如果不存在则返回null
     */
    public TransitDepot getTransitDepotById(Long transitDepotId) {
        if (transitDepotId == null || transitDepots == null) {
            return null;
        }
        return transitDepots.stream()
                .filter(depot -> depot.getTransitDepotId().equals(transitDepotId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据聚集区ID获取聚集区信息
     * 
     * @param accumulationId 聚集区ID
     * @return 聚集区信息，如果不存在则返回null
     */
    public Accumulation getAccumulationById(Long accumulationId) {
        if (accumulationId == null || accumulations == null) {
            return null;
        }
        return accumulations.stream()
                .filter(acc -> acc.getAccumulationId().equals(accumulationId))
                .findFirst()
                .orElse(null);
    }
} 