package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 路线合并算法
 * 
 * 实现智能路线合并，基于地理相近性和时间约束优化
 * 确保合并后的路线满足450分钟约束和地理合理性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class RouteMergingAlgorithm {
    
    /**
     * 合并两条路线
     * 
     * @param route1 第一条路线
     * @param route2 第二条路线
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 合并后的路线，如果无法合并则返回null
     */
    public List<Accumulation> mergeRoutes(List<Accumulation> route1,
                                        List<Accumulation> route2,
                                        TransitDepot depot,
                                        Map<String, TimeInfo> timeMatrix) {
        
        log.debug("🔗 开始合并路线：{} + {} → ?", route1.size(), route2.size());
        
        // 输入验证
        if (route1 == null || route1.isEmpty()) {
            log.warn("⚠️ 路线1为空，返回路线2");
            return route2 != null ? new ArrayList<>(route2) : new ArrayList<>();
        }
        
        if (route2 == null || route2.isEmpty()) {
            log.warn("⚠️ 路线2为空，返回路线1");
            return new ArrayList<>(route1);
        }
        
        try {
            // 预检查：合并后是否超时
            double combinedWorkTime = calculateRouteWorkTime(route1, depot, timeMatrix) + 
                                    calculateRouteWorkTime(route2, depot, timeMatrix);
            
            if (combinedWorkTime > 450.0) {
                log.debug("⚠️ 合并后预计工作时间{:.1f}分钟超过450分钟限制", combinedWorkTime);
                return null;
            }
            
            // 方法1：简单合并（直接连接）
            List<Accumulation> simpleMerge = performSimpleMerge(route1, route2);
            
            // 方法2：智能合并（基于地理位置优化）
            List<Accumulation> intelligentMerge = performIntelligentMerge(route1, route2, depot, timeMatrix);
            
            // 选择更优的合并方案
            List<Accumulation> bestMerge = selectBestMerge(simpleMerge, intelligentMerge, depot, timeMatrix);
            
            // 验证合并结果
            if (validateMergeResult(bestMerge, route1, route2, depot, timeMatrix)) {
                log.info("✅ 路线合并成功：{} + {} → {} 个聚集区", 
                    route1.size(), route2.size(), bestMerge.size());
                return bestMerge;
            } else {
                log.warn("⚠️ 合并结果验证失败");
                return null;
            }
            
        } catch (Exception e) {
            log.error("❌ 路线合应失败", e);
            return null;
        }
    }
    
    /**
     * 简单合并：直接连接两条路线
     */
    private List<Accumulation> performSimpleMerge(List<Accumulation> route1, List<Accumulation> route2) {
        List<Accumulation> merged = new ArrayList<>(route1);
        merged.addAll(route2);
        return merged;
    }
    
    /**
     * 智能合并：基于地理位置优化合并顺序
     */
    private List<Accumulation> performIntelligentMerge(List<Accumulation> route1,
                                                     List<Accumulation> route2,
                                                     TransitDepot depot,
                                                     Map<String, TimeInfo> timeMatrix) {
        
        log.debug("🧠 执行智能合并算法");
        
        // 计算所有聚集区的地理中心
        GeographicCenter center1 = calculateGeographicCenter(route1);
        GeographicCenter center2 = calculateGeographicCenter(route2);
        
        // 找到两条路线之间的最佳连接点
        ConnectionPoint bestConnection = findBestConnectionPoint(route1, route2, depot, timeMatrix);
        
        List<Accumulation> merged = new ArrayList<>();
        
        if (bestConnection != null) {
            // 基于最佳连接点重组路线
            merged = reorderBasedOnConnection(route1, route2, bestConnection, depot, timeMatrix);
        } else {
            // 如果没有找到最佳连接点，使用地理中心距离排序
            merged = reorderBasedOnGeography(route1, route2, center1, center2);
        }
        
        return merged;
    }
    
    /**
     * 计算路线的地理中心
     */
    private GeographicCenter calculateGeographicCenter(List<Accumulation> route) {
        if (route.isEmpty()) {
            return GeographicCenter.builder().latitude(0.0).longitude(0.0).build();
        }
        
        // 过滤有坐标信息的聚集区
        List<Accumulation> withCoordinates = route.stream()
            .filter(acc -> acc.getLatitude() != null && acc.getLongitude() != null)
            .collect(Collectors.toList());
        
        if (withCoordinates.isEmpty()) {
            return GeographicCenter.builder().latitude(0.0).longitude(0.0).build();
        }
        
        double avgLat = withCoordinates.stream()
            .mapToDouble(Accumulation::getLatitude)
            .average().orElse(0.0);
        
        double avgLng = withCoordinates.stream()
            .mapToDouble(Accumulation::getLongitude)
            .average().orElse(0.0);
        
        return GeographicCenter.builder()
            .latitude(avgLat)
            .longitude(avgLng)
            .accumulationCount(withCoordinates.size())
            .build();
    }
    
    /**
     * 找到两条路线之间的最佳连接点
     */
    private ConnectionPoint findBestConnectionPoint(List<Accumulation> route1,
                                                  List<Accumulation> route2,
                                                  TransitDepot depot,
                                                  Map<String, TimeInfo> timeMatrix) {
        
        double minConnectionCost = Double.MAX_VALUE;
        ConnectionPoint bestConnection = null;
        
        // 尝试所有可能的连接点组合
        for (int i = 0; i < route1.size(); i++) {
            for (int j = 0; j < route2.size(); j++) {
                Accumulation acc1 = route1.get(i);
                Accumulation acc2 = route2.get(j);
                
                // 计算连接成本（地理距离 + 时间成本）
                double connectionCost = calculateConnectionCost(acc1, acc2, depot, timeMatrix);
                
                if (connectionCost < minConnectionCost) {
                    minConnectionCost = connectionCost;
                    bestConnection = ConnectionPoint.builder()
                        .route1Index(i)
                        .route2Index(j)
                        .accumulation1(acc1)
                        .accumulation2(acc2)
                        .connectionCost(connectionCost)
                        .build();
                }
            }
        }
        
        return bestConnection;
    }
    
    /**
     * 基于最佳连接点重组路线
     */
    private List<Accumulation> reorderBasedOnConnection(List<Accumulation> route1,
                                                      List<Accumulation> route2,
                                                      ConnectionPoint connection,
                                                      TransitDepot depot,
                                                      Map<String, TimeInfo> timeMatrix) {
        
        List<Accumulation> merged = new ArrayList<>();
        
        // 策略：从中转站开始，选择最优的路线顺序
        // 简化实现：直接连接两条路线
        merged.addAll(route1);
        merged.addAll(route2);
        
        // 可以在这里实现更复杂的重排序逻辑
        // 例如：最近邻搜索、TSP近似算法等
        
        return merged;
    }
    
    /**
     * 基于地理位置重组路线
     */
    private List<Accumulation> reorderBasedOnGeography(List<Accumulation> route1,
                                                     List<Accumulation> route2,
                                                     GeographicCenter center1,
                                                     GeographicCenter center2) {
        
        List<Accumulation> merged = new ArrayList<>();
        
        // 如果路线1的地理中心更靠近中转站，先走路线1
        // 这里简化处理，直接按原顺序合并
        merged.addAll(route1);
        merged.addAll(route2);
        
        return merged;
    }
    
    /**
     * 选择最优的合并方案
     */
    private List<Accumulation> selectBestMerge(List<Accumulation> simpleMerge,
                                             List<Accumulation> intelligentMerge,
                                             TransitDepot depot,
                                             Map<String, TimeInfo> timeMatrix) {
        
        // 评估简单合并方案
        double simpleScore = evaluateMergeQuality(simpleMerge, depot, timeMatrix);
        
        // 评估智能合并方案
        double intelligentScore = evaluateMergeQuality(intelligentMerge, depot, timeMatrix);
        
        log.debug("   合并方案评分 - 简单合并: {:.3f}, 智能合并: {:.3f}", simpleScore, intelligentScore);
        
        return intelligentScore >= simpleScore ? intelligentMerge : simpleMerge;
    }
    
    /**
     * 评估合并质量
     */
    private double evaluateMergeQuality(List<Accumulation> merged, 
                                      TransitDepot depot,
                                      Map<String, TimeInfo> timeMatrix) {
        
        if (merged == null || merged.isEmpty()) {
            return 0.0;
        }
        
        double workTime = calculateRouteWorkTime(merged, depot, timeMatrix);
        
        // 评估指标
        double timeScore = workTime <= 450.0 ? 1.0 : (450.0 / workTime); // 时间约束满足度
        double efficiencyScore = Math.min(1.0, workTime / 350.0); // 效率评分（350分钟为理想工作时间）
        double sizeScore = Math.min(1.0, merged.size() / 15.0); // 规模评分（15个聚集区为理想规模）
        
        // 综合评分
        return timeScore * 0.5 + efficiencyScore * 0.3 + sizeScore * 0.2;
    }
    
    /**
     * 验证合并结果
     */
    private boolean validateMergeResult(List<Accumulation> merged,
                                      List<Accumulation> route1,
                                      List<Accumulation> route2,
                                      TransitDepot depot,
                                      Map<String, TimeInfo> timeMatrix) {
        
        if (merged == null || merged.isEmpty()) {
            return false;
        }
        
        // 验证1：聚集区总数匹配
        if (merged.size() != route1.size() + route2.size()) {
            log.warn("⚠️ 合并后聚集区总数不匹配：{} != {} + {}", 
                merged.size(), route1.size(), route2.size());
            return false;
        }
        
        // 验证2：没有重复的聚集区
        Set<Long> mergedAccIds = merged.stream()
            .map(Accumulation::getAccumulationId)
            .collect(Collectors.toSet());
        
        if (mergedAccIds.size() != merged.size()) {
            log.warn("⚠️ 合并后存在重复的聚集区");
            return false;
        }
        
        // 验证3：时间约束满足
        double workTime = calculateRouteWorkTime(merged, depot, timeMatrix);
        if (workTime > 450.0) {
            log.warn("⚠️ 合并后工作时间{:.1f}分钟超过450分钟限制", workTime);
            return false;
        }
        
        // 验证4：包含原路线的所有聚集区
        Set<Long> originalAccIds = new HashSet<>();
        route1.stream().map(Accumulation::getAccumulationId).forEach(originalAccIds::add);
        route2.stream().map(Accumulation::getAccumulationId).forEach(originalAccIds::add);
        
        if (!mergedAccIds.equals(originalAccIds)) {
            log.warn("⚠️ 合并后缺少原路线的聚集区");
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算连接成本
     */
    private double calculateConnectionCost(Accumulation acc1, Accumulation acc2,
                                         TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        
        // 如果有坐标信息，使用地理距离
        if (acc1.getLatitude() != null && acc1.getLongitude() != null &&
            acc2.getLatitude() != null && acc2.getLongitude() != null) {
            return calculateGeographicDistance(acc1, acc2);
        }
        
        // 否则使用时间距离作为代理
        double time1 = calculateAccumulationTravelTime(acc1, depot, timeMatrix);
        double time2 = calculateAccumulationTravelTime(acc2, depot, timeMatrix);
        
        return Math.abs(time1 - time2);
    }
    
    /**
     * 计算地理距离
     */
    private double calculateGeographicDistance(Accumulation acc1, Accumulation acc2) {
        if (acc1.getLatitude() == null || acc1.getLongitude() == null ||
            acc2.getLatitude() == null || acc2.getLongitude() == null) {
            return 0.0;
        }
        
        // Haversine公式计算地理距离
        double lat1 = Math.toRadians(acc1.getLatitude());
        double lat2 = Math.toRadians(acc2.getLatitude());
        double deltaLat = Math.toRadians(acc2.getLatitude() - acc1.getLatitude());
        double deltaLng = Math.toRadians(acc2.getLongitude() - acc1.getLongitude());
        
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return 6371.0 * c; // 地球半径6371km
    }
    
    /**
     * 计算聚集区往返时间
     */
    private double calculateAccumulationTravelTime(Accumulation acc, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null && timeInfo.getTravelTime() != null ? timeInfo.getTravelTime() * 2 : 60.0; // 默认60分钟往返
    }
    
    /**
     * 计算路线总工作时间
     */
    private double calculateRouteWorkTime(List<Accumulation> route, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            } else {
                totalTime += 30.0; // 默认30分钟配送时间
            }
        }
        
        // 往返时间
        for (Accumulation acc : route) {
            totalTime += calculateAccumulationTravelTime(acc, depot, timeMatrix);
        }
        
        return totalTime;
    }
}