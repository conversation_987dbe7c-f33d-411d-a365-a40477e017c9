package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * OptaPlanner约束权重配置
 * 
 * 定义各种约束的权重，用于平衡硬约束和软约束的重要性
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConstraintWeights {
    
    // ========== 硬约束权重 ==========
    
    /**
     * 450分钟工作时间硬约束权重
     * 默认: 100000 (极高权重，确保不违反)
     */
    @Builder.Default
    private int maxWorkTimeConstraintWeight = 100000;
    
    /**
     * 30分钟时间差异硬约束权重
     * 默认: 50000 (高权重，但低于450分钟约束)
     */
    @Builder.Default
    private int timeGapConstraintWeight = 50000;
    
    /**
     * 聚集区必须分配约束权重
     * 默认: 200000 (最高权重，确保所有聚集区都被分配)
     */
    @Builder.Default
    private int assignmentConstraintWeight = 200000;
    
    // ========== 软约束权重 ==========
    
    /**
     * 地理紧凑性软约束权重
     * 默认: 1000 (中等权重)
     */
    @Builder.Default
    private int geographicCompactnessWeight = 1000;
    
    /**
     * 工作量平衡软约束权重
     * 默认: 2000 (较高权重，时间平衡很重要)
     */
    @Builder.Default
    private int workloadBalanceWeight = 2000;
    
    /**
     * 聚类大小均匀性软约束权重
     * 默认: 500 (较低权重)
     */
    @Builder.Default
    private int clusterSizeUniformityWeight = 500;
    
    /**
     * 最小化聚类间时间差异软约束权重
     * 默认: 1500 (中高权重)
     */
    @Builder.Default
    private int minimizeTimeDifferenceWeight = 1500;
    
    // ========== 优化目标权重 ==========
    
    /**
     * 总工作时间最小化权重
     * 默认: 300 (较低权重，作为次要优化目标)
     */
    @Builder.Default
    private int totalWorkTimeMinimizationWeight = 300;
    
    /**
     * 地理中心优化权重
     * 默认: 200 (低权重，锦上添花)
     */
    @Builder.Default
    private int geographicCenterOptimizationWeight = 200;
    
    /**
     * 创建默认约束权重配置
     * 
     * @return 默认配置的约束权重
     */
    public static ConstraintWeights createDefault() {
        return ConstraintWeights.builder().build();
    }
    
    /**
     * 创建严格约束权重配置（提高硬约束权重）
     * 
     * @return 严格约束的权重配置
     */
    public static ConstraintWeights createStrict() {
        return ConstraintWeights.builder()
            .maxWorkTimeConstraintWeight(500000)
            .timeGapConstraintWeight(200000)
            .assignmentConstraintWeight(1000000)
            .workloadBalanceWeight(3000)
            .geographicCompactnessWeight(800)
            .build();
    }
    
    /**
     * 创建平衡权重配置（降低硬约束权重，提高软约束权重）
     * 
     * @return 平衡的权重配置
     */
    public static ConstraintWeights createBalanced() {
        return ConstraintWeights.builder()
            .maxWorkTimeConstraintWeight(50000)
            .timeGapConstraintWeight(25000)
            .assignmentConstraintWeight(100000)
            .workloadBalanceWeight(5000)
            .geographicCompactnessWeight(3000)
            .minimizeTimeDifferenceWeight(4000)
            .build();
    }
    
    /**
     * 创建地理优先权重配置
     * 
     * @return 地理优先的权重配置
     */
    public static ConstraintWeights createGeographyFirst() {
        return ConstraintWeights.builder()
            .geographicCompactnessWeight(5000)
            .geographicCenterOptimizationWeight(2000)
            .workloadBalanceWeight(1000)
            .build();
    }
    
    /**
     * 创建时间优先权重配置
     * 
     * @return 时间优先的权重配置
     */
    public static ConstraintWeights createTimeFirst() {
        return ConstraintWeights.builder()
            .maxWorkTimeConstraintWeight(200000)
            .timeGapConstraintWeight(100000)
            .workloadBalanceWeight(8000)
            .minimizeTimeDifferenceWeight(6000)
            .geographicCompactnessWeight(500)
            .build();
    }
    
    /**
     * 验证权重配置是否合理
     * 
     * @return true如果权重配置合理
     */
    public boolean isValid() {
        // 硬约束权重必须远大于软约束权重
        int maxSoftWeight = Math.max(
            Math.max(geographicCompactnessWeight, workloadBalanceWeight),
            Math.max(clusterSizeUniformityWeight, minimizeTimeDifferenceWeight)
        );
        
        int minHardWeight = Math.min(
            Math.min(maxWorkTimeConstraintWeight, timeGapConstraintWeight),
            assignmentConstraintWeight
        );
        
        // 硬约束权重至少应该是软约束权重的10倍
        return minHardWeight >= maxSoftWeight * 10;
    }
    
    /**
     * 获取权重配置摘要
     * 
     * @return 权重配置的可读描述
     */
    public String getSummary() {
        return String.format(
            "约束权重配置 - 硬约束[分配:%d, 450分钟:%d, 30分钟差异:%d], " +
            "软约束[负载均衡:%d, 地理紧凑:%d, 时间差异:%d]",
            assignmentConstraintWeight,
            maxWorkTimeConstraintWeight,
            timeGapConstraintWeight,
            workloadBalanceWeight,
            geographicCompactnessWeight,
            minimizeTimeDifferenceWeight
        );
    }
    
    /**
     * 深拷贝约束权重
     * 
     * @return 深拷贝的约束权重对象
     */
    public ConstraintWeights copy() {
        return ConstraintWeights.builder()
            .maxWorkTimeConstraintWeight(this.maxWorkTimeConstraintWeight)
            .timeGapConstraintWeight(this.timeGapConstraintWeight)
            .assignmentConstraintWeight(this.assignmentConstraintWeight)
            .geographicCompactnessWeight(this.geographicCompactnessWeight)
            .workloadBalanceWeight(this.workloadBalanceWeight)
            .clusterSizeUniformityWeight(this.clusterSizeUniformityWeight)
            .minimizeTimeDifferenceWeight(this.minimizeTimeDifferenceWeight)
            .totalWorkTimeMinimizationWeight(this.totalWorkTimeMinimizationWeight)
            .geographicCenterOptimizationWeight(this.geographicCenterOptimizationWeight)
            .build();
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}