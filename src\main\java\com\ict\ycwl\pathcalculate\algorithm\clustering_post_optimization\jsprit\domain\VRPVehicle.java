package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * JSPRIT VRP车辆模型
 * 
 * 表示物流配送中的一辆车辆，包含容量约束、时间约束等属性
 * 车辆容量使用时间维度建模（450分钟工作时间上限）
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPVehicle {
    
    /**
     * 车辆唯一标识
     */
    private String vehicleId;
    
    /**
     * 车辆类型标识
     */
    private String vehicleTypeId;
    
    /**
     * 起始位置（中转站）
     */
    private VRPLocation startLocation;
    
    /**
     * 结束位置（中转站）
     */
    private VRPLocation endLocation;
    
    /**
     * 车辆时间容量（分钟）
     * 默认450分钟工作时间上限
     */
    private Double timeCapacityMinutes;
    
    /**
     * 车辆最早出发时间（分钟，相对于工作日开始）
     */
    private Double earliestStartTime;
    
    /**
     * 车辆最晚返回时间（分钟，相对于工作日开始）
     */
    private Double latestEndTime;
    
    /**
     * 是否返回起始位置
     */
    private Boolean returnToDepot;
    
    /**
     * 所属中转站
     */
    private TransitDepot assignedDepot;
    
    /**
     * 车辆成本系数
     */
    private Double costPerDistance;
    
    /**
     * 车辆时间成本系数
     */
    private Double costPerTime;
    
    /**
     * 固定车辆成本
     */
    private Double fixedCost;
    
    /**
     * 创建标准配送车辆
     * 
     * @param vehicleId 车辆ID
     * @param depot 中转站
     * @return 标准配送车辆
     */
    public static VRPVehicle createStandardDeliveryVehicle(String vehicleId, TransitDepot depot) {
        VRPLocation depotLocation = VRPLocation.fromTransitDepot(depot);
        
        return VRPVehicle.builder()
            .vehicleId(vehicleId)
            .vehicleTypeId("standard_delivery_vehicle")
            .startLocation(depotLocation)
            .endLocation(depotLocation)
            .timeCapacityMinutes(450.0) // 450分钟工作时间上限
            .earliestStartTime(0.0) // 工作日开始
            .latestEndTime(480.0) // 允许8小时工作日
            .returnToDepot(true)
            .assignedDepot(depot)
            .costPerDistance(1.0)
            .costPerTime(1.0) // 时间成本较高，鼓励时间平衡
            .fixedCost(100.0)
            .build();
    }
    
    /**
     * 创建时间平衡优化车辆
     * 
     * @param vehicleId 车辆ID
     * @param depot 中转站
     * @param maxWorkTimeMinutes 最大工作时间（分钟）
     * @return 时间平衡优化车辆
     */
    public static VRPVehicle createTimeBalancedVehicle(
        String vehicleId, 
        TransitDepot depot, 
        Double maxWorkTimeMinutes
    ) {
        VRPLocation depotLocation = VRPLocation.fromTransitDepot(depot);
        
        return VRPVehicle.builder()
            .vehicleId(vehicleId)
            .vehicleTypeId("time_balanced_vehicle")
            .startLocation(depotLocation)
            .endLocation(depotLocation)
            .timeCapacityMinutes(maxWorkTimeMinutes)
            .earliestStartTime(0.0)
            .latestEndTime(maxWorkTimeMinutes + 30.0) // 留出缓冲时间
            .returnToDepot(true)
            .assignedDepot(depot)
            .costPerDistance(0.5) // 降低距离成本权重
            .costPerTime(2.0) // 提高时间成本权重，强调时间平衡
            .fixedCost(50.0)
            .build();
    }
    
    /**
     * 验证车辆配置
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return vehicleId != null && !vehicleId.trim().isEmpty()
            && startLocation != null
            && endLocation != null
            && timeCapacityMinutes != null && timeCapacityMinutes > 0
            && earliestStartTime != null && earliestStartTime >= 0
            && latestEndTime != null && latestEndTime > earliestStartTime
            && assignedDepot != null;
    }
    
    /**
     * 获取有效工作时间窗口（分钟）
     * 
     * @return 有效工作时间窗口
     */
    public Double getEffectiveWorkTimeWindow() {
        if (latestEndTime == null || earliestStartTime == null) {
            return timeCapacityMinutes;
        }
        return Math.min(timeCapacityMinutes, latestEndTime - earliestStartTime);
    }
    
    /**
     * 检查是否与指定中转站匹配
     * 
     * @param depot 中转站
     * @return 是否匹配
     */
    public boolean matchesDepot(TransitDepot depot) {
        return assignedDepot != null 
            && depot != null 
            && assignedDepot.getTransitDepotId().equals(depot.getTransitDepotId());
    }
    
    @Override
    public String toString() {
        return String.format("VRPVehicle{id='%s', type='%s', depot='%s', timeCapacity=%.1fmin, timeWindow=[%.1f,%.1f]}", 
            vehicleId, vehicleTypeId, 
            assignedDepot != null ? assignedDepot.getTransitDepotId() : "null",
            timeCapacityMinutes, earliestStartTime, latestEndTime);
    }
}