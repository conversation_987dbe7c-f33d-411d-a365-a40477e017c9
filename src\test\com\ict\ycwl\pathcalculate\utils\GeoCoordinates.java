package com.ict.ycwl.pathcalculate.utils;

import com.ict.ycwl.pathcalculate.entity.PolarCoordinates;

public class GeoCoordinates {

    // 地球平均半径（单位：千米）
    private static final double EARTH_RADIUS = 6371.0;

    public static PolarCoordinates get(double sourceLat,double sourceLon,double targetLat,double targetLon){
        PolarCoordinates polarCoordinates = new PolarCoordinates();
        // 计算大圆距离和方位角
        double distance = calculateHaversineDistance(sourceLat, sourceLon, targetLat, targetLon);
        double bearing = calculateInitialBearing(sourceLat, sourceLon, targetLat, targetLon);
        polarCoordinates.setDist(distance);
        polarCoordinates.setAzimuth(bearing);
        return polarCoordinates;
    }

    // 计算大圆距离（Haversine公式）
    private static double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        lat1 = Math.toRadians(lat1);
        lat2 = Math.toRadians(lat2);

        double a = Math.pow(Math.sin(dLat / 2), 2) 
                 + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dLon / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c;
    }

    // 计算初始方位角
    private static double calculateInitialBearing(double lat1, double lon1, double lat2, double lon2) {
        double phi1 = Math.toRadians(lat1);
        double phi2 = Math.toRadians(lat2);
        double lambda1 = Math.toRadians(lon1);
        double lambda2 = Math.toRadians(lon2);

        double y = Math.sin(lambda2 - lambda1) * Math.cos(phi2);
        double x = Math.cos(phi1) * Math.sin(phi2) 
                 - Math.sin(phi1) * Math.cos(phi2) * Math.cos(lambda2 - lambda1);
        double theta = Math.atan2(y, x);

        // 将弧度转换为度数并调整到0-360范围
        double bearing = (Math.toDegrees(theta) + 360) % 360;
        return bearing;
    }
}