package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * JSPRIT VRP问题模型
 * 
 * 表示完整的车辆路径规划问题，包含所有车辆、服务点、约束条件等
 * 用于JSPRIT求解器的输入数据结构
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPProblem {
    
    /**
     * 问题唯一标识
     */
    private String problemId;
    
    /**
     * 问题名称
     */
    private String problemName;
    
    /**
     * 中转站（车辆起始/结束位置）
     */
    private TransitDepot depot;
    
    /**
     * 车辆列表
     */
    private List<VRPVehicle> vehicles;
    
    /**
     * 服务列表
     */
    private List<VRPService> services;
    
    /**
     * 时间矩阵
     */
    private Map<String, TimeInfo> timeMatrix;
    
    /**
     * 问题约束配置
     */
    private VRPConstraints constraints;
    
    /**
     * 问题统计信息
     */
    private VRPProblemStatistics statistics;
    
    /**
     * 从聚类数据创建VRP问题
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return VRP问题
     */
    public static VRPProblem fromClusters(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        if (depot == null) {
            throw new IllegalArgumentException("中转站不能为空");
        }
        if (clusters == null || clusters.isEmpty()) {
            throw new IllegalArgumentException("聚类结果不能为空");
        }
        
        String problemId = "vrp_" + depot.getTransitDepotId() + "_" + System.currentTimeMillis();
        
        // 创建车辆列表（每个聚类对应一辆车）
        List<VRPVehicle> vehicles = createVehiclesFromClusters(depot, clusters);
        
        // 创建服务列表（每个聚集区对应一个服务）
        List<VRPService> services = createServicesFromClusters(depot, clusters, timeMatrix);
        
        // 创建约束配置
        VRPConstraints constraints = VRPConstraints.createDefaultConstraints();
        
        // 计算问题统计信息
        VRPProblemStatistics statistics = VRPProblemStatistics.calculateFrom(vehicles, services);
        
        return VRPProblem.builder()
            .problemId(problemId)
            .problemName("中转站 " + depot.getTransitDepotName() + " VRP优化")
            .depot(depot)
            .vehicles(vehicles)
            .services(services)
            .timeMatrix(timeMatrix)
            .constraints(constraints)
            .statistics(statistics)
            .build();
    }
    
    /**
     * 从聚类创建车辆列表
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @return 车辆列表
     */
    private static List<VRPVehicle> createVehiclesFromClusters(
        TransitDepot depot,
        List<List<Accumulation>> clusters
    ) {
        List<VRPVehicle> vehicles = new ArrayList<>();
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            if (!cluster.isEmpty()) {
                String vehicleId = "vehicle_" + depot.getTransitDepotId() + "_" + i;
                
                // 根据聚类工作量调整车辆容量
                Double clusterWorkTime = cluster.stream()
                    .mapToDouble(acc -> acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 30.0)
                    .sum();
                
                // 动态调整车辆容量（但不超过450分钟硬约束）
                Double vehicleCapacity = Math.min(450.0, Math.max(clusterWorkTime * 1.1, 300.0));
                
                VRPVehicle vehicle = VRPVehicle.createTimeBalancedVehicle(vehicleId, depot, vehicleCapacity);
                vehicles.add(vehicle);
            }
        }
        
        return vehicles;
    }
    
    /**
     * 从聚类创建服务列表
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 服务列表
     */
    private static List<VRPService> createServicesFromClusters(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        List<VRPService> services = new ArrayList<>();
        
        for (List<Accumulation> cluster : clusters) {
            for (Accumulation accumulation : cluster) {
                VRPService service = VRPService.fromAccumulation(accumulation, depot, timeMatrix);
                services.add(service);
            }
        }
        
        return services;
    }
    
    /**
     * 验证问题配置
     * 
     * @return 验证结果
     */
    public VRPValidationResult validate() {
        VRPValidationResult.VRPValidationResultBuilder resultBuilder = VRPValidationResult.builder()
            .isValid(true);
        
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 验证基本字段
        if (problemId == null || problemId.trim().isEmpty()) {
            errors.add("问题ID不能为空");
        }
        
        if (depot == null) {
            errors.add("中转站不能为空");
        }
        
        if (vehicles == null || vehicles.isEmpty()) {
            errors.add("车辆列表不能为空");
        } else {
            // 验证车辆配置
            for (int i = 0; i < vehicles.size(); i++) {
                VRPVehicle vehicle = vehicles.get(i);
                if (!vehicle.isValid()) {
                    errors.add("车辆 " + i + " 配置无效: " + vehicle.getVehicleId());
                }
                if (!vehicle.matchesDepot(depot)) {
                    errors.add("车辆 " + i + " 与中转站不匹配: " + vehicle.getVehicleId());
                }
            }
        }
        
        if (services == null || services.isEmpty()) {
            errors.add("服务列表不能为空");
        } else {
            // 验证服务配置
            for (int i = 0; i < services.size(); i++) {
                VRPService service = services.get(i);
                if (!service.isValid()) {
                    errors.add("服务 " + i + " 配置无效: " + service.getServiceId());
                }
                if (!service.belongsToDepot(String.valueOf(depot.getTransitDepotId()))) {
                    errors.add("服务 " + i + " 与中转站不匹配: " + service.getServiceId());
                }
            }
        }
        
        // 验证约束配置
        if (constraints == null) {
            warnings.add("缺少约束配置，将使用默认约束");
            constraints = VRPConstraints.createDefaultConstraints();
        }
        
        // 检查车辆容量与服务需求的匹配性
        if (vehicles != null && services != null) {
            double totalDemand = services.stream()
                .mapToDouble(service -> service.getDemandTimeMinutes() != null ? service.getDemandTimeMinutes() : 0.0)
                .sum();
            
            double totalCapacity = vehicles.stream()
                .mapToDouble(vehicle -> vehicle.getTimeCapacityMinutes() != null ? vehicle.getTimeCapacityMinutes() : 0.0)
                .sum();
            
            if (totalDemand > totalCapacity * 1.1) { // 允许10%的容差
                warnings.add(String.format("总需求(%.1f分钟)可能超过总容量(%.1f分钟)", totalDemand, totalCapacity));
            }
        }
        
        boolean isValid = errors.isEmpty();
        
        return resultBuilder
            .isValid(isValid)
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 获取所有服务的总需求时间
     * 
     * @return 总需求时间（分钟）
     */
    public Double getTotalDemandTime() {
        if (services == null) {
            return 0.0;
        }
        return services.stream()
            .mapToDouble(service -> service.getDemandTimeMinutes() != null ? service.getDemandTimeMinutes() : 0.0)
            .sum();
    }
    
    /**
     * 获取所有车辆的总容量
     * 
     * @return 总容量（分钟）
     */
    public Double getTotalVehicleCapacity() {
        if (vehicles == null) {
            return 0.0;
        }
        return vehicles.stream()
            .mapToDouble(vehicle -> vehicle.getTimeCapacityMinutes() != null ? vehicle.getTimeCapacityMinutes() : 0.0)
            .sum();
    }
    
    /**
     * 获取属于指定车辆的服务列表
     * 
     * @param vehicleId 车辆ID
     * @return 服务列表
     */
    public List<VRPService> getServicesForVehicle(String vehicleId) {
        if (services == null || vehicleId == null) {
            return new ArrayList<>();
        }
        
        // 这里需要根据实际的车辆-服务分配逻辑来实现
        // 当前返回所有服务，实际使用时可能需要根据求解结果来确定
        return new ArrayList<>(services);
    }
    
    /**
     * 检查问题是否包含时间窗口约束
     * 
     * @return 是否包含时间窗口约束
     */
    public boolean hasTimeWindowConstraints() {
        if (services == null) {
            return false;
        }
        
        return services.stream()
            .anyMatch(service -> service.getEarliestStartTime() != null || service.getLatestStartTime() != null);
    }
    
    /**
     * 获取问题规模描述
     * 
     * @return 规模描述
     */
    public String getProblemSizeDescription() {
        int vehicleCount = vehicles != null ? vehicles.size() : 0;
        int serviceCount = services != null ? services.size() : 0;
        
        return String.format("车辆数: %d, 服务点数: %d, 总需求: %.1f分钟, 总容量: %.1f分钟", 
            vehicleCount, serviceCount, getTotalDemandTime(), getTotalVehicleCapacity());
    }
    
    @Override
    public String toString() {
        return String.format("VRPProblem{id='%s', depot='%s', %s}", 
            problemId, 
            depot != null ? depot.getTransitDepotId() : "null",
            getProblemSizeDescription());
    }
}