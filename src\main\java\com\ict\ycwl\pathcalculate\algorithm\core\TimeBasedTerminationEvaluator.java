package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 时间基础终止评估器
 * 
 * 核心功能：基于时间成本的智能终止决策
 * 替代原有的简单点数限制机制，实现：
 * - 多维度时间评估：配送时间 + 行驶时间 + 往返时间
 * - 智能决策逻辑：立即停止/谨慎停止/谨慎继续/继续合并
 * - 参数化配置：上限时间、可动区间、行驶速度等
 * - 详细日志记录：完整的决策过程追踪
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Slf4j
public class TimeBasedTerminationEvaluator {
    
    private final TimeEvaluationConfig config;
    private final RouteTimeCalculator timeCalculator;
    
    public TimeBasedTerminationEvaluator(TimeEvaluationConfig config, RouteTimeCalculator timeCalculator) {
        this.config = config;
        this.timeCalculator = timeCalculator;
        
        // 验证并打印配置
        if (config.validateConfig()) {
            config.logConfig();
            log.info("✅ TimeBasedTerminationEvaluator初始化完成");
        } else {
            log.error("❌ TimeBasedTerminationEvaluator初始化失败：配置验证不通过");
        }
    }
    
    // ===================== 主要评估接口 =====================
    
    /**
     * 主要评估接口 - 判断是否应该停止添加新地块
     * 
     * @param currentRoute 当前路线的地块列表
     * @param candidateBlock 候选要添加的新地块
     * @param depot 中转站信息
     * @return 终止决策结果
     */
    public TerminationDecision shouldTerminate(
            List<List<Accumulation>> currentRoute,
            List<Accumulation> candidateBlock,
            TransitDepot depot) {
        
        long startTime = System.currentTimeMillis();
        
        log.info("🔍 开始时间评估决策: 当前{}个地块 候选地块{}个点", 
            currentRoute.size(), candidateBlock.size());
        
        try {
            // 1. 时间评估
            TimeEvaluationResult timeResult = timeCalculator.evaluateAddBlock(
                currentRoute, candidateBlock, depot);
            
            // 2. 决策分析
            TerminationDecision decision = makeTerminationDecision(timeResult, currentRoute, candidateBlock);
            
            // 3. 记录决策详情
            long elapsedTime = System.currentTimeMillis() - startTime;
            log.info("🎯 决策完成: {} (耗时{}ms)", decision.getShortDescription(), elapsedTime);
            
            // 4. 详细日志（debug级别）
            if (log.isDebugEnabled()) {
                log.debug("📊 详细评估结果:\n{}", timeResult.getFormattedReport());
                log.debug("🎯 详细决策信息:\n{}", decision.getFormattedReport());
            }
            
            return decision;
            
        } catch (Exception e) {
            log.error("❌ 时间评估决策异常", e);
            // 异常情况下返回保守的停止决策
            return TerminationDecision.cautiousStop("评估过程发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 简化评估接口 - 仅评估当前路线是否已达限制
     * 
     * @param currentRoute 当前路线的地块列表
     * @param depot 中转站信息
     * @return 终止决策结果
     */
    public TerminationDecision evaluateCurrentRoute(
            List<List<Accumulation>> currentRoute,
            TransitDepot depot) {
        
        return shouldTerminate(currentRoute, Collections.emptyList(), depot);
    }
    
    // ===================== 核心决策逻辑 =====================
    
    /**
     * 综合决策逻辑
     * 基于时间评估结果做出智能终止决策
     */
    private TerminationDecision makeTerminationDecision(
            TimeEvaluationResult timeResult,
            List<List<Accumulation>> currentRoute,
            List<Accumulation> candidateBlock) {
        
        double totalTimeHours = timeResult.getTotalTimeHours();
        double timeUtilization = timeResult.getTimeUtilizationRatio();
        double efficiencyScore = timeResult.getEfficiencyScore();
        
        // 构建决策指标
        Map<String, Object> metrics = buildDecisionMetrics(timeResult, currentRoute, candidateBlock);
        
        log.debug("📈 决策指标: 总时间{}h 利用率{}%% 效率{}/100", 
            String.format("%.1f", totalTimeHours), 
            String.format("%.1f", timeUtilization * 100), 
            String.format("%.1f", efficiencyScore));
        
        // 决策逻辑分支
        if (timeUtilization >= config.getImmediateStopThreshold()) {
            return createImmediateStopDecision(timeResult, metrics);
            
        } else if (timeUtilization >= config.getCautionThreshold()) {
            return createCautiousDecision(timeResult, metrics);
            
        } else if (timeUtilization >= config.getOptimalThreshold()) {
            return createOptimalDecision(timeResult, metrics);
            
        } else {
            return createContinueDecision(timeResult, metrics);
        }
    }
    
    /**
     * 创建立即停止决策
     */
    private TerminationDecision createImmediateStopDecision(
            TimeEvaluationResult timeResult, 
            Map<String, Object> metrics) {
        
        String reason = String.format("超过时间上限：预估%.1f小时 > 限制%.1f小时", 
            timeResult.getTotalTimeHours(), 
            config.getImmediateStopTimeThreshold());
        
        TerminationDecision decision = TerminationDecision.immediateStop(reason, metrics);
        decision.addContext("trigger", "time_limit_exceeded");
        decision.addContext("severity", "critical");
        
        log.info("⛔ 立即停止: 时间利用率{}%% > 阈值{}%%", 
            String.format("%.1f", timeResult.getTimeUtilizationPercentage()), 
            String.format("%.1f", config.getImmediateStopThreshold() * 100));
        
        return decision;
    }
    
    /**
     * 创建谨慎模式决策
     */
    private TerminationDecision createCautiousDecision(
            TimeEvaluationResult timeResult, 
            Map<String, Object> metrics) {
        
        double efficiencyScore = timeResult.getEfficiencyScore();
        
        if (efficiencyScore < 60.0) {
            // 效率不佳，谨慎停止
            String reason = String.format("时间接近上限且效率不佳：利用率%.1f%%效率%.1f/100", 
                timeResult.getTimeUtilizationPercentage(), efficiencyScore);
            
            TerminationDecision decision = TerminationDecision.cautiousStop(reason, metrics);
            decision.addContext("trigger", "time_and_efficiency");
            decision.addContext("severity", "high");
            
            log.info("🚫 谨慎停止: 效率不佳(%.1f/100 < 60.0)", efficiencyScore);
            
            return decision;
            
        } else {
            // 效率良好，谨慎继续
            String reason = String.format("时间接近上限但效率良好：利用率%.1f%%效率%.1f/100", 
                timeResult.getTimeUtilizationPercentage(), efficiencyScore);
            
            TerminationDecision decision = TerminationDecision.continueWithCaution(reason, metrics);
            decision.addContext("trigger", "time_near_limit");
            decision.addContext("severity", "medium");
            
            log.info("⚠️ 谨慎继续: 效率良好(%.1f/100 >= 60.0)", efficiencyScore);
            
            return decision;
        }
    }
    
    /**
     * 创建最优范围决策
     */
    private TerminationDecision createOptimalDecision(
            TimeEvaluationResult timeResult, 
            Map<String, Object> metrics) {
        
        String reason = String.format("在最优时间范围内：利用率%.1f%%效率%.1f/100", 
            timeResult.getTimeUtilizationPercentage(), 
            timeResult.getEfficiencyScore());
        
        TerminationDecision decision = TerminationDecision.continueOptimization(reason, metrics);
        decision.addContext("trigger", "optimal_range");
        decision.addContext("severity", "low");
        
        log.info("✅ 最优范围: 时间利用率{}%% 在最优区间", 
            String.format("%.1f", timeResult.getTimeUtilizationPercentage()));
        
        return decision;
    }
    
    /**
     * 创建继续优化决策
     */
    private TerminationDecision createContinueDecision(
            TimeEvaluationResult timeResult, 
            Map<String, Object> metrics) {
        
        String reason = String.format("时间充裕继续合并：利用率%.1f%%效率%.1f/100", 
            timeResult.getTimeUtilizationPercentage(), 
            timeResult.getEfficiencyScore());
        
        TerminationDecision decision = TerminationDecision.continueOptimization(reason, metrics);
        decision.addContext("trigger", "time_sufficient");
        decision.addContext("severity", "low");
        
        log.info("🔄 继续合并: 时间充裕({}%% < {}%%)", 
            String.format("%.1f", timeResult.getTimeUtilizationPercentage()),
            String.format("%.1f", config.getOptimalThreshold() * 100));
        
        return decision;
    }
    
    // ===================== 辅助方法 =====================
    
    /**
     * 构建决策指标
     */
    private Map<String, Object> buildDecisionMetrics(
            TimeEvaluationResult timeResult,
            List<List<Accumulation>> currentRoute,
            List<Accumulation> candidateBlock) {
        
        Map<String, Object> metrics = new HashMap<>();
        
        // 时间指标
        metrics.put("totalTimeHours", timeResult.getTotalTimeHours());
        metrics.put("serviceTimeHours", timeResult.getServiceTimeHours());
        metrics.put("travelTimeHours", timeResult.getTravelTimeHours());
        metrics.put("depotTimeHours", timeResult.getDepotTimeHours());
        metrics.put("timeUtilizationPercentage", timeResult.getTimeUtilizationPercentage());
        
        // 效率指标
        metrics.put("efficiencyScore", timeResult.getEfficiencyScore());
        metrics.put("geographicCompactness", timeResult.getGeographicCompactness());
        
        // 规模指标
        int currentBlockCount = currentRoute.size();
        int currentPointCount = currentRoute.stream().mapToInt(List::size).sum();
        int candidatePointCount = candidateBlock.size();
        
        metrics.put("currentBlockCount", currentBlockCount);
        metrics.put("currentPointCount", currentPointCount);
        metrics.put("candidatePointCount", candidatePointCount);
        metrics.put("totalPointCount", currentPointCount + candidatePointCount);
        
        // 阈值指标
        metrics.put("maxWorkTimeHours", config.getMaxWorkTimeHours());
        metrics.put("flexibilityTimeThreshold", config.getFlexibilityTimeThreshold());
        metrics.put("cautionTimeThreshold", config.getCautionTimeThreshold());
        metrics.put("immediateStopTimeThreshold", config.getImmediateStopTimeThreshold());
        
        return metrics;
    }
    
    /**
     * 计算路线质量分数
     * 综合考虑时间效率、地理紧凑度等因素
     */
    private double calculateRouteQualityScore(TimeEvaluationResult timeResult) {
        double timeScore = 1.0 - timeResult.getTimeUtilizationRatio(); // 时间利用率越低越好
        double efficiencyScore = timeResult.getEfficiencyScore() / 100.0; // 标准化到0-1
        double compactnessScore = timeResult.getGeographicCompactness(); // 已经是0-1
        
        // 加权计算总分
        double qualityScore = timeScore * config.getTimeWeight() +
                             efficiencyScore * config.getEfficiencyWeight() +
                             compactnessScore * config.getBalanceWeight();
        
        log.debug("📊 质量分数: 时间{} × {} + 效率{} × {} + 紧凑{} × {} = {}", 
            String.format("%.3f", timeScore), String.format("%.1f", config.getTimeWeight()),
            String.format("%.3f", efficiencyScore), String.format("%.1f", config.getEfficiencyWeight()),
            String.format("%.3f", compactnessScore), String.format("%.1f", config.getBalanceWeight()),
            String.format("%.3f", qualityScore));
        
        return qualityScore;
    }
    
    /**
     * 获取评估器状态信息
     */
    public String getEvaluatorStatus() {
        return String.format("TimeBasedTerminationEvaluator[最大时间: %.1fh, 可动区间: %.1fh, 速度: %.0fkm/h]",
            config.getMaxWorkTimeHours(),
            config.getFlexibilityMarginHours(),
            config.getDrivingSpeedKmh());
    }
    
    /**
     * 检查评估器是否就绪
     */
    public boolean isReady() {
        return config.validateConfig() && timeCalculator != null;
    }
}