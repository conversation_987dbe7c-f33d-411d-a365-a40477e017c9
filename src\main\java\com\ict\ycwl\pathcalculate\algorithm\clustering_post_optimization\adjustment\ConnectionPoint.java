package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

/**
 * 连接点
 * 
 * 表示两条路线之间的最佳连接点信息，用于路线合并优化
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ConnectionPoint {
    
    /**
     * 路线1中的索引位置
     */
    private int route1Index;
    
    /**
     * 路线2中的索引位置
     */
    private int route2Index;
    
    /**
     * 路线1的连接聚集区
     */
    private Accumulation accumulation1;
    
    /**
     * 路线2的连接聚集区
     */
    private Accumulation accumulation2;
    
    /**
     * 连接成本（距离或时间）
     */
    private double connectionCost;
    
    /**
     * 获取连接聚集区1的ID
     */
    public Long getAccumulation1Id() {
        return accumulation1 != null ? accumulation1.getAccumulationId() : null;
    }
    
    /**
     * 获取连接聚集区2的ID
     */
    public Long getAccumulation2Id() {
        return accumulation2 != null ? accumulation2.getAccumulationId() : null;
    }
    
    /**
     * 获取连接聚集区1的名称
     */
    public String getAccumulation1Name() {
        return accumulation1 != null ? accumulation1.getAccumulationName() : "未知";
    }
    
    /**
     * 获取连接聚集区2的名称
     */
    public String getAccumulation2Name() {
        return accumulation2 != null ? accumulation2.getAccumulationName() : "未知";
    }
    
    /**
     * 是否为低成本连接
     */
    public boolean isLowCostConnection() {
        return connectionCost < 10.0; // 成本小于10（具体单位根据实际情况调整）
    }
    
    /**
     * 是否为高成本连接
     */
    public boolean isHighCostConnection() {
        return connectionCost > 50.0; // 成本大于50
    }
    
    /**
     * 获取连接成本等级描述
     */
    public String getCostLevel() {
        if (isLowCostConnection()) {
            return "低成本";
        } else if (isHighCostConnection()) {
            return "高成本";
        } else {
            return "中等成本";
        }
    }
    
    /**
     * 是否为有效连接点
     */
    public boolean isValidConnection() {
        return accumulation1 != null && accumulation2 != null && 
               !accumulation1.equals(accumulation2) && 
               connectionCost >= 0.0;
    }
    
    /**
     * 计算连接效率（成本的倒数，归一化）
     */
    public double getConnectionEfficiency() {
        if (connectionCost <= 0.0) {
            return 1.0; // 零成本视为最高效率
        }
        return 1.0 / (1.0 + connectionCost); // 归一化效率评分
    }
    
    /**
     * 比较连接成本（用于排序）
     */
    public int compareCost(ConnectionPoint other) {
        return Double.compare(this.connectionCost, other.connectionCost);
    }
    
    /**
     * 生成连接点描述
     */
    public String generateDescription() {
        return String.format("连接点 - %s[%d] ↔ %s[%d]，成本: %.2f (%s)",
            getAccumulation1Name(), route1Index,
            getAccumulation2Name(), route2Index,
            connectionCost, getCostLevel());
    }
    
    /**
     * 获取连接距离描述
     */
    public String getDistanceDescription() {
        if (connectionCost < 1.0) {
            return "极近";
        } else if (connectionCost < 5.0) {
            return "很近";
        } else if (connectionCost < 15.0) {
            return "较近";
        } else if (connectionCost < 30.0) {
            return "中等距离";
        } else {
            return "较远";
        }
    }
    
    /**
     * 是否适合作为连接点
     */
    public boolean isSuitableForConnection() {
        return isValidConnection() && !isHighCostConnection();
    }
}