package com.ict.ycwl.pathcalculate.AOP;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Aspect
@Component
@Order(10000)
public class ConfigFileDataSourceAspect {

    private final ResourceLoader resourceLoader;
    private final Environment environment;

    // 缓存文件内容和最后修改时间
    public static class FileCache {
        String content;
        long lastModified;
    }

    // 文件路径 -> 文件内容缓存
    private final ConcurrentMap<String, FileCache> fileCache = new ConcurrentHashMap<>();

    @Autowired
    public ConfigFileDataSourceAspect(ResourceLoader resourceLoader, Environment environment) {
        this.resourceLoader = resourceLoader;
        this.environment = environment;
    }

    @Around("@within(configFileDataSource)")
    public Object around(ProceedingJoinPoint joinPoint, ConfigFileDataSource configFileDataSource) throws Throwable {
        String dsKey = resolveDataSourceFromConfigFile(configFileDataSource);

        try {
            // 切换数据源
            DynamicDataSourceContextHolder.push(dsKey);
            return joinPoint.proceed();
        } finally {
            // 清理数据源
            DynamicDataSourceContextHolder.clear();
        }
    }

    private String resolveDataSourceFromConfigFile(ConfigFileDataSource annotation) {
        // 1. 从YAML配置获取文件路径
        String configKey = annotation.configKey();
        String filePath = environment.getProperty(configKey);

        if (filePath == null ) {
            throw new IllegalArgumentException("配置键 '" + configKey + "' 未在YAML中定义或为空");
        }

        try {
            // 2. 解析文件路径
            Resource resource = resourceLoader.getResource(filePath);

            // 3. 检查文件是否存在
            if (!resource.exists()) {
                throw new FileNotFoundException("数据源文件不存在: " + filePath);
            }

            // 4. 获取文件绝对路径作为缓存键
            String absolutePath = resource.getFile().getAbsolutePath();

            // 5. 检查缓存
            FileCache cache = fileCache.get(absolutePath);
            long currentModified = resource.lastModified();

            // 6. 如果文件已修改或缓存为空，重新读取
            if (cache == null || cache.lastModified != currentModified) {
                cache = new FileCache();
                cache.lastModified = currentModified;

                // 7. 读取文件第一行
                try (BufferedReader reader = new BufferedReader(
                     new InputStreamReader(
                         resource.getInputStream(),
                         Charset.forName(annotation.encoding())))) {

                    String firstLine = reader.readLine();
                    cache.content = firstLine != null ? firstLine.trim() : "";
                }

                // 8. 更新缓存
                fileCache.put(absolutePath, cache);
            }

            // 9. 验证数据源名称
            if (cache.content == null || cache.content.isEmpty()) {
                throw new IllegalArgumentException("数据源文件内容为空: " + filePath);
            }

            return cache.content;

        } catch (Exception e) {
            // 处理异常：记录错误并使用默认数据源
            System.err.println("从文件加载数据源失败: " + e.getMessage());
            System.err.println("使用默认数据源: " + annotation.defaultDS());
            return annotation.defaultDS();
        }
    }
}