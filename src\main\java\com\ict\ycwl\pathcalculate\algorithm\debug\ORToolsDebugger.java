package com.ict.ycwl.pathcalculate.algorithm.debug;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URL;
import java.security.CodeSource;

/**
 * OR-Tools调试器 - 深度诊断OR-Tools加载问题
 */
@Slf4j
public class ORToolsDebugger {

    public static void main(String[] args) {
        log.info("=== OR-Tools深度诊断开始 ===");
        
        // 1. 系统信息
        analyzeSystemInfo();
        
        // 2. ClassPath分析
        analyzeClassPath();
        
        // 3. Maven仓库检查
        analyzeMavenRepository();
        
        // 4. 逐步加载测试
        stepByStepLoadTest();
        
        log.info("=== OR-Tools深度诊断结束 ===");
    }
    
    private static void analyzeSystemInfo() {
        log.info("--- 系统信息分析 ---");
        log.info("Java版本: {}", System.getProperty("java.version"));
        log.info("Java供应商: {}", System.getProperty("java.vendor"));
        log.info("操作系统: {}", System.getProperty("os.name"));
        log.info("系统架构: {}", System.getProperty("os.arch"));
        log.info("Java架构: {}", System.getProperty("sun.arch.data.model") + "位");
        log.info("临时目录: {}", System.getProperty("java.io.tmpdir"));
        log.info("用户目录: {}", System.getProperty("user.home"));
        log.info("当前工作目录: {}", System.getProperty("user.dir"));
        log.info("Java库路径: {}", System.getProperty("java.library.path"));
    }
    
    private static void analyzeClassPath() {
        log.info("--- ClassPath分析 ---");
        String classPath = System.getProperty("java.class.path");
        String[] paths = classPath.split(File.pathSeparator);
        
        boolean foundOrTools = false;
        for (String path : paths) {
            if (path.toLowerCase().contains("ortools")) {
                log.info("✅ 发现OR-Tools JAR: {}", path);
                foundOrTools = true;
                
                // 检查文件是否存在
                File jarFile = new File(path);
                if (jarFile.exists()) {
                    log.info("   文件大小: {} MB", jarFile.length() / (1024 * 1024));
                    log.info("   最后修改: {}", new java.util.Date(jarFile.lastModified()));
                } else {
                    log.warn("   ❌ 文件不存在！");
                }
            }
        }
        
        if (!foundOrTools) {
            log.warn("❌ ClassPath中未发现OR-Tools JAR");
        }
    }
    
    private static void analyzeMavenRepository() {
        log.info("--- Maven仓库分析 ---");
        String userHome = System.getProperty("user.home");
        String orToolsPath = userHome + File.separator + ".m2" + File.separator + 
                           "repository" + File.separator + "com" + File.separator + 
                           "google" + File.separator + "ortools" + File.separator + 
                           "ortools-java" + File.separator + "9.8.3296";
        
        File orToolsDir = new File(orToolsPath);
        log.info("Maven仓库路径: {}", orToolsPath);
        log.info("目录存在: {}", orToolsDir.exists());
        
        if (orToolsDir.exists()) {
            File[] files = orToolsDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    log.info("  文件: {} ({} bytes)", file.getName(), file.length());
                }
                
                // 特别检查主JAR文件
                File mainJar = new File(orToolsDir, "ortools-java-9.8.3296.jar");
                if (mainJar.exists()) {
                    log.info("✅ 主JAR文件存在，大小: {} MB", mainJar.length() / (1024 * 1024));
                    analyzeJarContent(mainJar);
                } else {
                    log.warn("❌ 主JAR文件不存在");
                }
            }
        }
    }
    
    private static void analyzeJarContent(File jarFile) {
        log.info("--- JAR文件内容分析 ---");
        try {
            java.util.jar.JarFile jar = new java.util.jar.JarFile(jarFile);
            boolean hasLoader = false;
            boolean hasRoutingModel = false;
            boolean hasNativeLib = false;
            
            java.util.Enumeration<java.util.jar.JarEntry> entries = jar.entries();
            while (entries.hasMoreElements()) {
                java.util.jar.JarEntry entry = entries.nextElement();
                String name = entry.getName();
                
                if (name.contains("Loader.class")) {
                    hasLoader = true;
                    log.info("✅ 发现Loader类: {}", name);
                }
                if (name.contains("RoutingModel.class")) {
                    hasRoutingModel = true;
                    log.info("✅ 发现RoutingModel类: {}", name);
                }
                if (name.endsWith(".dll") || name.endsWith(".so") || name.endsWith(".dylib")) {
                    hasNativeLib = true;
                    log.info("✅ 发现原生库: {} ({} bytes)", name, entry.getSize());
                }
            }
            
            log.info("JAR内容检查结果:");
            log.info("  Loader类: {}", hasLoader ? "✅" : "❌");
            log.info("  RoutingModel类: {}", hasRoutingModel ? "✅" : "❌");
            log.info("  原生库文件: {}", hasNativeLib ? "✅" : "❌");
            
            jar.close();
        } catch (Exception e) {
            log.error("分析JAR文件失败: {}", e.getMessage());
        }
    }
    
    private static void stepByStepLoadTest() {
        log.info("--- 逐步加载测试 ---");
        
        // Step 1: 检查类是否存在
        log.info("Step 1: 检查Loader类");
        try {
            Class<?> loaderClass = Class.forName("com.google.ortools.Loader");
            log.info("✅ Loader类加载成功: {}", loaderClass.getName());
            
            // 检查类的来源
            CodeSource codeSource = loaderClass.getProtectionDomain().getCodeSource();
            if (codeSource != null) {
                URL location = codeSource.getLocation();
                log.info("   类来源: {}", location);
            }
        } catch (ClassNotFoundException e) {
            log.error("❌ Loader类不存在: {}", e.getMessage());
            return;
        }
        
        // Step 2: 尝试调用loadNativeLibraries
        log.info("Step 2: 调用loadNativeLibraries");
        try {
            Class<?> loaderClass = Class.forName("com.google.ortools.Loader");
            java.lang.reflect.Method loadMethod = loaderClass.getMethod("loadNativeLibraries");
            log.info("✅ 找到loadNativeLibraries方法");
            
            loadMethod.invoke(null);
            log.info("✅ loadNativeLibraries调用成功");
        } catch (Exception e) {
            log.error("❌ loadNativeLibraries调用失败: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            if (e.getCause() != null) {
                log.error("   根本原因: {} - {}", e.getCause().getClass().getSimpleName(), e.getCause().getMessage());
            }
        }
        
        // Step 3: 检查RoutingModel类
        log.info("Step 3: 检查RoutingModel类");
        try {
            Class.forName("com.google.ortools.constraintsolver.RoutingModel");
            log.info("✅ RoutingModel类加载成功");
        } catch (Exception e) {
            log.error("❌ RoutingModel类加载失败: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            return;
        }
        
        // Step 4: 尝试创建实例
        log.info("Step 4: 创建RoutingModel实例");
        try {
            Object routingModel = Class.forName("com.google.ortools.constraintsolver.RoutingModel")
                .getConstructor(int.class, int.class, int.class)
                .newInstance(2, 1, 0);
            log.info("✅ RoutingModel实例创建成功！OR-Tools完全可用！");
        } catch (Exception e) {
            log.error("❌ RoutingModel实例创建失败: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            if (e.getCause() != null) {
                log.error("   根本原因: {} - {}", e.getCause().getClass().getSimpleName(), e.getCause().getMessage());
            }
        }
    }
}