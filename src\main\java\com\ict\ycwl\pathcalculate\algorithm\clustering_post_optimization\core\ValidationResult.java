package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果
 * 
 * 记录数据完整性和约束满足验证的结果
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
public class ValidationResult {
    
    /**
     * 验证项列表
     */
    private List<ValidationCheck> checks;
    
    /**
     * 整体验证是否通过
     */
    private boolean valid;
    
    public ValidationResult() {
        this.checks = new ArrayList<>();
        this.valid = true;
    }
    
    /**
     * 添加验证项
     */
    public void addCheck(String checkName, boolean passed) {
        checks.add(new ValidationCheck(checkName, passed));
        if (!passed) {
            this.valid = false;
        }
    }
    
    /**
     * 获取验证通过率
     */
    public double getPassRate() {
        if (checks.isEmpty()) {
            return 1.0;
        }
        
        long passedCount = checks.stream().filter(ValidationCheck::isPassed).count();
        return (double) passedCount / checks.size();
    }
    
    /**
     * 验证项内部类
     */
    @Data
    public static class ValidationCheck {
        private String name;
        private boolean passed;
        
        public ValidationCheck(String name, boolean passed) {
            this.name = name;
            this.passed = passed;
        }
    }
}