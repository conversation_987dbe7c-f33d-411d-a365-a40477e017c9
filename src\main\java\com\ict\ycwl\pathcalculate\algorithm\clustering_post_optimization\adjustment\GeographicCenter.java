package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import lombok.Builder;
import lombok.Data;

/**
 * 地理中心
 * 
 * 表示一组聚集区的地理中心点坐标
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class GeographicCenter {
    
    /**
     * 纬度
     */
    private double latitude;
    
    /**
     * 经度
     */
    private double longitude;
    
    /**
     * 参与计算的聚集区数量
     */
    private int accumulationCount;
    
    /**
     * 是否有效的坐标
     */
    public boolean isValidCoordinates() {
        return latitude != 0.0 || longitude != 0.0;
    }
    
    /**
     * 计算到另一个地理中心的距离（公里）
     */
    public double distanceTo(GeographicCenter other) {
        if (!this.isValidCoordinates() || !other.isValidCoordinates()) {
            return 0.0;
        }
        
        // Haversine公式计算地理距离
        double lat1 = Math.toRadians(this.latitude);
        double lat2 = Math.toRadians(other.latitude);
        double deltaLat = Math.toRadians(other.latitude - this.latitude);
        double deltaLng = Math.toRadians(other.longitude - this.longitude);
        
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return 6371.0 * c; // 地球半径6371km
    }
    
    /**
     * 计算到指定坐标点的距离（公里）
     */
    public double distanceToPoint(double lat, double lng) {
        return distanceTo(GeographicCenter.builder()
            .latitude(lat)
            .longitude(lng)
            .accumulationCount(1)
            .build());
    }
    
    /**
     * 获取坐标字符串表示
     */
    public String getCoordinateString() {
        if (!isValidCoordinates()) {
            return "无效坐标";
        }
        return String.format("(%.6f, %.6f)", latitude, longitude);
    }
    
    /**
     * 获取地理中心描述
     */
    public String getDescription() {
        return String.format("地理中心 %s，包含%d个聚集区", 
            getCoordinateString(), accumulationCount);
    }
    
    /**
     * 判断是否在指定范围内
     */
    public boolean isWithinBounds(double minLat, double maxLat, double minLng, double maxLng) {
        return latitude >= minLat && latitude <= maxLat &&
               longitude >= minLng && longitude <= maxLng;
    }
    
    /**
     * 创建空的地理中心（用于默认值）
     */
    public static GeographicCenter createEmpty() {
        return GeographicCenter.builder()
            .latitude(0.0)
            .longitude(0.0)
            .accumulationCount(0)
            .build();
    }
    
    /**
     * 创建基于单点的地理中心
     */
    public static GeographicCenter createFromPoint(double lat, double lng) {
        return GeographicCenter.builder()
            .latitude(lat)
            .longitude(lng)
            .accumulationCount(1)
            .build();
    }
}