package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聚类二次优化器实现类
 * 
 * 集成OptaPlanner、JSPRIT、OR-Tools三大高性能库实现约束驱动优化
 * 专门解决450分钟工作时间约束和30分钟时间差异约束违反问题
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class ClusteringPostOptimizerImpl implements ClusteringPostOptimizer {
    
    // 约束阈值常量
    private static final double MAX_WORK_TIME_MINUTES = 450.0;
    private static final double MAX_TIME_GAP_MINUTES = 30.0;
    private static final int MAX_OPTIMIZATION_ROUNDS = 3;
    private static final long MAX_OPTIMIZATION_TIME_MS = 120000L; // 2分钟
    private static final double MIN_IMPROVEMENT_THRESHOLD = 0.05; // 5%最小改进阈值
    
    @Autowired
    private ConstraintAnalyzer constraintAnalyzer;
    
    @Autowired
    private MultiStrategyOptimizationManager strategyManager;
    
    @Autowired
    private DataTransformationLayer dataTransformer;
    
    @Autowired
    private ResultValidator resultValidator;
    
    @Autowired
    private OptimizationReportExporter reportExporter;
    
    /**
     * 优化器状态
     */
    private OptimizerStatus status;
    
    @PostConstruct
    public void initialize() {
        this.status = OptimizerStatus.createDefault();
        log.info("🚀 聚类二次优化器初始化完成");
    }
    
    @Override
    public List<List<Accumulation>> optimize(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        String sessionId = generateSessionId();
        long startTime = System.currentTimeMillis();
        
        try {
            status.updateStatus(OptimizerStatus.Status.OPTIMIZING);
            
            log.info("🔄 开始聚类二次优化，会话: {}, 中转站: {}, 聚类数: {}", 
                sessionId, depot.getTransitDepotName(), originalClusters.size());
            
            // 第1步：约束违反检测与分析
            ConstraintViolationReport initialReport = constraintAnalyzer.analyzeViolations(
                originalClusters, depot, timeMatrix);
            
            if (!initialReport.hasViolations()) {
                log.info("✅ 聚类已满足所有约束，无需二次优化");
                recordSuccessfulOptimization(startTime, 0, 0, 0.0, 0.0);
                return deepCopyOfClusters(originalClusters);
            }
            
            log.info("⚠️ 检测到约束违反 - 450分钟违反: {}, 30分钟差异违反: {}", 
                initialReport.getMaxTimeViolationCount(),
                initialReport.getTimeGapViolationCount());
            
            // 第2步：多轮渐进式优化
            List<List<Accumulation>> currentClusters = deepCopyOfClusters(originalClusters);
            OptimizationHistory history = new OptimizationHistory(sessionId);
            
            for (int round = 1; round <= MAX_OPTIMIZATION_ROUNDS; round++) {
                log.info("🔄 第 {} 轮优化开始", round);
                
                // 2.1 选择最优策略
                OptimizationStrategy strategy = strategyManager.selectOptimalStrategy(
                    constraintAnalyzer.analyzeViolations(currentClusters, depot, timeMatrix));
                
                // 2.2 执行优化
                OptimizationRoundResult roundResult = executeOptimizationRound(
                    currentClusters, depot, timeMatrix, strategy, round);
                
                history.addRound(roundResult);
                
                // 2.3 检查优化结果
                if (roundResult.isSuccess() && roundResult.isConstraintsSatisfied()) {
                    log.info("🎉 第 {} 轮优化成功，所有约束已满足", round);
                    currentClusters = roundResult.getOptimizedClusters();
                    break;
                }
                
                // 2.4 检查改进程度
                if (roundResult.getImprovementRatio() < MIN_IMPROVEMENT_THRESHOLD) {
                    log.warn("⚠️ 第 {} 轮优化改进有限 ({:.2f}%)，终止优化", 
                        round, roundResult.getImprovementRatio() * 100);
                    break;
                }
                
                if (roundResult.isSuccess()) {
                    currentClusters = roundResult.getOptimizedClusters();
                }
                
                // 检查时间限制
                if (System.currentTimeMillis() - startTime > MAX_OPTIMIZATION_TIME_MS) {
                    log.warn("⏱️ 优化时间超限，终止优化");
                    break;
                }
            }
            
            // 第3步：结果验证
            ValidationResult validation = resultValidator.validateDataIntegrity(
                originalClusters, currentClusters);
            
            if (!validation.isValid()) {
                log.error("❌ 优化结果验证失败，回退到原始聚类");
                recordFailedOptimization(startTime, "结果验证失败");
                return originalClusters;
            }
            
            // 第4步：统计优化效果
            ConstraintViolationReport finalReport = constraintAnalyzer.analyzeViolations(
                currentClusters, depot, timeMatrix);
            
            int fixed450Violations = initialReport.getMaxTimeViolationCount() - 
                                   finalReport.getMaxTimeViolationCount();
            int fixed30GapViolations = initialReport.getTimeGapViolationCount() - 
                                     finalReport.getTimeGapViolationCount();
            double maxTimeReduction = initialReport.getMaxWorkTime() - finalReport.getMaxWorkTime();
            double gapReduction = initialReport.getMaxTimeGap() - finalReport.getMaxTimeGap();
            
            // 第5步：导出优化报告
            reportExporter.exportOptimizationReport(sessionId, depot, originalClusters, 
                currentClusters, initialReport, finalReport, history);
            
            recordSuccessfulOptimization(startTime, fixed450Violations, fixed30GapViolations, 
                maxTimeReduction, gapReduction);
            
            log.info("✅ 聚类二次优化完成，会话: {}, 耗时: {}ms, 修复约束: {}/{}",
                sessionId, System.currentTimeMillis() - startTime,
                fixed450Violations + fixed30GapViolations,
                initialReport.getTotalViolationCount());
            
            return currentClusters;
            
        } catch (Exception e) {
            log.error("❌ 聚类二次优化异常，会话: {}", sessionId, e);
            recordFailedOptimization(startTime, e.getMessage());
            return originalClusters;
        }
    }
    
    @Override
    public Map<TransitDepot, List<List<Accumulation>>> optimizeBatch(
        Map<TransitDepot, List<List<Accumulation>>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        Map<TransitDepot, List<List<Accumulation>>> result = new HashMap<>();
        
        log.info("🔄 开始批量聚类二次优化，中转站数量: {}", originalClusters.size());
        
        for (Map.Entry<TransitDepot, List<List<Accumulation>>> entry : originalClusters.entrySet()) {
            TransitDepot depot = entry.getKey();
            List<List<Accumulation>> clusters = entry.getValue();
            
            List<List<Accumulation>> optimizedClusters = optimize(depot, clusters, timeMatrix);
            result.put(depot, optimizedClusters);
        }
        
        log.info("✅ 批量聚类二次优化完成");
        return result;
    }
    
    @Override
    public boolean needsOptimization(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        ConstraintViolationReport report = constraintAnalyzer.analyzeViolations(
            clusters, depot, timeMatrix);
        return report.hasViolations();
    }
    
    @Override
    public OptimizerStatus getStatus() {
        return status;
    }
    
    @Override
    public void reset() {
        this.status = OptimizerStatus.createDefault();
        log.info("🔄 聚类二次优化器状态已重置");
    }
    
    /**
     * 执行单轮优化
     */
    private OptimizationRoundResult executeOptimizationRound(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        OptimizationStrategy strategy,
        int round
    ) {
        long roundStart = System.currentTimeMillis();
        
        try {
            log.info("   🎯 使用策略: {}", strategy.getName());
            
            // 执行具体优化策略
            List<List<Accumulation>> optimizedClusters = strategyManager.executeStrategy(
                strategy, clusters, depot, timeMatrix);
            
            // 评估优化效果
            ConstraintViolationReport beforeReport = constraintAnalyzer.analyzeViolations(
                clusters, depot, timeMatrix);
            ConstraintViolationReport afterReport = constraintAnalyzer.analyzeViolations(
                optimizedClusters, depot, timeMatrix);
            
            double improvementRatio = calculateImprovementRatio(beforeReport, afterReport);
            boolean constraintsSatisfied = !afterReport.hasViolations();
            
            long roundTime = System.currentTimeMillis() - roundStart;
            
            log.info("   📊 第 {} 轮结果: 改进率 {:.2f}%, 约束满足: {}, 耗时: {}ms",
                round, improvementRatio * 100, constraintsSatisfied ? "是" : "否", roundTime);
            
            return OptimizationRoundResult.builder()
                .round(round)
                .strategy(strategy)
                .optimizedClusters(optimizedClusters)
                .beforeReport(beforeReport)
                .afterReport(afterReport)
                .improvementRatio(improvementRatio)
                .constraintsSatisfied(constraintsSatisfied)
                .executionTime(roundTime)
                .success(true)
                .build();
                
        } catch (Exception e) {
            log.error("   ❌ 第 {} 轮优化失败: {}", round, e.getMessage());
            
            return OptimizationRoundResult.builder()
                .round(round)
                .strategy(strategy)
                .optimizedClusters(clusters) // 返回原始聚类
                .improvementRatio(0.0)
                .constraintsSatisfied(false)
                .executionTime(System.currentTimeMillis() - roundStart)
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }
    
    /**
     * 计算改进率
     */
    private double calculateImprovementRatio(ConstraintViolationReport before, ConstraintViolationReport after) {
        int beforeViolations = before.getTotalViolationCount();
        int afterViolations = after.getTotalViolationCount();
        
        if (beforeViolations == 0) {
            return 0.0;
        }
        
        return (double) (beforeViolations - afterViolations) / beforeViolations;
    }
    
    /**
     * 深拷贝聚类结果
     */
    private List<List<Accumulation>> deepCopyOfClusters(List<List<Accumulation>> clusters) {
        return clusters.stream()
            .map(cluster -> cluster.stream()
                .map(Accumulation::copy)
                .collect(Collectors.toList()))
            .collect(Collectors.toList());
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "clustering_opt_" + System.currentTimeMillis() + "_" + 
               (int)(Math.random() * 10000);
    }
    
    /**
     * 记录成功优化
     */
    private void recordSuccessfulOptimization(long startTime, int fixed450Violations, 
                                            int fixed30GapViolations, double maxTimeReduction, 
                                            double gapReduction) {
        long executionTime = System.currentTimeMillis() - startTime;
        status.recordOptimizationCompleted(executionTime, true);
        status.recordConstraintFix(fixed450Violations, fixed30GapViolations, 
                                 maxTimeReduction, gapReduction);
    }
    
    /**
     * 记录失败优化
     */
    private void recordFailedOptimization(long startTime, String errorMessage) {
        long executionTime = System.currentTimeMillis() - startTime;
        status.recordOptimizationCompleted(executionTime, false);
        status.setLastError(errorMessage);
    }
}