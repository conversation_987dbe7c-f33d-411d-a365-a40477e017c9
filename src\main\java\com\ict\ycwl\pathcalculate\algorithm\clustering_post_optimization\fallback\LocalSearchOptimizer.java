package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 局部搜索算法优化器
 * 
 * 在当前解的邻域中寻找改进解的简单高效算法，适用于快速局部优化
 * 特别适合小规模问题和需要快速修复的场景
 * 
 * 算法特点：
 * - 计算效率高，收敛速度快
 * - 实现简单，易于调试和维护
 * - 适合小规模问题的快速优化
 * - 常用作其他算法的局部改进组件
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class LocalSearchOptimizer {
    
    // 算法参数
    private static final int MAX_ITERATIONS = 200;            // 最大迭代次数
    private static final int MAX_NO_IMPROVEMENT = 20;         // 最大无改进次数
    private static final int NEIGHBOR_SIZE = 20;              // 每次迭代的邻域大小
    private static final double MIN_IMPROVEMENT = 0.1;        // 最小改进阈值
    
    // 优化参数
    private static final double MAX_ROUTE_TIME = 450.0;       // 路线时间上限
    private static final double CONSTRAINT_WEIGHT = 1.0;      // 约束满足权重
    private static final double BALANCE_WEIGHT = 0.9;         // 时间平衡权重
    private static final double EFFICIENCY_WEIGHT = 0.4;      // 效率权重
    
    /**
     * 局部搜索操作类型
     */
    private enum LocalSearchOperation {
        RELOCATE("重定位", "将聚集区移动到最佳路线"),
        EXCHANGE("交换", "交换两条路线中的聚集区"),
        TWO_OPT("2-优化", "优化路线内部顺序"),
        OR_OPT("Or-优化", "移动连续的聚集区段");
        
        private final String name;
        private final String description;
        
        LocalSearchOperation(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDescription() { return description; }
    }
    
    /**
     * 执行局部搜索优化
     */
    public FallbackOptimizationResult optimize(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🔍 启动局部搜索优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotName(), originalRoutes.size());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            if (originalRoutes == null || originalRoutes.isEmpty()) {
                return createErrorResult("输入路线为空", startTime);
            }
            
            // 初始化
            List<List<Accumulation>> currentSolution = deepCopyRoutes(originalRoutes);
            List<List<Accumulation>> bestSolution = deepCopyRoutes(currentSolution);
            
            double currentScore = evaluateSolution(currentSolution, depot, timeMatrix);
            double bestScore = currentScore;
            
            // 算法执行状态
            int totalIterations = 0;
            int improvementCount = 0;
            int noImprovementCount = 0;
            
            // 操作使用统计
            Map<LocalSearchOperation, Integer> operationUsage = new HashMap<>();
            Map<LocalSearchOperation, Integer> operationSuccess = new HashMap<>();
            for (LocalSearchOperation op : LocalSearchOperation.values()) {
                operationUsage.put(op, 0);
                operationSuccess.put(op, 0);
            }
            
            // 评分历史
            List<Double> scoreHistory = new ArrayList<>();
            scoreHistory.add(currentScore);
            
            log.info("   📊 初始解评分: {:.3f}", currentScore);
            
            // 主搜索循环
            while (totalIterations < MAX_ITERATIONS && noImprovementCount < MAX_NO_IMPROVEMENT) {
                
                boolean foundImprovement = false;
                double bestIterationScore = currentScore;
                List<List<Accumulation>> bestIterationSolution = currentSolution;
                LocalSearchOperation bestOperation = null;
                
                // 尝试所有局部搜索操作
                for (LocalSearchOperation operation : LocalSearchOperation.values()) {
                    operationUsage.put(operation, operationUsage.get(operation) + 1);
                    
                    // 在当前操作类型下寻找最佳邻居
                    LocalSearchResult result = findBestNeighbor(
                        currentSolution, operation, depot, timeMatrix);
                    
                    if (result != null && result.score > bestIterationScore + MIN_IMPROVEMENT) {
                        bestIterationScore = result.score;
                        bestIterationSolution = result.solution;
                        bestOperation = operation;
                        foundImprovement = true;
                    }
                }
                
                // 应用最佳改进
                if (foundImprovement) {
                    currentSolution = bestIterationSolution;
                    currentScore = bestIterationScore;
                    improvementCount++;
                    noImprovementCount = 0;
                    
                    if (bestOperation != null) {
                        operationSuccess.put(bestOperation, 
                            operationSuccess.get(bestOperation) + 1);
                    }
                    
                    // 更新全局最优解
                    if (currentScore > bestScore) {
                        bestSolution = deepCopyRoutes(currentSolution);
                        bestScore = currentScore;
                        
                        log.debug("   ✨ 第{}次迭代发现更优解: {:.3f} → {:.3f} (操作: {})", 
                            totalIterations + 1, bestScore, currentScore, 
                            bestOperation != null ? bestOperation.getName() : "未知");
                    }
                } else {
                    noImprovementCount++;
                }
                
                totalIterations++;
                
                // 记录评分历史
                if (totalIterations % 20 == 0) {
                    scoreHistory.add(currentScore);
                    log.debug("   🔍 迭代{}: 当前={:.3f}, 最优={:.3f}, 无改进={}次", 
                        totalIterations, currentScore, bestScore, noImprovementCount);
                }
            }
            
            // 计算优化指标
            OptimizationMetrics metrics = calculateOptimizationMetrics(
                originalRoutes, bestSolution, depot, timeMatrix);
            
            // 收敛信息
            FallbackOptimizationResult.ConvergenceInfo convergenceInfo = 
                FallbackOptimizationResult.ConvergenceInfo.builder()
                    .totalIterations(totalIterations)
                    .effectiveImprovements(improvementCount)
                    .convergenceGeneration(totalIterations)
                    .initialScore(scoreHistory.get(0))
                    .finalScore(bestScore)
                    .converged(noImprovementCount >= MAX_NO_IMPROVEMENT)
                    .convergenceReason(noImprovementCount >= MAX_NO_IMPROVEMENT ? 
                        "连续无改进达到限制" : "达到最大迭代次数")
                    .build();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 生成操作使用统计
            StringBuilder operationStats = new StringBuilder();
            for (LocalSearchOperation op : LocalSearchOperation.values()) {
                int usage = operationUsage.get(op);
                int success = operationSuccess.get(op);
                double successRate = usage > 0 ? (double) success / usage * 100.0 : 0.0;
                operationStats.append(String.format("%s:%d次(%.1f%%) ", 
                    op.name(), usage, successRate));
            }
            
            String algorithmDetails = String.format(
                "总迭代:%d | 改进:%d次 | 邻域大小:%d | 最小改进阈值:%.1f",
                totalIterations, improvementCount, NEIGHBOR_SIZE, MIN_IMPROVEMENT);
            
            String parameterInfo = String.format(
                "无改进限制:%d | 约束权重:%.1f | 平衡权重:%.1f | %s",
                MAX_NO_IMPROVEMENT, CONSTRAINT_WEIGHT, BALANCE_WEIGHT, 
                operationStats.toString().trim());
            
            boolean success = metrics.getViolationReduction() >= 0 || metrics.getTimeImprovement() > 0;
            
            log.info("✅ 局部搜索完成 - 耗时: {}ms, 迭代: {}, 改进: {}次, 成功: {}", 
                executionTime, totalIterations, improvementCount, success);
            
            return FallbackOptimizationResult.builder()
                .success(success)
                .optimizedRoutes(bestSolution)
                .originalRouteCount(originalRoutes.size())
                .optimizedRouteCount(bestSolution.size())
                .strategy(FallbackStrategy.LOCAL_SEARCH)
                .optimizationMetrics(metrics)
                .executionTimeMs(executionTime)
                .algorithmDetails(algorithmDetails)
                .parameterInfo(parameterInfo)
                .convergenceInfo(convergenceInfo)
                .message("局部搜索算法执行完成")
                .build();
                
        } catch (Exception e) {
            log.error("❌ 局部搜索算法执行异常", e);
            return createErrorResult("算法执行异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 局部搜索结果内部类
     */
    private static class LocalSearchResult {
        public List<List<Accumulation>> solution;
        public double score;
        public LocalSearchOperation operation;
        public String details;
        
        public LocalSearchResult(List<List<Accumulation>> solution, double score, 
                               LocalSearchOperation operation, String details) {
            this.solution = solution;
            this.score = score;
            this.operation = operation;
            this.details = details;
        }
    }
    
    /**
     * 寻找最佳邻居解
     */
    private LocalSearchResult findBestNeighbor(
            List<List<Accumulation>> currentSolution,
            LocalSearchOperation operation,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<Accumulation>> bestNeighbor = null;
        double bestScore = -1.0;
        String bestDetails = "";
        
        // 根据操作类型生成邻居解
        List<List<List<Accumulation>>> neighbors = generateNeighbors(
            currentSolution, operation, depot, timeMatrix);
        
        // 评估所有邻居解
        for (int i = 0; i < Math.min(neighbors.size(), NEIGHBOR_SIZE); i++) {
            List<List<Accumulation>> neighbor = neighbors.get(i);
            double score = evaluateSolution(neighbor, depot, timeMatrix);
            
            if (score > bestScore) {
                bestScore = score;
                bestNeighbor = neighbor;
                bestDetails = String.format("第%d个邻居", i + 1);
            }
        }
        
        return bestNeighbor != null ? 
            new LocalSearchResult(bestNeighbor, bestScore, operation, bestDetails) : null;
    }
    
    /**
     * 生成邻居解
     */
    private List<List<List<Accumulation>>> generateNeighbors(
            List<List<Accumulation>> solution,
            LocalSearchOperation operation,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<List<Accumulation>>> neighbors = new ArrayList<>();
        
        try {
            switch (operation) {
                case RELOCATE:
                    neighbors = generateRelocateNeighbors(solution, depot, timeMatrix);
                    break;
                    
                case EXCHANGE:
                    neighbors = generateExchangeNeighbors(solution, depot, timeMatrix);
                    break;
                    
                case TWO_OPT:
                    neighbors = generateTwoOptNeighbors(solution, depot, timeMatrix);
                    break;
                    
                case OR_OPT:
                    neighbors = generateOrOptNeighbors(solution, depot, timeMatrix);
                    break;
            }
        } catch (Exception e) {
            log.debug("   ⚠️ 生成{}邻居失败: {}", operation.getName(), e.getMessage());
        }
        
        return neighbors;
    }
    
    /**
     * 生成重定位邻居
     */
    private List<List<List<Accumulation>>> generateRelocateNeighbors(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<List<Accumulation>>> neighbors = new ArrayList<>();
        
        // 为每个聚集区尝试重定位到其他路线
        for (int i = 0; i < solution.size(); i++) {
            List<Accumulation> sourceRoute = solution.get(i);
            
            for (int j = 0; j < sourceRoute.size(); j++) {
                Accumulation accToMove = sourceRoute.get(j);
                
                // 找到最佳目标路线
                int bestTargetRoute = findBestTargetRoute(
                    solution, i, accToMove, depot, timeMatrix);
                
                if (bestTargetRoute != -1 && bestTargetRoute != i) {
                    List<List<Accumulation>> neighbor = deepCopyRoutes(solution);
                    
                    // 执行重定位
                    Accumulation removed = neighbor.get(i).remove(j);
                    neighbor.get(bestTargetRoute).add(removed);
                    
                    neighbors.add(neighbor);
                }
            }
        }
        
        return neighbors;
    }
    
    /**
     * 找到最佳目标路线
     */
    private int findBestTargetRoute(
            List<List<Accumulation>> solution,
            int sourceRouteIndex,
            Accumulation accToMove,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        int bestTarget = -1;
        double bestImprovement = 0.0;
        double sourceTime = calculateRouteTime(solution.get(sourceRouteIndex), depot, timeMatrix);
        double accTime = calculateAccumulationTime(accToMove, depot, timeMatrix);
        
        for (int i = 0; i < solution.size(); i++) {
            if (i == sourceRouteIndex) continue;
            
            List<Accumulation> targetRoute = solution.get(i);
            double targetTime = calculateRouteTime(targetRoute, depot, timeMatrix);
            
            // 检查约束
            if (targetTime + accTime > MAX_ROUTE_TIME * 1.05) continue;
            
            // 计算平衡性改进
            double newSourceTime = sourceTime - accTime;
            double newTargetTime = targetTime + accTime;
            
            double originalImbalance = Math.abs(sourceTime - targetTime);
            double newImbalance = Math.abs(newSourceTime - newTargetTime);
            double improvement = originalImbalance - newImbalance;
            
            if (improvement > bestImprovement) {
                bestImprovement = improvement;
                bestTarget = i;
            }
        }
        
        return bestTarget;
    }
    
    /**
     * 生成交换邻居
     */
    private List<List<List<Accumulation>>> generateExchangeNeighbors(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<List<Accumulation>>> neighbors = new ArrayList<>();
        
        // 尝试所有可能的聚集区交换
        for (int i = 0; i < solution.size(); i++) {
            for (int j = i + 1; j < solution.size(); j++) {
                List<Accumulation> route1 = solution.get(i);
                List<Accumulation> route2 = solution.get(j);
                
                if (route1.isEmpty() || route2.isEmpty()) continue;
                
                // 尝试最有希望的交换组合
                List<int[]> bestExchanges = findBestExchangePairs(
                    route1, route2, depot, timeMatrix);
                
                for (int[] exchange : bestExchanges) {
                    List<List<Accumulation>> neighbor = deepCopyRoutes(solution);
                    
                    // 执行交换
                    Accumulation temp = neighbor.get(i).get(exchange[0]);
                    neighbor.get(i).set(exchange[0], neighbor.get(j).get(exchange[1]));
                    neighbor.get(j).set(exchange[1], temp);
                    
                    neighbors.add(neighbor);
                    
                    if (neighbors.size() >= NEIGHBOR_SIZE) break;
                }
                
                if (neighbors.size() >= NEIGHBOR_SIZE) break;
            }
            if (neighbors.size() >= NEIGHBOR_SIZE) break;
        }
        
        return neighbors;
    }
    
    /**
     * 找到最佳交换对
     */
    private List<int[]> findBestExchangePairs(
            List<Accumulation> route1,
            List<Accumulation> route2,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<int[]> bestPairs = new ArrayList<>();
        List<ExchangeCandidate> candidates = new ArrayList<>();
        
        // 计算所有可能交换的收益
        for (int i = 0; i < route1.size(); i++) {
            for (int j = 0; j < route2.size(); j++) {
                double benefit = calculateExchangeBenefit(
                    route1, route2, i, j, depot, timeMatrix);
                
                if (benefit > 0) {
                    candidates.add(new ExchangeCandidate(i, j, benefit));
                }
            }
        }
        
        // 按收益排序，选择前几个
        candidates.sort((a, b) -> Double.compare(b.benefit, a.benefit));
        
        int count = Math.min(5, candidates.size()); // 最多选择5个最佳交换
        for (int i = 0; i < count; i++) {
            ExchangeCandidate candidate = candidates.get(i);
            bestPairs.add(new int[]{candidate.index1, candidate.index2});
        }
        
        return bestPairs;
    }
    
    /**
     * 交换候选内部类
     */
    private static class ExchangeCandidate {
        int index1, index2;
        double benefit;
        
        ExchangeCandidate(int index1, int index2, double benefit) {
            this.index1 = index1;
            this.index2 = index2;
            this.benefit = benefit;
        }
    }
    
    /**
     * 计算交换收益
     */
    private double calculateExchangeBenefit(
            List<Accumulation> route1,
            List<Accumulation> route2,
            int index1,
            int index2,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Accumulation acc1 = route1.get(index1);
        Accumulation acc2 = route2.get(index2);
        
        double time1 = calculateAccumulationTime(acc1, depot, timeMatrix);
        double time2 = calculateAccumulationTime(acc2, depot, timeMatrix);
        
        double route1Time = calculateRouteTime(route1, depot, timeMatrix);
        double route2Time = calculateRouteTime(route2, depot, timeMatrix);
        
        // 交换后的时间
        double newRoute1Time = route1Time - time1 + time2;
        double newRoute2Time = route2Time - time2 + time1;
        
        // 检查约束
        if (newRoute1Time > MAX_ROUTE_TIME || newRoute2Time > MAX_ROUTE_TIME) {
            return -1.0; // 违反约束
        }
        
        // 计算平衡性改进
        double originalImbalance = Math.abs(route1Time - route2Time);
        double newImbalance = Math.abs(newRoute1Time - newRoute2Time);
        
        return originalImbalance - newImbalance;
    }
    
    /**
     * 生成2-opt邻居
     */
    private List<List<List<Accumulation>>> generateTwoOptNeighbors(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<List<Accumulation>>> neighbors = new ArrayList<>();
        
        // 对每条有足够聚集区的路线进行2-opt
        for (int routeIndex = 0; routeIndex < solution.size(); routeIndex++) {
            List<Accumulation> route = solution.get(routeIndex);
            
            if (route.size() < 4) continue; // 至少需要4个聚集区
            
            // 尝试不同的2-opt切割点
            for (int i = 1; i < route.size() - 2; i++) {
                for (int j = i + 1; j < route.size() - 1; j++) {
                    List<List<Accumulation>> neighbor = deepCopyRoutes(solution);
                    
                    // 执行2-opt操作
                    List<Accumulation> newRoute = apply2Opt(route, i, j);
                    neighbor.set(routeIndex, newRoute);
                    
                    neighbors.add(neighbor);
                    
                    if (neighbors.size() >= NEIGHBOR_SIZE) break;
                }
                if (neighbors.size() >= NEIGHBOR_SIZE) break;
            }
            if (neighbors.size() >= NEIGHBOR_SIZE) break;
        }
        
        return neighbors;
    }
    
    /**
     * 应用2-opt操作
     */
    private List<Accumulation> apply2Opt(List<Accumulation> route, int i, int j) {
        List<Accumulation> newRoute = new ArrayList<>();
        
        // 添加0到i的部分
        for (int k = 0; k <= i; k++) {
            newRoute.add(route.get(k));
        }
        
        // 反转i+1到j的部分
        for (int k = j; k > i; k--) {
            newRoute.add(route.get(k));
        }
        
        // 添加j+1到end的部分
        for (int k = j + 1; k < route.size(); k++) {
            newRoute.add(route.get(k));
        }
        
        return newRoute;
    }
    
    /**
     * 生成Or-opt邻居
     */
    private List<List<List<Accumulation>>> generateOrOptNeighbors(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<List<List<Accumulation>>> neighbors = new ArrayList<>();
        
        // 尝试移动连续的聚集区段
        for (int routeIndex = 0; routeIndex < solution.size(); routeIndex++) {
            List<Accumulation> route = solution.get(routeIndex);
            
            if (route.size() < 3) continue;
            
            // 尝试不同长度的段（1到3个聚集区）
            for (int segmentLength = 1; segmentLength <= Math.min(3, route.size() - 1); segmentLength++) {
                for (int startPos = 0; startPos <= route.size() - segmentLength; startPos++) {
                    
                    // 尝试移动到不同位置
                    for (int newPos = 0; newPos <= route.size() - segmentLength; newPos++) {
                        if (Math.abs(newPos - startPos) <= segmentLength) continue; // 跳过无意义的移动
                        
                        List<List<Accumulation>> neighbor = deepCopyRoutes(solution);
                        List<Accumulation> newRoute = applyOrOpt(route, startPos, segmentLength, newPos);
                        neighbor.set(routeIndex, newRoute);
                        
                        neighbors.add(neighbor);
                        
                        if (neighbors.size() >= NEIGHBOR_SIZE) break;
                    }
                    if (neighbors.size() >= NEIGHBOR_SIZE) break;
                }
                if (neighbors.size() >= NEIGHBOR_SIZE) break;
            }
            if (neighbors.size() >= NEIGHBOR_SIZE) break;
        }
        
        return neighbors;
    }
    
    /**
     * 应用Or-opt操作
     */
    private List<Accumulation> applyOrOpt(List<Accumulation> route, int startPos, int segmentLength, int newPos) {
        List<Accumulation> newRoute = new ArrayList<>(route);
        
        // 提取要移动的段
        List<Accumulation> segment = new ArrayList<>();
        for (int i = 0; i < segmentLength; i++) {
            segment.add(newRoute.remove(startPos));
        }
        
        // 调整插入位置
        int insertPos = newPos;
        if (newPos > startPos) {
            insertPos -= segmentLength;
        }
        
        // 插入段到新位置
        newRoute.addAll(insertPos, segment);
        
        return newRoute;
    }
    
    /**
     * 评估解的质量
     */
    private double evaluateSolution(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double score = 0.0;
        
        // 1. 约束满足评分
        double constraintScore = evaluateConstraintSatisfaction(solution, depot, timeMatrix);
        score += constraintScore * CONSTRAINT_WEIGHT;
        
        // 2. 时间平衡评分
        double balanceScore = evaluateTimeBalance(solution, depot, timeMatrix);
        score += balanceScore * BALANCE_WEIGHT;
        
        // 3. 效率评分
        double efficiencyScore = evaluateEfficiency(solution);
        score += efficiencyScore * EFFICIENCY_WEIGHT;
        
        return score;
    }
    
    /**
     * 评估约束满足程度
     */
    private double evaluateConstraintSatisfaction(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double score = 100.0;
        int violationCount = 0;
        double totalViolationTime = 0.0;
        
        for (List<Accumulation> route : solution) {
            double routeTime = calculateRouteTime(route, depot, timeMatrix);
            if (routeTime > MAX_ROUTE_TIME) {
                violationCount++;
                totalViolationTime += (routeTime - MAX_ROUTE_TIME);
            }
        }
        
        // 扣分策略：每个违反减10分，每超时1分钟减1分
        score -= violationCount * 10.0;
        score -= totalViolationTime;
        
        return Math.max(0.0, score);
    }
    
    /**
     * 评估时间平衡程度
     */
    private double evaluateTimeBalance(
            List<List<Accumulation>> solution,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (solution.isEmpty()) return 0.0;
        
        List<Double> routeTimes = solution.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        double avgTime = routeTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = routeTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgTime, 2))
            .average().orElse(0.0);
        
        double stdDev = Math.sqrt(variance);
        return Math.max(0.0, 100.0 - stdDev * 0.25); // 局部搜索对平衡性要求相对较低
    }
    
    /**
     * 评估效率
     */
    private double evaluateEfficiency(List<List<Accumulation>> solution) {
        if (solution.isEmpty()) return 0.0;
        
        int totalAccumulations = solution.stream().mapToInt(List::size).sum();
        int routeCount = solution.size();
        
        if (routeCount == 0) return 0.0;
        
        // 简单的效率评分：避免空路线
        long emptyRoutes = solution.stream().mapToLong(route -> route.isEmpty() ? 1 : 0).sum();
        double utilization = 1.0 - (double) emptyRoutes / routeCount;
        
        return utilization * 100.0;
    }
    
    /**
     * 计算路线总时间
     */
    private double calculateRouteTime(
            List<Accumulation> route,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            }
        }
        
        // 往返时间
        for (Accumulation acc : route) {
            totalTime += calculateAccumulationTime(acc, depot, timeMatrix);
        }
        
        return totalTime;
    }
    
    /**
     * 计算单个聚集区的往返时间
     */
    private double calculateAccumulationTime(
            Accumulation acc,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
        TimeInfo timeInfo = timeMatrix.get(key);
        if (timeInfo != null && timeInfo.getTravelTime() != null) {
            return timeInfo.getTravelTime() * 2; // 往返
        }
        return 60.0; // 默认往返60分钟
    }
    
    /**
     * 计算优化指标
     */
    private OptimizationMetrics calculateOptimizationMetrics(
            List<List<Accumulation>> originalRoutes,
            List<List<Accumulation>> optimizedRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 计算原始指标
        double originalTotalTime = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> originalTimes = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long originalViolations = originalTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double originalAvg = originalTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double originalStdDev = Math.sqrt(originalTimes.stream()
            .mapToDouble(time -> Math.pow(time - originalAvg, 2))
            .average().orElse(0.0));
        
        // 计算优化后指标
        double optimizedTotalTime = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> optimizedTimes = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long optimizedViolations = optimizedTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double optimizedAvg = optimizedTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double optimizedStdDev = Math.sqrt(optimizedTimes.stream()
            .mapToDouble(time -> Math.pow(time - optimizedAvg, 2))
            .average().orElse(0.0));
        
        // 计算改进指标
        double timeImprovement = originalTotalTime > 0 ? 
            (originalTotalTime - optimizedTotalTime) / originalTotalTime * 100.0 : 0.0;
        
        double timeBalanceImprovement = originalStdDev > 0 ? 
            (originalStdDev - optimizedStdDev) / originalStdDev * 100.0 : 0.0;
        
        int violationReduction = (int) (originalViolations - optimizedViolations);
        
        double constraintSatisfactionRate = optimizedRoutes.size() > 0 ? 
            1.0 - (double) optimizedViolations / optimizedRoutes.size() : 1.0;
        
        return OptimizationMetrics.builder()
            .originalTotalTime(originalTotalTime)
            .optimizedTotalTime(optimizedTotalTime)
            .timeImprovement(timeImprovement)
            .originalViolations((int) originalViolations)
            .optimizedViolations((int) optimizedViolations)
            .violationReduction(violationReduction)
            .constraintSatisfactionRate(constraintSatisfactionRate)
            .originalTimeStdDev(originalStdDev)
            .optimizedTimeStdDev(optimizedStdDev)
            .timeBalanceImprovement(timeBalanceImprovement)
            .geographicRationalityScore(0.9) // 局部搜索通常保持高地理合理性
            .convergenceScore(0.8) // 中等收敛性评分
            .build();
    }
    
    /**
     * 深度复制路线列表
     */
    private List<List<Accumulation>> deepCopyRoutes(List<List<Accumulation>> originalRoutes) {
        return originalRoutes.stream()
            .map(ArrayList::new)
            .collect(Collectors.toList());
    }
    
    /**
     * 创建错误结果
     */
    private FallbackOptimizationResult createErrorResult(String message, long startTime) {
        return FallbackOptimizationResult.builder()
            .success(false)
            .optimizedRoutes(new ArrayList<>())
            .originalRouteCount(0)
            .optimizedRouteCount(0)
            .strategy(FallbackStrategy.LOCAL_SEARCH)
            .message(message)
            .executionTimeMs(System.currentTimeMillis() - startTime)
            .build();
    }
}