package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;

import java.util.List;
import java.util.Map;

/**
 * 聚类二次优化器接口
 * 
 * 负责在聚类和TSP阶段之间插入二次优化，专门解决约束违反问题：
 * - 450分钟工作时间硬约束
 * - 30分钟时间差异硬约束
 * - 地理合理性约束
 * 
 * 使用高性能第三方库(OptaPlanner、JSPRIT、OR-Tools)实现约束驱动优化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
public interface ClusteringPostOptimizer {
    
    /**
     * 对单个中转站的聚类结果进行二次优化
     * 
     * @param depot 中转站信息
     * @param originalClusters 原始聚类结果
     * @param timeMatrix 时间矩阵
     * @return 优化后的聚类结果
     */
    List<List<Accumulation>> optimize(
        TransitDepot depot,
        List<List<Accumulation>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    );
    
    /**
     * 批量优化多个中转站的聚类结果
     * 
     * @param originalClusters 原始聚类结果(按中转站分组)
     * @param timeMatrix 时间矩阵
     * @return 优化后的聚类结果
     */
    Map<TransitDepot, List<List<Accumulation>>> optimizeBatch(
        Map<TransitDepot, List<List<Accumulation>>> originalClusters,
        Map<String, TimeInfo> timeMatrix
    );
    
    /**
     * 检查是否需要二次优化
     * 
     * @param depot 中转站信息
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 是否需要二次优化
     */
    boolean needsOptimization(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    );
    
    /**
     * 获取优化器状态信息
     * 
     * @return 优化器状态
     */
    OptimizerStatus getStatus();
    
    /**
     * 重置优化器状态
     */
    void reset();
}