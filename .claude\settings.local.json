{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(mvn compile:*)", "Bash(git add:*)", "Bash(find:*)", "<PERSON><PERSON>(mvn test:*)", "Bash(.check_vcredist.bat)", "<PERSON><PERSON>(mvn clean:*)", "Bash(rm:*)", "Bash(mvn dependency:*)", "Bash(java:*)", "Bash(ls:*)", "Bash(echo $TEMP)", "Bash(echo $TMP)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git commit:*)", "Bash(dir clustering_results_*.json /od)"], "deny": []}}