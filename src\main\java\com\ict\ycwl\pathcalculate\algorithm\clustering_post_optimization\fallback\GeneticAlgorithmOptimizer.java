package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 遗传算法优化器
 * 
 * 模拟生物进化过程的群体智能优化算法，通过选择、交叉、变异等操作寻找最优解
 * 特别适合大规模复杂路线优化问题
 * 
 * 算法特点：
 * - 群体搜索策略，维护多个候选解
 * - 并行优化能力，适合大规模问题
 * - 通过交叉操作交换优质基因
 * - 通过变异操作保持群体多样性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class GeneticAlgorithmOptimizer {
    
    // 算法参数
    private static final int POPULATION_SIZE = 50;              // 种群大小
    private static final int MAX_GENERATIONS = 200;            // 最大进化代数
    private static final double ELITE_RATE = 0.1;              // 精英保留率
    private static final double CROSSOVER_RATE = 0.8;          // 交叉概率
    private static final double MUTATION_RATE = 0.2;           // 变异概率
    private static final int TOURNAMENT_SIZE = 5;              // 锦标赛选择规模
    private static final int CONVERGENCE_GENERATIONS = 20;     // 收敛判定代数
    
    // 优化参数
    private static final double MAX_ROUTE_TIME = 450.0;        // 路线时间上限
    private static final double CONSTRAINT_WEIGHT = 1.0;       // 约束满足权重
    private static final double BALANCE_WEIGHT = 0.8;          // 时间平衡权重
    private static final double GEOGRAPHIC_WEIGHT = 0.3;       // 地理合理性权重
    
    /**
     * 个体（染色体）类
     * 代表一个路线分配方案
     */
    private static class Individual {
        private List<List<Accumulation>> routes;
        private double fitness;
        private boolean evaluated;
        
        public Individual(List<List<Accumulation>> routes) {
            this.routes = deepCopyRoutes(routes);
            this.fitness = 0.0;
            this.evaluated = false;
        }
        
        public List<List<Accumulation>> getRoutes() { return routes; }
        public double getFitness() { return fitness; }
        public void setFitness(double fitness) { 
            this.fitness = fitness; 
            this.evaluated = true;
        }
        public boolean isEvaluated() { return evaluated; }
        
        public Individual copy() {
            Individual copy = new Individual(this.routes);
            copy.fitness = this.fitness;
            copy.evaluated = this.evaluated;
            return copy;
        }
    }
    
    /**
     * 执行遗传算法优化
     */
    public FallbackOptimizationResult optimize(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🧬 启动遗传算法优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotName(), originalRoutes.size());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            if (originalRoutes == null || originalRoutes.isEmpty()) {
                return createErrorResult("输入路线为空", startTime);
            }
            
            // 初始化种群
            List<Individual> population = initializePopulation(originalRoutes, depot, timeMatrix);
            
            // 评估初始种群
            evaluatePopulation(population, depot, timeMatrix);
            
            // 进化统计
            Individual bestIndividual = findBestIndividual(population);
            double initialBestFitness = bestIndividual.getFitness();
            double currentBestFitness = initialBestFitness;
            int generationsWithoutImprovement = 0;
            int totalGenerations = 0;
            int improvementCount = 0;
            
            // 适应度历史
            List<Double> fitnessHistory = new ArrayList<>();
            fitnessHistory.add(currentBestFitness);
            
            log.info("   📊 初始种群最优适应度: {:.3f}", initialBestFitness);
            
            // 主进化循环
            for (int generation = 0; generation < MAX_GENERATIONS; generation++) {
                totalGenerations = generation;
                
                // 生成新一代种群
                List<Individual> newPopulation = evolveGeneration(population, depot, timeMatrix);
                
                // 评估新种群
                evaluatePopulation(newPopulation, depot, timeMatrix);
                
                // 精英保留策略
                population = selectSurvivors(population, newPopulation);
                
                // 更新最优个体
                Individual currentBest = findBestIndividual(population);
                double newBestFitness = currentBest.getFitness();
                
                if (newBestFitness > currentBestFitness) {
                    bestIndividual = currentBest.copy();
                    currentBestFitness = newBestFitness;
                    generationsWithoutImprovement = 0;
                    improvementCount++;
                    
                    log.debug("   ✨ 第{}代发现更优解: {:.3f} (+{:.3f})", 
                        generation + 1, newBestFitness, newBestFitness - currentBestFitness);
                } else {
                    generationsWithoutImprovement++;
                }
                
                // 记录适应度历史
                if (generation % 10 == 0) {
                    fitnessHistory.add(currentBestFitness);
                    
                    double avgFitness = population.stream()
                        .mapToDouble(Individual::getFitness)
                        .average().orElse(0.0);
                    
                    log.debug("   🧬 第{}代: 最优={:.3f}, 平均={:.3f}, 无改进={}代", 
                        generation + 1, currentBestFitness, avgFitness, generationsWithoutImprovement);
                }
                
                // 收敛判定
                if (generationsWithoutImprovement >= CONVERGENCE_GENERATIONS) {
                    log.info("   🎯 算法收敛: {}代无改进，提前终止", generationsWithoutImprovement);
                    break;
                }
            }
            
            // 计算优化指标
            OptimizationMetrics metrics = calculateOptimizationMetrics(
                originalRoutes, bestIndividual.getRoutes(), depot, timeMatrix);
            
            // 收敛信息
            FallbackOptimizationResult.ConvergenceInfo convergenceInfo = 
                FallbackOptimizationResult.ConvergenceInfo.builder()
                    .totalIterations(totalGenerations + 1)
                    .effectiveImprovements(improvementCount)
                    .convergenceGeneration(totalGenerations + 1)
                    .initialScore(initialBestFitness)
                    .finalScore(currentBestFitness)
                    .converged(generationsWithoutImprovement >= CONVERGENCE_GENERATIONS)
                    .convergenceReason(generationsWithoutImprovement >= CONVERGENCE_GENERATIONS ? 
                        "连续无改进达到收敛条件" : "达到最大进化代数")
                    .build();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            String algorithmDetails = String.format(
                "种群大小:%d | 进化代数:%d | 精英率:%.1f%% | 交叉率:%.1f%% | 变异率:%.1f%% | 改进:%d次",
                POPULATION_SIZE, totalGenerations + 1, ELITE_RATE * 100, 
                CROSSOVER_RATE * 100, MUTATION_RATE * 100, improvementCount);
            
            String parameterInfo = String.format(
                "锦标赛规模:%d | 收敛代数:%d | 约束权重:%.1f | 平衡权重:%.1f",
                TOURNAMENT_SIZE, CONVERGENCE_GENERATIONS, CONSTRAINT_WEIGHT, BALANCE_WEIGHT);
            
            boolean success = metrics.getViolationReduction() >= 0 || metrics.getTimeImprovement() > 0;
            
            log.info("✅ 遗传算法优化完成 - 耗时: {}ms, 进化: {}代, 改进: {}次, 成功: {}", 
                executionTime, totalGenerations + 1, improvementCount, success);
            
            return FallbackOptimizationResult.builder()
                .success(success)
                .optimizedRoutes(bestIndividual.getRoutes())
                .originalRouteCount(originalRoutes.size())
                .optimizedRouteCount(bestIndividual.getRoutes().size())
                .strategy(FallbackStrategy.GENETIC_ALGORITHM)
                .optimizationMetrics(metrics)
                .executionTimeMs(executionTime)
                .algorithmDetails(algorithmDetails)
                .parameterInfo(parameterInfo)
                .convergenceInfo(convergenceInfo)
                .message("遗传算法执行完成")
                .build();
                
        } catch (Exception e) {
            log.error("❌ 遗传算法执行异常", e);
            return createErrorResult("算法执行异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 初始化种群
     */
    private List<Individual> initializePopulation(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<Individual> population = new ArrayList<>();
        Random random = new Random();
        
        // 第一个个体：原始解
        population.add(new Individual(originalRoutes));
        
        // 其余个体：通过随机变换生成
        for (int i = 1; i < POPULATION_SIZE; i++) {
            List<List<Accumulation>> routes = deepCopyRoutes(originalRoutes);
            
            // 随机应用多种变换
            int transformations = 1 + random.nextInt(3); // 1-3次变换
            for (int j = 0; j < transformations; j++) {
                routes = applyRandomTransformation(routes, depot, timeMatrix);
            }
            
            population.add(new Individual(routes));
        }
        
        return population;
    }
    
    /**
     * 应用随机变换
     */
    private List<List<Accumulation>> applyRandomTransformation(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        int transformType = random.nextInt(3);
        
        try {
            switch (transformType) {
                case 0:
                    return performRandomReassignment(routes, depot, timeMatrix);
                case 1:
                    return performRandomSwap(routes, depot, timeMatrix);
                case 2:
                    return performRandomSplitOrMerge(routes, depot, timeMatrix);
                default:
                    return routes;
            }
        } catch (Exception e) {
            return routes; // 变换失败，返回原路线
        }
    }
    
    /**
     * 随机重分配操作
     */
    private List<List<Accumulation>> performRandomReassignment(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到非空路线
        List<Integer> nonEmptyRoutes = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            if (!routes.get(i).isEmpty()) {
                nonEmptyRoutes.add(i);
            }
        }
        
        if (nonEmptyRoutes.size() < 2) return routes;
        
        // 随机选择源路线和目标路线
        int sourceIndex = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        int targetIndex = random.nextInt(routes.size());
        
        if (sourceIndex == targetIndex || routes.get(sourceIndex).isEmpty()) {
            return routes;
        }
        
        // 随机选择聚集区进行移动
        List<Accumulation> sourceRoute = routes.get(sourceIndex);
        int accIndex = random.nextInt(sourceRoute.size());
        Accumulation accToMove = sourceRoute.remove(accIndex);
        routes.get(targetIndex).add(accToMove);
        
        return routes;
    }
    
    /**
     * 随机交换操作
     */
    private List<List<Accumulation>> performRandomSwap(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到非空路线
        List<Integer> nonEmptyRoutes = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            if (!routes.get(i).isEmpty()) {
                nonEmptyRoutes.add(i);
            }
        }
        
        if (nonEmptyRoutes.size() < 2) return routes;
        
        // 随机选择两条路线
        int route1Index = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        int route2Index;
        do {
            route2Index = nonEmptyRoutes.get(random.nextInt(nonEmptyRoutes.size()));
        } while (route2Index == route1Index);
        
        List<Accumulation> route1 = routes.get(route1Index);
        List<Accumulation> route2 = routes.get(route2Index);
        
        // 随机选择聚集区进行交换
        int acc1Index = random.nextInt(route1.size());
        int acc2Index = random.nextInt(route2.size());
        
        Accumulation temp = route1.get(acc1Index);
        route1.set(acc1Index, route2.get(acc2Index));
        route2.set(acc2Index, temp);
        
        return routes;
    }
    
    /**
     * 随机分割或合并操作
     */
    private List<List<Accumulation>> performRandomSplitOrMerge(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        if (random.nextBoolean() && routes.size() > 1) {
            // 尝试合并操作
            return performRandomMerge(routes, depot, timeMatrix);
        } else {
            // 尝试分割操作
            return performRandomSplit(routes, depot, timeMatrix);
        }
    }
    
    /**
     * 随机合并操作
     */
    private List<List<Accumulation>> performRandomMerge(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        if (routes.size() < 2) return routes;
        
        // 随机选择两条路线进行合并
        int route1Index = random.nextInt(routes.size());
        int route2Index;
        do {
            route2Index = random.nextInt(routes.size());
        } while (route2Index == route1Index);
        
        List<Accumulation> route1 = routes.get(route1Index);
        List<Accumulation> route2 = routes.get(route2Index);
        
        // 合并路线
        List<Accumulation> mergedRoute = new ArrayList<>(route1);
        mergedRoute.addAll(route2);
        
        // 更新路线列表
        routes.set(route1Index, mergedRoute);
        routes.remove(route2Index);
        
        return routes;
    }
    
    /**
     * 随机分割操作
     */
    private List<List<Accumulation>> performRandomSplit(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        Random random = new Random();
        
        // 找到可分割的路线
        List<Integer> splittableRoutes = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            if (routes.get(i).size() >= 4) {
                splittableRoutes.add(i);
            }
        }
        
        if (splittableRoutes.isEmpty()) return routes;
        
        // 随机选择一条路线进行分割
        int routeIndex = splittableRoutes.get(random.nextInt(splittableRoutes.size()));
        List<Accumulation> routeToSplit = routes.get(routeIndex);
        
        // 随机选择分割点
        int splitPoint = 1 + random.nextInt(routeToSplit.size() - 2);
        
        // 创建两条新路线
        List<Accumulation> newRoute1 = new ArrayList<>(routeToSplit.subList(0, splitPoint));
        List<Accumulation> newRoute2 = new ArrayList<>(routeToSplit.subList(splitPoint, routeToSplit.size()));
        
        // 更新路线列表
        routes.set(routeIndex, newRoute1);
        routes.add(newRoute2);
        
        return routes;
    }
    
    /**
     * 评估种群
     */
    private void evaluatePopulation(
            List<Individual> population,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        for (Individual individual : population) {
            if (!individual.isEvaluated()) {
                double fitness = evaluateFitness(individual.getRoutes(), depot, timeMatrix);
                individual.setFitness(fitness);
            }
        }
    }
    
    /**
     * 评估个体适应度
     */
    private double evaluateFitness(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double fitness = 0.0;
        
        // 1. 约束满足评分
        double constraintScore = evaluateConstraintSatisfaction(routes, depot, timeMatrix);
        fitness += constraintScore * CONSTRAINT_WEIGHT;
        
        // 2. 时间平衡评分
        double balanceScore = evaluateTimeBalance(routes, depot, timeMatrix);
        fitness += balanceScore * BALANCE_WEIGHT;
        
        // 3. 地理合理性评分
        double geographicScore = evaluateGeographicRationality(routes);
        fitness += geographicScore * GEOGRAPHIC_WEIGHT;
        
        return fitness;
    }
    
    /**
     * 评估约束满足程度
     */
    private double evaluateConstraintSatisfaction(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        double score = 100.0;
        int violationCount = 0;
        double totalViolationTime = 0.0;
        
        for (List<Accumulation> route : routes) {
            double routeTime = calculateRouteTime(route, depot, timeMatrix);
            if (routeTime > MAX_ROUTE_TIME) {
                violationCount++;
                totalViolationTime += (routeTime - MAX_ROUTE_TIME);
            }
        }
        
        // 扣分：每个违反减15分，每超时1分钟减1分
        score -= violationCount * 15.0;
        score -= totalViolationTime;
        
        return Math.max(0.0, score);
    }
    
    /**
     * 评估时间平衡程度
     */
    private double evaluateTimeBalance(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (routes.isEmpty()) return 0.0;
        
        List<Double> routeTimes = routes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        double avgTime = routeTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = routeTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgTime, 2))
            .average().orElse(0.0);
        
        double stdDev = Math.sqrt(variance);
        return Math.max(0.0, 100.0 - stdDev);
    }
    
    /**
     * 评估地理合理性
     */
    private double evaluateGeographicRationality(List<List<Accumulation>> routes) {
        // 简化实现：基于路线数量和聚集区分布的均匀性
        if (routes.isEmpty()) return 0.0;
        
        int totalAccumulations = routes.stream().mapToInt(List::size).sum();
        if (totalAccumulations == 0) return 0.0;
        
        double avgRouteSize = (double) totalAccumulations / routes.size();
        double sizeVariance = routes.stream()
            .mapToDouble(route -> Math.pow(route.size() - avgRouteSize, 2))
            .average().orElse(0.0);
        
        double sizeStdDev = Math.sqrt(sizeVariance);
        double uniformityScore = Math.max(0.0, 100.0 - sizeStdDev * 10.0);
        
        return uniformityScore;
    }
    
    /**
     * 进化一代
     */
    private List<Individual> evolveGeneration(
            List<Individual> population,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        List<Individual> newPopulation = new ArrayList<>();
        Random random = new Random();
        
        // 生成新个体直到达到种群大小
        while (newPopulation.size() < POPULATION_SIZE) {
            
            if (random.nextDouble() < CROSSOVER_RATE) {
                // 交叉操作
                Individual parent1 = tournamentSelection(population);
                Individual parent2 = tournamentSelection(population);
                
                Individual offspring = crossover(parent1, parent2, depot, timeMatrix);
                if (offspring != null) {
                    newPopulation.add(offspring);
                }
            } else {
                // 直接选择
                Individual selected = tournamentSelection(population);
                newPopulation.add(selected.copy());
            }
        }
        
        // 变异操作
        for (Individual individual : newPopulation) {
            if (random.nextDouble() < MUTATION_RATE) {
                mutate(individual, depot, timeMatrix);
            }
        }
        
        return newPopulation;
    }
    
    /**
     * 锦标赛选择
     */
    private Individual tournamentSelection(List<Individual> population) {
        Random random = new Random();
        Individual best = null;
        
        for (int i = 0; i < TOURNAMENT_SIZE; i++) {
            Individual candidate = population.get(random.nextInt(population.size()));
            if (best == null || candidate.getFitness() > best.getFitness()) {
                best = candidate;
            }
        }
        
        return best;
    }
    
    /**
     * 交叉操作
     */
    private Individual crossover(
            Individual parent1,
            Individual parent2,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        try {
            // 使用部分匹配交叉（PMX）的变种
            List<List<Accumulation>> routes1 = parent1.getRoutes();
            List<List<Accumulation>> routes2 = parent2.getRoutes();
            
            // 创建子代路线列表
            List<List<Accumulation>> offspringRoutes = new ArrayList<>();
            Random random = new Random();
            
            // 收集所有聚集区
            Set<String> allAccIds = new HashSet<>();
            for (List<Accumulation> route : routes1) {
                for (Accumulation acc : route) {
                    allAccIds.add(String.valueOf(acc.getAccumulationId()));
                }
            }
            
            // 随机选择交叉点
            int maxRoutes = Math.max(routes1.size(), routes2.size());
            int crossoverPoint = random.nextInt(maxRoutes);
            
            // 从parent1复制前半部分路线
            for (int i = 0; i < crossoverPoint && i < routes1.size(); i++) {
                offspringRoutes.add(new ArrayList<>(routes1.get(i)));
            }
            
            // 收集已使用的聚集区
            Set<String> usedAccIds = new HashSet<>();
            for (List<Accumulation> route : offspringRoutes) {
                for (Accumulation acc : route) {
                    usedAccIds.add(String.valueOf(acc.getAccumulationId()));
                }
            }
            
            // 从parent2复制后半部分路线，但过滤已使用的聚集区
            for (int i = crossoverPoint; i < routes2.size(); i++) {
                List<Accumulation> newRoute = new ArrayList<>();
                for (Accumulation acc : routes2.get(i)) {
                    String accId = String.valueOf(acc.getAccumulationId());
                    if (!usedAccIds.contains(accId)) {
                        newRoute.add(acc);
                        usedAccIds.add(accId);
                    }
                }
                if (!newRoute.isEmpty()) {
                    offspringRoutes.add(newRoute);
                }
            }
            
            // 处理剩余未分配的聚集区
            List<Accumulation> unassignedAccumulations = new ArrayList<>();
            for (List<Accumulation> route : routes1) {
                for (Accumulation acc : route) {
                    String accId = String.valueOf(acc.getAccumulationId());
                    if (!usedAccIds.contains(accId)) {
                        unassignedAccumulations.add(acc);
                    }
                }
            }
            
            // 随机分配未分配的聚集区
            for (Accumulation acc : unassignedAccumulations) {
                if (offspringRoutes.isEmpty()) {
                    offspringRoutes.add(new ArrayList<>());
                }
                int routeIndex = random.nextInt(offspringRoutes.size());
                offspringRoutes.get(routeIndex).add(acc);
            }
            
            return new Individual(offspringRoutes);
            
        } catch (Exception e) {
            // 交叉失败，返回父代之一的副本
            return parent1.copy();
        }
    }
    
    /**
     * 变异操作
     */
    private void mutate(Individual individual, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        Random random = new Random();
        
        // 随机选择变异类型
        int mutationType = random.nextInt(3);
        
        try {
            switch (mutationType) {
                case 0:
                    // 重分配变异
                    individual.routes = performRandomReassignment(individual.routes, depot, timeMatrix);
                    break;
                case 1:
                    // 交换变异
                    individual.routes = performRandomSwap(individual.routes, depot, timeMatrix);
                    break;
                case 2:
                    // 分割或合并变异
                    individual.routes = performRandomSplitOrMerge(individual.routes, depot, timeMatrix);
                    break;
            }
            
            // 标记需要重新评估
            individual.evaluated = false;
            
        } catch (Exception e) {
            // 变异失败，个体保持不变
        }
    }
    
    /**
     * 精英保留策略选择幸存者
     */
    private List<Individual> selectSurvivors(
            List<Individual> oldPopulation,
            List<Individual> newPopulation) {
        
        // 合并两个种群
        List<Individual> combinedPopulation = new ArrayList<>();
        combinedPopulation.addAll(oldPopulation);
        combinedPopulation.addAll(newPopulation);
        
        // 按适应度排序
        combinedPopulation.sort((a, b) -> Double.compare(b.getFitness(), a.getFitness()));
        
        // 选择前POPULATION_SIZE个个体
        return combinedPopulation.subList(0, Math.min(POPULATION_SIZE, combinedPopulation.size()));
    }
    
    /**
     * 找到最优个体
     */
    private Individual findBestIndividual(List<Individual> population) {
        return population.stream()
            .max(Comparator.comparingDouble(Individual::getFitness))
            .orElse(population.get(0));
    }
    
    /**
     * 计算路线总时间
     */
    private double calculateRouteTime(
            List<Accumulation> route,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        if (route == null || route.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            }
        }
        
        // 往返时间
        for (Accumulation acc : route) {
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返
            } else {
                totalTime += 60.0; // 默认往返60分钟
            }
        }
        
        return totalTime;
    }
    
    /**
     * 计算优化指标
     */
    private OptimizationMetrics calculateOptimizationMetrics(
            List<List<Accumulation>> originalRoutes,
            List<List<Accumulation>> optimizedRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 计算原始指标
        double originalTotalTime = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> originalTimes = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long originalViolations = originalTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double originalAvg = originalTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double originalStdDev = Math.sqrt(originalTimes.stream()
            .mapToDouble(time -> Math.pow(time - originalAvg, 2))
            .average().orElse(0.0));
        
        // 计算优化后指标
        double optimizedTotalTime = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        List<Double> optimizedTimes = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(Collectors.toList());
        
        long optimizedViolations = optimizedTimes.stream()
            .mapToLong(time -> time > MAX_ROUTE_TIME ? 1 : 0)
            .sum();
        
        double optimizedAvg = optimizedTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double optimizedStdDev = Math.sqrt(optimizedTimes.stream()
            .mapToDouble(time -> Math.pow(time - optimizedAvg, 2))
            .average().orElse(0.0));
        
        // 计算改进指标
        double timeImprovement = originalTotalTime > 0 ? 
            (originalTotalTime - optimizedTotalTime) / originalTotalTime * 100.0 : 0.0;
        
        double timeBalanceImprovement = originalStdDev > 0 ? 
            (originalStdDev - optimizedStdDev) / originalStdDev * 100.0 : 0.0;
        
        int violationReduction = (int) (originalViolations - optimizedViolations);
        
        double constraintSatisfactionRate = optimizedRoutes.size() > 0 ? 
            1.0 - (double) optimizedViolations / optimizedRoutes.size() : 1.0;
        
        return OptimizationMetrics.builder()
            .originalTotalTime(originalTotalTime)
            .optimizedTotalTime(optimizedTotalTime)
            .timeImprovement(timeImprovement)
            .originalViolations((int) originalViolations)
            .optimizedViolations((int) optimizedViolations)
            .violationReduction(violationReduction)
            .constraintSatisfactionRate(constraintSatisfactionRate)
            .originalTimeStdDev(originalStdDev)
            .optimizedTimeStdDev(optimizedStdDev)
            .timeBalanceImprovement(timeBalanceImprovement)
            .geographicRationalityScore(0.8) // 默认地理合理性评分
            .convergenceScore(0.9) // 默认收敛性评分
            .build();
    }
    
    /**
     * 深度复制路线列表
     */
    private static List<List<Accumulation>> deepCopyRoutes(List<List<Accumulation>> originalRoutes) {
        return originalRoutes.stream()
            .map(ArrayList::new)
            .collect(Collectors.toList());
    }
    
    /**
     * 创建错误结果
     */
    private FallbackOptimizationResult createErrorResult(String message, long startTime) {
        return FallbackOptimizationResult.builder()
            .success(false)
            .optimizedRoutes(new ArrayList<>())
            .originalRouteCount(0)
            .optimizedRouteCount(0)
            .strategy(FallbackStrategy.GENETIC_ALGORITHM)
            .message(message)
            .executionTimeMs(System.currentTimeMillis() - startTime)
            .build();
    }
}