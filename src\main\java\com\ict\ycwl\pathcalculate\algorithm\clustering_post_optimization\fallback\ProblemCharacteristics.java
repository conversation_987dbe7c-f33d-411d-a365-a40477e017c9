package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import lombok.Builder;
import lombok.Data;

/**
 * 问题特征分析
 * 
 * 分析路线优化问题的特征，为降级策略选择提供依据
 * 包括问题规模、约束违反情况、时间分布等关键特征
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class ProblemCharacteristics {
    
    /**
     * 路线数量
     */
    private int routeCount;
    
    /**
     * 总聚集区数量
     */
    private int totalAccumulations;
    
    /**
     * 平均路线时间（分钟）
     */
    private double averageRouteTime;
    
    /**
     * 路线时间方差
     */
    private double timeVariance;
    
    /**
     * 约束违反率（0.0-1.0）
     */
    private double constraintViolationRate;
    
    /**
     * 问题复杂度评分
     */
    private double problemComplexity;
    
    /**
     * 最大路线时间（分钟）
     */
    private double maxRouteTime;
    
    /**
     * 最小路线时间（分钟）
     */
    private double minRouteTime;
    
    /**
     * 时间标准差
     */
    private double timeStandardDeviation;
    
    /**
     * 平均聚集区密度（聚集区数/路线数）
     */
    private double averageAccumulationDensity;
    
    /**
     * 获取问题规模等级
     */
    public ProblemScale getScale() {
        if (routeCount > 50) return ProblemScale.LARGE;
        if (routeCount > 20) return ProblemScale.MEDIUM;
        return ProblemScale.SMALL;
    }
    
    /**
     * 获取约束违反严重程度
     */
    public ViolationSeverity getViolationSeverity() {
        if (constraintViolationRate > 0.5) return ViolationSeverity.SEVERE;
        if (constraintViolationRate > 0.3) return ViolationSeverity.MODERATE;
        if (constraintViolationRate > 0.1) return ViolationSeverity.MILD;
        return ViolationSeverity.MINIMAL;
    }
    
    /**
     * 获取时间分布均匀程度
     */
    public TimeDistribution getTimeDistribution() {
        if (averageRouteTime == 0) return TimeDistribution.UNDEFINED;
        
        double coefficientOfVariation = timeStandardDeviation / averageRouteTime;
        
        if (coefficientOfVariation > 0.3) return TimeDistribution.HIGHLY_UNEVEN;
        if (coefficientOfVariation > 0.2) return TimeDistribution.MODERATELY_UNEVEN;
        if (coefficientOfVariation > 0.1) return TimeDistribution.SLIGHTLY_UNEVEN;
        return TimeDistribution.EVEN;
    }
    
    /**
     * 计算负载均衡指数
     */
    public double getLoadBalanceIndex() {
        if (maxRouteTime == minRouteTime) return 1.0;
        return 1.0 - (maxRouteTime - minRouteTime) / (maxRouteTime + minRouteTime);
    }
    
    /**
     * 计算问题难度评分（0.0-10.0）
     */
    public double getDifficultyScore() {
        double score = 0.0;
        
        // 规模因素（0-3分）
        switch (getScale()) {
            case SMALL: score += 1.0; break;
            case MEDIUM: score += 2.0; break;
            case LARGE: score += 3.0; break;
        }
        
        // 约束违反因素（0-3分）
        switch (getViolationSeverity()) {
            case MINIMAL: score += 0.5; break;
            case MILD: score += 1.5; break;
            case MODERATE: score += 2.5; break;
            case SEVERE: score += 3.0; break;
        }
        
        // 时间分布因素（0-2分）
        switch (getTimeDistribution()) {
            case EVEN: score += 0.5; break;
            case SLIGHTLY_UNEVEN: score += 1.0; break;
            case MODERATELY_UNEVEN: score += 1.5; break;
            case HIGHLY_UNEVEN: score += 2.0; break;
        }
        
        // 复杂度因素（0-2分）
        score += Math.min(2.0, problemComplexity / 10.0 * 2.0);
        
        return Math.min(10.0, score);
    }
    
    /**
     * 推荐最适合的降级策略
     */
    public FallbackStrategy recommendBestStrategy() {
        // 基于问题特征的策略推荐逻辑
        
        // 大规模问题：优先遗传算法或混合策略
        if (getScale() == ProblemScale.LARGE) {
            if (constraintViolationRate > 0.3) {
                return FallbackStrategy.HYBRID; // 混合策略处理复杂大规模问题
            }
            return FallbackStrategy.GENETIC_ALGORITHM;
        }
        
        // 严重约束违反：模拟退火
        if (getViolationSeverity() == ViolationSeverity.SEVERE) {
            return FallbackStrategy.SIMULATED_ANNEALING;
        }
        
        // 高度不均匀分布：变邻域搜索
        if (getTimeDistribution() == TimeDistribution.HIGHLY_UNEVEN) {
            return FallbackStrategy.VARIABLE_NEIGHBORHOOD_SEARCH;
        }
        
        // 中等规模且有适度问题：混合策略
        if (getScale() == ProblemScale.MEDIUM && getDifficultyScore() > 5.0) {
            return FallbackStrategy.HYBRID;
        }
        
        // 小规模问题：局部搜索
        if (getScale() == ProblemScale.SMALL) {
            return FallbackStrategy.LOCAL_SEARCH;
        }
        
        // 默认：混合策略
        return FallbackStrategy.HYBRID;
    }
    
    /**
     * 评估策略适用性
     */
    public double evaluateStrategyFitness(FallbackStrategy strategy) {
        double fitness = 0.0;
        
        switch (strategy) {
            case SIMULATED_ANNEALING:
                // 适合约束违反严重的问题
                fitness += (constraintViolationRate > 0.3) ? 0.8 : 0.4;
                // 适合中大规模问题
                fitness += (routeCount > 20) ? 0.6 : 0.3;
                break;
                
            case GENETIC_ALGORITHM:
                // 非常适合大规模问题
                fitness += (routeCount > 50) ? 1.0 : (routeCount > 20) ? 0.6 : 0.2;
                // 适合复杂问题
                fitness += (problemComplexity > 15) ? 0.7 : 0.4;
                break;
                
            case VARIABLE_NEIGHBORHOOD_SEARCH:
                // 适合时间分布不均匀的问题
                fitness += (getTimeDistribution().ordinal() >= 2) ? 0.8 : 0.4;
                // 适合中等规模问题
                fitness += (routeCount >= 20 && routeCount <= 50) ? 0.7 : 0.4;
                break;
                
            case LOCAL_SEARCH:
                // 适合小规模问题
                fitness += (routeCount < 20) ? 0.9 : 0.3;
                // 适合约束违反较少的问题
                fitness += (constraintViolationRate < 0.2) ? 0.6 : 0.2;
                break;
                
            case HYBRID:
                // 适合复杂问题
                fitness += (getDifficultyScore() > 6.0) ? 0.9 : 0.6;
                // 适合大中规模问题
                fitness += (routeCount > 20) ? 0.8 : 0.5;
                break;
        }
        
        return Math.min(1.0, fitness);
    }
    
    /**
     * 生成特征摘要
     */
    public String generateSummary() {
        return String.format(
            "规模:%s(%d路线,%d聚集区) | 违反率:%.1f%%(%s) | 时间分布:%s | 难度:%.1f/10 | 推荐:%s",
            getScale().getDisplayName(),
            routeCount,
            totalAccumulations,
            constraintViolationRate * 100.0,
            getViolationSeverity().getDisplayName(),
            getTimeDistribution().getDisplayName(),
            getDifficultyScore(),
            recommendBestStrategy().getCode()
        );
    }
    
    /**
     * 生成详细分析报告
     */
    public String generateDetailedAnalysis() {
        StringBuilder analysis = new StringBuilder();
        
        analysis.append("🔍 问题特征详细分析\n");
        analysis.append("══════════════════════════════════════════════════════\n");
        
        // 基本信息
        analysis.append("📏 问题规模:\n");
        analysis.append(String.format("   路线数量: %d条 (%s)\n", routeCount, getScale().getDisplayName()));
        analysis.append(String.format("   聚集区总数: %d个\n", totalAccumulations));
        analysis.append(String.format("   平均密度: %.1f个/路线\n", averageAccumulationDensity));
        
        // 时间特征
        analysis.append(String.format("\n⏱️ 时间特征:\n"));
        analysis.append(String.format("   平均时间: %.1f分钟\n", averageRouteTime));
        analysis.append(String.format("   时间范围: %.1f - %.1f分钟\n", minRouteTime, maxRouteTime));
        analysis.append(String.format("   标准差: %.2f分钟\n", timeStandardDeviation));
        analysis.append(String.format("   方差: %.2f\n", timeVariance));
        analysis.append(String.format("   分布类型: %s\n", getTimeDistribution().getDisplayName()));
        
        // 约束情况
        analysis.append(String.format("\n🚫 约束违反情况:\n"));
        analysis.append(String.format("   违反率: %.1f%% (%s)\n", 
            constraintViolationRate * 100.0, getViolationSeverity().getDisplayName()));
        analysis.append(String.format("   负载均衡指数: %.3f\n", getLoadBalanceIndex()));
        
        // 复杂度评估
        analysis.append(String.format("\n📊 复杂度评估:\n"));
        analysis.append(String.format("   问题复杂度: %.2f\n", problemComplexity));
        analysis.append(String.format("   难度评分: %.1f/10.0\n", getDifficultyScore()));
        
        // 策略推荐
        FallbackStrategy recommended = recommendBestStrategy();
        analysis.append(String.format("\n🎯 策略建议:\n"));
        analysis.append(String.format("   推荐策略: %s\n", recommended.getName()));
        analysis.append(String.format("   适用性评分: %.2f\n", evaluateStrategyFitness(recommended)));
        analysis.append(String.format("   推荐理由: %s\n", getRecommendationReason(recommended)));
        
        analysis.append("══════════════════════════════════════════════════════\n");
        
        return analysis.toString();
    }
    
    /**
     * 获取推荐理由
     */
    private String getRecommendationReason(FallbackStrategy strategy) {
        switch (strategy) {
            case SIMULATED_ANNEALING:
                return "约束违反严重，需要全局搜索能力强的算法";
            case GENETIC_ALGORITHM:
                return "大规模问题，适合并行群体优化";
            case VARIABLE_NEIGHBORHOOD_SEARCH:
                return "时间分布不均匀，需要多邻域局部搜索";
            case LOCAL_SEARCH:
                return "小规模问题，局部搜索效率高";
            case HYBRID:
                return "复杂问题，需要多算法组合策略";
            default:
                return "综合考虑问题特征";
        }
    }
    
    /**
     * 问题规模枚举
     */
    public enum ProblemScale {
        SMALL("小规模", "<20路线"),
        MEDIUM("中等规模", "20-50路线"),
        LARGE("大规模", ">50路线");
        
        private final String displayName;
        private final String description;
        
        ProblemScale(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 约束违反严重程度枚举
     */
    public enum ViolationSeverity {
        MINIMAL("轻微", "<10%"),
        MILD("一般", "10-30%"),
        MODERATE("中等", "30-50%"),
        SEVERE("严重", ">50%");
        
        private final String displayName;
        private final String range;
        
        ViolationSeverity(String displayName, String range) {
            this.displayName = displayName;
            this.range = range;
        }
        
        public String getDisplayName() { return displayName; }
        public String getRange() { return range; }
    }
    
    /**
     * 时间分布类型枚举
     */
    public enum TimeDistribution {
        EVEN("均匀分布"),
        SLIGHTLY_UNEVEN("轻微不均"),
        MODERATELY_UNEVEN("中度不均"),
        HIGHLY_UNEVEN("高度不均"),
        UNDEFINED("未定义");
        
        private final String displayName;
        
        TimeDistribution(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() { return displayName; }
    }
}