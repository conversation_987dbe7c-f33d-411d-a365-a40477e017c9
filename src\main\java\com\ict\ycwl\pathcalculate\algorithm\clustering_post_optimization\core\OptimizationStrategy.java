package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

/**
 * 优化策略枚举
 * 
 * 定义聚类二次优化中可用的各种策略，
 * 每种策略适用于不同的约束违反模式
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
public enum OptimizationStrategy {
    
    /**
     * OptaPlanner约束求解策略
     * 适用场景：严重的450分钟约束违反
     * 优势：强大的硬约束求解能力
     */
    OPTAPLANNER_CONSTRAINTS("OptaPlanner约束求解", 
                           "专注于硬约束满足的约束规划求解器",
                           OptimizationType.CONSTRAINT_SOLVING,
                           120), // 2分钟时间限制
    
    /**
     * JSPRIT负载均衡策略
     * 适用场景：30分钟时间差异违反
     * 优势：专业的负载均衡和VRP优化
     */
    JSPRIT_LOAD_BALANCE("JSPRIT负载均衡",
                       "专注于负载均衡的车辆路径问题求解器", 
                       OptimizationType.LOAD_BALANCING,
                       90), // 1.5分钟时间限制
    
    /**
     * OR-Tools几何优化策略
     * 适用场景：地理分散性问题
     * 优势：强大的几何约束和数学优化
     */
    ORTOOLS_GEOMETRIC("OR-Tools几何优化",
                     "专注于几何约束的数学优化求解器",
                     OptimizationType.GEOMETRIC_OPTIMIZATION,
                     120), // 2分钟时间限制
    
    /**
     * 混合策略：OptaPlanner + JSPRIT
     * 适用场景：同时存在时间约束和负载均衡问题
     * 优势：结合两者的优势
     */
    HYBRID_OPTAPLANNER_JSPRIT("混合策略(OptaPlanner+JSPRIT)",
                             "结合约束求解和负载均衡的混合策略",
                             OptimizationType.HYBRID,
                             180), // 3分钟时间限制
    
    /**
     * 混合策略：全方位优化
     * 适用场景：复杂的多重约束违反
     * 优势：使用所有可用的优化技术
     */
    HYBRID_ALL("全方位混合策略",
              "使用所有可用优化技术的综合策略",
              OptimizationType.HYBRID,
              240), // 4分钟时间限制
    
    /**
     * 简单启发式策略
     * 适用场景：轻微违反或性能要求高的场景
     * 优势：快速执行，资源消耗低
     */
    SIMPLE_HEURISTIC("简单启发式",
                    "基于贪心算法的快速优化策略",
                    OptimizationType.HEURISTIC,
                    30); // 30秒时间限制
    
    private final String name;
    private final String description;
    private final OptimizationType type;
    private final int timeoutSeconds;
    
    OptimizationStrategy(String name, String description, OptimizationType type, int timeoutSeconds) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public OptimizationType getType() {
        return type;
    }
    
    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    /**
     * 检查是否为混合策略
     */
    public boolean isHybridStrategy() {
        return type == OptimizationType.HYBRID;
    }
    
    /**
     * 检查是否为启发式策略
     */
    public boolean isHeuristicStrategy() {
        return type == OptimizationType.HEURISTIC;
    }
    
    /**
     * 检查是否适用于约束求解
     */
    public boolean isConstraintSolvingCapable() {
        return this == OPTAPLANNER_CONSTRAINTS || 
               this == HYBRID_OPTAPLANNER_JSPRIT || 
               this == HYBRID_ALL;
    }
    
    /**
     * 检查是否适用于负载均衡
     */
    public boolean isLoadBalancingCapable() {
        return this == JSPRIT_LOAD_BALANCE || 
               this == HYBRID_OPTAPLANNER_JSPRIT || 
               this == HYBRID_ALL;
    }
    
    /**
     * 检查是否适用于几何优化
     */
    public boolean isGeometricOptimizationCapable() {
        return this == ORTOOLS_GEOMETRIC || this == HYBRID_ALL;
    }
    
    /**
     * 获取策略的资源消耗等级
     */
    public ResourceConsumptionLevel getResourceConsumptionLevel() {
        switch (this) {
            case SIMPLE_HEURISTIC:
                return ResourceConsumptionLevel.LOW;
            case JSPRIT_LOAD_BALANCE:
                return ResourceConsumptionLevel.MEDIUM;
            case OPTAPLANNER_CONSTRAINTS:
            case ORTOOLS_GEOMETRIC:
                return ResourceConsumptionLevel.HIGH;
            case HYBRID_OPTAPLANNER_JSPRIT:
                return ResourceConsumptionLevel.HIGH;
            case HYBRID_ALL:
                return ResourceConsumptionLevel.VERY_HIGH;
            default:
                return ResourceConsumptionLevel.MEDIUM;
        }
    }
    
    /**
     * 根据约束违反模式推荐策略
     */
    public static OptimizationStrategy recommendStrategy(
        int maxTimeViolations,
        boolean timeGapViolation,
        int geographicViolations
    ) {
        // 决策逻辑
        if (maxTimeViolations >= 3) {
            // 严重的450分钟约束违反
            return OPTAPLANNER_CONSTRAINTS;
        } else if (timeGapViolation && maxTimeViolations >= 1) {
            // 同时有时间约束和差异问题
            return HYBRID_OPTAPLANNER_JSPRIT;
        } else if (geographicViolations >= 2) {
            // 地理分散问题
            return ORTOOLS_GEOMETRIC;
        } else if (timeGapViolation) {
            // 主要是负载均衡问题
            return JSPRIT_LOAD_BALANCE;
        } else if (maxTimeViolations > 0) {
            // 轻微的时间约束违反
            return OPTAPLANNER_CONSTRAINTS;
        } else {
            // 没有明显违反，使用简单启发式
            return SIMPLE_HEURISTIC;
        }
    }
    
    /**
     * 优化类型枚举
     */
    public enum OptimizationType {
        CONSTRAINT_SOLVING,      // 约束求解
        LOAD_BALANCING,         // 负载均衡
        GEOMETRIC_OPTIMIZATION, // 几何优化
        HEURISTIC,              // 启发式
        HYBRID                  // 混合策略
    }
    
    /**
     * 资源消耗等级枚举
     */
    public enum ResourceConsumptionLevel {
        LOW,        // 低消耗
        MEDIUM,     // 中等消耗
        HIGH,       // 高消耗
        VERY_HIGH   // 极高消耗
    }
}