package com.ict.ycwl.pathcalculate.AOP;

import java.lang.annotation.*;

@Target(ElementType.METHOD)  // 改为方法级注解
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ConfigFileMothodDataSource {
    /**
     * 配置文件中定义文件路径的键名
     * 示例: "datasource.config-path"
     */
    String configKey();
    
    /**
     * 文件编码格式（默认UTF-8）
     */
    String encoding() default "UTF-8";
    
    /**
     * 读取失败时的默认数据源
     */
    String defaultDS() default "master";
}