# 路径规划算法集成说明

## 📋 概述

本项目已成功集成第三方库实现高性能路径规划算法，主要特性：

- **TSP求解**：Google OR-Tools（精确算法）+ 启发式算法（大规模问题）
- **凸包计算**：JTS Topology Suite（工业标准）
- **统计计算**：Apache Commons Math（标准差、方差等）
- **遗传算法**：JENETICS（备用TSP求解器）
- **核心业务逻辑**：自实现工作量均衡聚类和多层级时间均衡

## 🔧 依赖库

### 新增依赖
```xml
<!-- OR-Tools for TSP solving -->
<dependency>
    <groupId>com.google.ortools</groupId>
    <artifactId>ortools-java</artifactId>
    <version>9.8.3296</version>
</dependency>

<!-- Apache Commons Math for statistical calculations -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-math3</artifactId>
    <version>3.6.1</version>
</dependency>

<!-- JENETICS for genetic algorithms -->
<dependency>
    <groupId>io.jenetics</groupId>
    <artifactId>jenetics</artifactId>
    <version>7.2.0</version>
</dependency>

<!-- Jackson for JSON processing -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.15.2</version>
</dependency>
```

### 已有依赖
- JTS Topology Suite（凸包计算）
- Spring Boot（依赖注入）
- Lombok（代码简化）

## 🏗️ 架构设计

### 核心类结构

```
algorithm/
├── PathPlanningUtils.java           // 主入口，六阶段流程编排
├── core/                           // 核心算法组件
│   ├── AlgorithmContext.java       // 算法执行上下文
│   ├── AlgorithmParameters.java    // 算法参数配置
│   ├── DataValidator.java          // 数据验证器
│   ├── DataPreprocessor.java       // 数据预处理器
│   ├── WorkloadBalancedKMeans.java // 工作量均衡聚类
│   ├── TSPSolverManager.java       // TSP求解器管理
│   ├── ConvexHullManager.java      // 凸包管理器（JTS）
│   └── TimeBalanceOptimizer.java   // 时间均衡优化器
├── entity/                         // 数据实体
└── dto/                           // 数据传输对象
```

## 🚀 使用方式

### 基本调用
```java
@Autowired
private PathPlanningUtils pathPlanningUtils;

public void runPathPlanning() {
    // 构建请求数据
    PathPlanningRequest request = PathPlanningRequest.builder()
        .accumulations(accumulationList)
        .transitDepots(transitDepotList)
        .teams(teamList)
        .timeMatrix(timeMatrix)
        .build();
    
    // 执行算法
    PathPlanningResult result = PathPlanningUtils.calculate(request);
    
    // 处理结果
    if (result.isSuccess()) {
        List<RouteResult> routes = result.getRoutes();
        TimeBalanceStats stats = result.getTimeBalanceStats();
        // 使用路线结果...
    } else {
        log.error("路径规划失败: {}", result.getErrorMessage());
    }
}
```

### 算法参数配置
```java
// 参数在 AlgorithmParameters 类中统一管理
public class AlgorithmParameters {
    // TSP算法选择阈值
    public static final int DP_MAX_NODES = 12;           // 动态规划最大节点数
    public static final int BRANCH_BOUND_MAX_NODES = 20; // 分支定界最大节点数
    
    // 时间均衡阈值
    public static final double ROUTE_TIME_GAP_THRESHOLD = 30.0;  // 路线间差距
    public static final double DEPOT_TIME_GAP_THRESHOLD = 60.0;  // 中转站间差距
    public static final double TEAM_TIME_GAP_THRESHOLD = 120.0;  // 班组间差距
    
    // 点权和边权系数
    public static final double DELIVERY_TIME_WEIGHT = 1.0;      // 配送时间权重
    public static final double TRAVEL_TIME_WEIGHT = 1.0;        // 行驶时间权重
}
```

## 🔄 算法流程

### 六个主要阶段

1. **数据验证和预处理**
   - 输入数据完整性检查
   - 时间矩阵覆盖度验证
   - 构建内部索引和分组关系

2. **初始路线分配（聚类）**
   - 基于工作量均衡的K-means聚类
   - 考虑地理位置和配送时间
   - 动态调整聚类中心

3. **路线内序列优化（TSP）**
   - 自动选择求解算法：
     - ≤12个点：动态规划（精确解）
     - ≤20个点：分支定界算法
     - >20个点：启发式算法（贪心+2-opt）

4. **凸包生成与冲突解决**
   - 使用JTS库生成凸包
   - 检测重叠冲突
   - 通过聚集区转移解决冲突

5. **多层级时间均衡**
   - 路线级：同一中转站下的路线均衡
   - 中转站级：同一班组下的中转站均衡
   - 班组级：全局班组间均衡观察

6. **结果构建**
   - 生成最终路线结果
   - 计算时间均衡统计
   - 记录算法执行信息

## 📊 性能特性

### TSP求解性能
| 节点数量 | 算法选择 | 时间复杂度 | 典型耗时 |
|---------|---------|-----------|----------|
| ≤12     | 动态规划 | O(n²2ⁿ)   | <1秒     |
| 13-20   | 分支定界 | 指数级    | 1-5秒    |
| >20     | 启发式   | O(n²)     | <1秒     |

### 聚类性能
- **K-means收敛**：通常5-20次迭代
- **工作量均衡**：标准差作为优化目标
- **地理约束**：Haversine距离计算

### 凸包处理
- **JTS库**：工业级几何计算
- **重叠检测**：精确相交面积计算
- **冲突解决**：智能聚集区转移

## 🎯 关键特性

### 1. 点权重视
- 配送时间作为点权重集成到所有算法中
- TSP成本函数：`travel_time + delivery_time`
- 聚类考虑工作量均衡而非仅地理距离

### 2. 多层级均衡
- **路线级**：最大差距30分钟内
- **中转站级**：最大差距60分钟内  
- **班组级**：最大差距120分钟内

### 3. 智能算法选择
- 根据问题规模自动选择最优算法
- 精确算法优先，启发式算法备用
- 时间限制保证算法不会无限运行

### 4. 容错性强
- 时间矩阵缺失自动补全（Haversine估算）
- 凸包生成失败优雅降级
- 数据验证提供详细错误信息

## 🔍 调试和监控

### 日志级别
```properties
# 算法详细日志
logging.level.com.ict.ycwl.pathcalculate.algorithm=DEBUG

# 第三方库日志
logging.level.org.locationtech.jts=INFO
```

### 性能监控
```java
// 算法执行统计
PathPlanningResult result = PathPlanningUtils.calculate(request);
log.info("算法耗时: {}ms", result.getExecutionTime());
log.info("生成路线: {}条", result.getRoutes().size());
log.info("总工作时间: {}分钟", result.getTotalWorkTime());
```

## ⚠️ 注意事项

### 1. 内存使用
- 动态规划算法：O(n×2ⁿ)内存，限制≤12节点
- 时间矩阵：全量加载到内存，大数据集需注意

### 2. 线程安全
- 所有算法类都是无状态的，线程安全
- AlgorithmContext在单次执行中线程不安全

### 3. OR-Tools依赖
- 需要本地库文件，部署时确保环境支持
- 首次加载可能较慢，建议预热

### 4. 坐标系统
- 统一使用WGS84坐标系（经纬度）
- 距离计算使用Haversine公式

## 🔧 故障排除

### 常见问题

1. **OR-Tools加载失败**
   ```
   解决方案：确保系统支持OR-Tools本地库，考虑使用Docker部署
   ```

2. **时间矩阵覆盖度不足**
   ```
   解决方案：算法会自动补全缺失数据，但建议提供完整时间矩阵
   ```

3. **内存不足**
   ```
   解决方案：减少单次处理的聚集区数量，或增加JVM内存配置
   ```

4. **TSP求解时间过长**
   ```
   解决方案：检查TSP_TIME_LIMIT_SECONDS参数，确保合理设置
   ```

## 📈 性能优化建议

1. **批量处理**：对大量路线规划任务使用并行处理
2. **缓存策略**：对时间矩阵实施缓存策略
3. **参数调优**：根据实际业务调整算法参数阈值
4. **数据预处理**：提前验证和清洗输入数据

---

*最后更新：2024年1月* 