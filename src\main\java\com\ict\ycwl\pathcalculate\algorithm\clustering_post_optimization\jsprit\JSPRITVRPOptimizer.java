package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain.*;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.config.JSPRITAlgorithmConfig;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.config.JSPRITAlgorithmConfig.JSPRITParameters;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.converter.VRPProblemConverter;

import com.graphhopper.jsprit.core.algorithm.VehicleRoutingAlgorithm;
import com.graphhopper.jsprit.core.problem.VehicleRoutingProblem;
import com.graphhopper.jsprit.core.problem.solution.VehicleRoutingProblemSolution;
import com.graphhopper.jsprit.core.util.Solutions;
import com.graphhopper.jsprit.core.algorithm.listener.VehicleRoutingAlgorithmListeners;
import com.graphhopper.jsprit.core.algorithm.listener.IterationStartsListener;
import com.graphhopper.jsprit.core.algorithm.SearchStrategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JSPRIT VRP优化器
 * 
 * 聚类二次优化的第三层：VRP精细化优化
 * 使用JSPRIT库进行车辆路径优化，专注于负载均衡和路径精细化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Slf4j
@Component
public class JSPRITVRPOptimizer {
    
    @Autowired
    private JSPRITAlgorithmConfig algorithmConfig;
    
    @Autowired
    private VRPProblemConverter problemConverter;
    
    /**
     * 优化执行统计
     */
    public static class OptimizationStatistics {
        private long startTime;
        private long endTime;
        private int iterationsExecuted;
        private double initialCost;
        private double finalCost;
        private boolean successful;
        private String errorMessage;
        private Map<String, Object> additionalMetrics;
        
        public OptimizationStatistics() {
            this.additionalMetrics = new HashMap<>();
        }
        
        public double getImprovementPercentage() {
            if (initialCost > 0) {
                return ((initialCost - finalCost) / initialCost) * 100.0;
            }
            return 0.0;
        }
        
        public long getExecutionTimeMillis() {
            return endTime - startTime;
        }
        
        // Getters and setters
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public int getIterationsExecuted() { return iterationsExecuted; }
        public void setIterationsExecuted(int iterationsExecuted) { this.iterationsExecuted = iterationsExecuted; }
        public double getInitialCost() { return initialCost; }
        public void setInitialCost(double initialCost) { this.initialCost = initialCost; }
        public double getFinalCost() { return finalCost; }
        public void setFinalCost(double finalCost) { this.finalCost = finalCost; }
        public boolean isSuccessful() { return successful; }
        public void setSuccessful(boolean successful) { this.successful = successful; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public Map<String, Object> getAdditionalMetrics() { return additionalMetrics; }
        public void setAdditionalMetrics(Map<String, Object> additionalMetrics) { this.additionalMetrics = additionalMetrics; }
    }
    
    /**
     * 快速VRP优化（30秒时间限制）
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 优化后的聚类结果
     */
    public List<List<Accumulation>> quickOptimize(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.info("🚀 开始JSPRIT快速VRP优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotId(), clusters.size());
        
        VRPConstraints constraints = VRPConstraints.createDefaultConstraints();
        JSPRITParameters parameters = algorithmConfig.createQuickOptimizationParameters();
        
        return optimizeWithParameters(depot, clusters, timeMatrix, constraints, parameters);
    }
    
    /**
     * 深度VRP优化（5分钟时间限制）
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 优化后的聚类结果
     */
    public List<List<Accumulation>> deepOptimize(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.info("🎯 开始JSPRIT深度VRP优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotId(), clusters.size());
        
        VRPConstraints constraints = VRPConstraints.createStrictConstraints();
        JSPRITParameters parameters = algorithmConfig.createDeepOptimizationParameters();
        
        return optimizeWithParameters(depot, clusters, timeMatrix, constraints, parameters);
    }
    
    /**
     * 自定义约束VRP优化
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @param constraints 自定义约束
     * @return 优化后的聚类结果
     */
    public List<List<Accumulation>> optimizeWithConstraints(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints
    ) {
        log.info("⚙️ 开始JSPRIT自定义约束VRP优化 - 中转站: {}, 路线数: {}", 
            depot.getTransitDepotId(), clusters.size());
        
        int vehicleCount = clusters.size();
        int serviceCount = clusters.stream().mapToInt(List::size).sum();
        JSPRITParameters parameters = algorithmConfig.selectParametersForProblem(vehicleCount, serviceCount, constraints);
        
        return optimizeWithParameters(depot, clusters, timeMatrix, constraints, parameters);
    }
    
    /**
     * 使用指定参数进行VRP优化
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @param constraints 约束配置
     * @param parameters 算法参数
     * @return 优化后的聚类结果
     */
    private List<List<Accumulation>> optimizeWithParameters(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints,
        JSPRITParameters parameters
    ) {
        OptimizationStatistics stats = new OptimizationStatistics();
        stats.setStartTime(System.currentTimeMillis());
        
        try {
            // 输入验证
            VRPValidationResult validation = validateInput(depot, clusters, timeMatrix);
            if (!validation.getIsValid()) {
                log.error("❌ 输入验证失败: {}", validation.getValidationSummary());
                stats.setSuccessful(false);
                stats.setErrorMessage("输入验证失败");
                return deepCopyOfClusters(clusters);
            }
            
            // 转换为JSPRIT问题
            log.debug("🔄 转换聚类结果为JSPRIT VRP问题");
            VehicleRoutingProblem vrpProblem = problemConverter.convertToJspritProblem(
                depot, clusters, timeMatrix, constraints);
            
            // 配置算法
            log.debug("⚙️ 配置JSPRIT算法: {}", algorithmConfig.getParameterSummary(parameters));
            VehicleRoutingAlgorithm algorithm = algorithmConfig.configureAlgorithm(vrpProblem, parameters);
            
            // 添加监听器以收集统计信息
            addOptimizationListeners(algorithm, stats);
            
            // 执行优化
            log.info("🔥 开始JSPRIT算法求解...");
            Instant optimizationStart = Instant.now();
            
            Collection<VehicleRoutingProblemSolution> solutions = algorithm.searchSolutions();
            
            Duration optimizationTime = Duration.between(optimizationStart, Instant.now());
            log.info("⏱️ JSPRIT优化完成，耗时: {}ms", optimizationTime.toMillis());
            
            // 获取最优解
            VehicleRoutingProblemSolution bestSolution = Solutions.bestOf(solutions);
            if (bestSolution == null) {
                log.error("❌ JSPRIT未找到有效解");
                stats.setSuccessful(false);
                stats.setErrorMessage("未找到有效解");
                return deepCopyOfClusters(clusters);
            }
            
            stats.setFinalCost(bestSolution.getCost());
            
            // 转换解决方案
            log.debug("🔄 转换JSPRIT解决方案为聚类结果");
            List<List<Accumulation>> optimizedClusters = problemConverter.convertFromJspritSolution(
                bestSolution, clusters, depot);
            
            // 验证优化结果
            VRPValidationResult resultValidation = validateOptimizationResult(
                clusters, optimizedClusters, depot, timeMatrix, constraints);
            
            if (!resultValidation.getIsValid()) {
                log.warn("⚠️ 优化结果验证失败，返回原始聚类: {}", resultValidation.getValidationSummary());
                stats.setSuccessful(false);
                stats.setErrorMessage("结果验证失败");
                return deepCopyOfClusters(clusters);
            }
            
            // 记录优化统计
            stats.setSuccessful(true);
            logOptimizationResults(clusters, optimizedClusters, stats, depot, timeMatrix);
            
            return optimizedClusters;
            
        } catch (Exception e) {
            log.error("❌ JSPRIT优化异常: {}", e.getMessage(), e);
            stats.setSuccessful(false);
            stats.setErrorMessage(e.getMessage());
            return deepCopyOfClusters(clusters);
            
        } finally {
            stats.setEndTime(System.currentTimeMillis());
        }
    }
    
    /**
     * 验证输入数据
     * 
     * @param depot 中转站
     * @param clusters 聚类结果
     * @param timeMatrix 时间矩阵
     * @return 验证结果
     */
    private VRPValidationResult validateInput(
        TransitDepot depot,
        List<List<Accumulation>> clusters,
        Map<String, TimeInfo> timeMatrix
    ) {
        VRPValidationResult.VRPValidationResultBuilder resultBuilder = VRPValidationResult.builder()
            .isValid(true)
            .validationType(VRPValidationResult.ValidationType.BASIC_VALIDATION);
        
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 基本输入检查
        if (depot == null) {
            errors.add("中转站不能为空");
        }
        
        if (clusters == null || clusters.isEmpty()) {
            errors.add("聚类结果不能为空");
        } else {
            // 检查空聚类
            long emptyClusters = clusters.stream().filter(List::isEmpty).count();
            if (emptyClusters > 0) {
                warnings.add(String.format("发现%d个空聚类", emptyClusters));
            }
            
            // 检查聚集区数据完整性
            for (int i = 0; i < clusters.size(); i++) {
                List<Accumulation> cluster = clusters.get(i);
                for (int j = 0; j < cluster.size(); j++) {
                    Accumulation acc = cluster.get(j);
                    if (acc == null) {
                        errors.add(String.format("聚类%d中存在null聚集区", i));
                    } else if (acc.getAccumulationId() == null) {
                        errors.add(String.format("聚类%d中聚集区%d缺少ID", i, j));
                    } else if (acc.getLongitude() == null || acc.getLatitude() == null) {
                        errors.add(String.format("聚集区%s缺少坐标信息", acc.getAccumulationId()));
                    }
                }
            }
        }
        
        // 时间矩阵检查
        if (timeMatrix == null || timeMatrix.isEmpty()) {
            warnings.add("时间矩阵为空，将使用地理距离估算");
        }
        
        boolean isValid = errors.isEmpty();
        
        return resultBuilder
            .isValid(isValid)
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 验证优化结果
     * 
     * @param originalClusters 原始聚类
     * @param optimizedClusters 优化后聚类
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @param constraints 约束配置
     * @return 验证结果
     */
    private VRPValidationResult validateOptimizationResult(
        List<List<Accumulation>> originalClusters,
        List<List<Accumulation>> optimizedClusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        VRPConstraints constraints
    ) {
        VRPValidationResult.VRPValidationResultBuilder resultBuilder = VRPValidationResult.builder()
            .isValid(true)
            .validationType(VRPValidationResult.ValidationType.COMPREHENSIVE_VALIDATION);
        
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 数据完整性检查
        Set<String> originalIds = originalClusters.stream()
            .flatMap(List::stream)
            .map(acc -> String.valueOf(acc.getAccumulationId()))
            .collect(Collectors.toSet());
        
        Set<String> optimizedIds = optimizedClusters.stream()
            .flatMap(List::stream)
            .map(acc -> String.valueOf(acc.getAccumulationId()))
            .collect(Collectors.toSet());
        
        if (!originalIds.equals(optimizedIds)) {
            errors.add("优化前后聚集区数据不一致");
        }
        
        // 约束满足检查
        int violatedRoutes = 0;
        for (List<Accumulation> cluster : optimizedClusters) {
            double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            if (workTime > constraints.getMaxVehicleWorkTimeMinutes()) {
                violatedRoutes++;
            }
        }
        
        if (violatedRoutes > 0) {
            if (violatedRoutes > constraints.getMaxServiceViolations()) {
                errors.add(String.format("约束违反路线数(%d)超过允许上限(%d)", 
                    violatedRoutes, constraints.getMaxServiceViolations()));
            } else {
                warnings.add(String.format("存在%d条路线违反时间约束", violatedRoutes));
            }
        }
        
        boolean isValid = errors.isEmpty();
        
        return resultBuilder
            .isValid(isValid)
            .errors(errors)
            .warnings(warnings)
            .build();
    }
    
    /**
     * 添加优化监听器
     * 
     * @param algorithm 算法实例
     * @param stats 统计对象
     */
    private void addOptimizationListeners(VehicleRoutingAlgorithm algorithm, OptimizationStatistics stats) {
        algorithm.addListener(new IterationStartsListener() {
            @Override
            public void informIterationStarts(int iteration, VehicleRoutingProblem problem, 
                                           Collection<VehicleRoutingProblemSolution> solutions) {
                stats.setIterationsExecuted(iteration);
                
                if (iteration == 1 && !solutions.isEmpty()) {
                    VehicleRoutingProblemSolution initialSolution = solutions.iterator().next();
                    stats.setInitialCost(initialSolution.getCost());
                }
                
                if (iteration % 100 == 0) {
                    log.debug("   📊 JSPRIT迭代进度: {}", iteration);
                }
            }
        });
    }
    
    /**
     * 记录优化结果
     * 
     * @param originalClusters 原始聚类
     * @param optimizedClusters 优化后聚类
     * @param stats 统计信息
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     */
    private void logOptimizationResults(
        List<List<Accumulation>> originalClusters,
        List<List<Accumulation>> optimizedClusters,
        OptimizationStatistics stats,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        // 计算优化指标
        double[] originalWorkTimes = calculateAllWorkTimes(originalClusters, depot, timeMatrix);
        double[] optimizedWorkTimes = calculateAllWorkTimes(optimizedClusters, depot, timeMatrix);
        
        double originalVariance = calculateVariance(originalWorkTimes);
        double optimizedVariance = calculateVariance(optimizedWorkTimes);
        double varianceImprovement = ((originalVariance - optimizedVariance) / originalVariance) * 100.0;
        
        int originalViolations = countTimeViolations(originalClusters, depot, timeMatrix, 450.0);
        int optimizedViolations = countTimeViolations(optimizedClusters, depot, timeMatrix, 450.0);
        
        log.info("✅ JSPRIT VRP优化完成:");
        log.info("   📊 执行统计: {}次迭代, {}ms执行时间", 
            stats.getIterationsExecuted(), stats.getExecutionTimeMillis());
        log.info("   🎯 成本改进: {:.1f} → {:.1f} ({:.1f}%)", 
            stats.getInitialCost(), stats.getFinalCost(), stats.getImprovementPercentage());
        log.info("   ⚖️ 时间方差: {:.1f} → {:.1f} ({:.1f}%改进)", 
            originalVariance, optimizedVariance, varianceImprovement);
        log.info("   🚫 约束违反: {} → {}条路线", originalViolations, optimizedViolations);
        log.info("   🛣️ 路线数量: {} → {}", originalClusters.size(), optimizedClusters.size());
    }
    
    /**
     * 计算所有聚类的工作时间
     * 
     * @param clusters 聚类列表
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 工作时间数组
     */
    private double[] calculateAllWorkTimes(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        return clusters.stream()
            .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .toArray();
    }
    
    /**
     * 计算聚类工作时间
     * 
     * @param cluster 聚类
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 工作时间（分钟）
     */
    private double calculateClusterWorkTime(
        List<Accumulation> cluster,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        // 配送时间
        double totalTime = cluster.stream()
            .mapToDouble(acc -> acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 30.0)
            .sum();
        
        // 往返交通时间
        for (Accumulation acc : cluster) {
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返
            } else {
                // 使用距离估算
                double distance = calculateDistance(
                    depot.getLatitude(), depot.getLongitude(),
                    acc.getLatitude(), acc.getLongitude()
                );
                totalTime += distance * 2.0; // 假设1公里/分钟，往返
            }
        }
        
        return totalTime;
    }
    
    /**
     * 计算距离（Haversine公式）
     * 
     * @param lat1 纬度1
     * @param lon1 经度1
     * @param lat2 纬度2
     * @param lon2 经度2
     * @return 距离（公里）
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 计算方差
     * 
     * @param values 数值数组
     * @return 方差
     */
    private double calculateVariance(double[] values) {
        if (values.length <= 1) return 0.0;
        
        double mean = Arrays.stream(values).average().orElse(0.0);
        double sumSquaredDiffs = Arrays.stream(values)
            .map(value -> Math.pow(value - mean, 2))
            .sum();
        
        return sumSquaredDiffs / values.length;
    }
    
    /**
     * 计算时间约束违反数量
     * 
     * @param clusters 聚类列表
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @param maxTimeLimit 最大时间限制
     * @return 违反数量
     */
    private int countTimeViolations(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        double maxTimeLimit
    ) {
        return (int) clusters.stream()
            .mapToDouble(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .filter(time -> time > maxTimeLimit)
            .count();
    }
    
    /**
     * 深拷贝聚类列表
     * 
     * @param clusters 原始聚类
     * @return 深拷贝结果
     */
    private List<List<Accumulation>> deepCopyOfClusters(List<List<Accumulation>> clusters) {
        return clusters.stream()
            .map(cluster -> cluster.stream()
                .map(Accumulation::copy)
                .collect(Collectors.toList()))
            .collect(Collectors.toList());
    }
}