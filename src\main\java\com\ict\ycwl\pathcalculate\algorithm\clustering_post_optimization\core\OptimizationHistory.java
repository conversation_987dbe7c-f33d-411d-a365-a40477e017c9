package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 优化历史记录
 * 
 * 记录整个优化过程的历史信息，包括每轮优化的结果、
 * 总体统计信息和性能指标
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
public class OptimizationHistory {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 优化轮次结果列表
     */
    private List<OptimizationRoundResult> rounds;
    
    /**
     * 开始时间戳
     */
    private long startTimestamp;
    
    /**
     * 结束时间戳
     */
    private long endTimestamp;
    
    public OptimizationHistory(String sessionId) {
        this.sessionId = sessionId;
        this.rounds = new ArrayList<>();
        this.startTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 添加轮次结果
     */
    public void addRound(OptimizationRoundResult roundResult) {
        rounds.add(roundResult);
    }
    
    /**
     * 完成优化历史记录
     */
    public void complete() {
        this.endTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 获取总执行时间
     */
    public long getTotalExecutionTime() {
        if (endTimestamp == 0) {
            return System.currentTimeMillis() - startTimestamp;
        }
        return endTimestamp - startTimestamp;
    }
    
    /**
     * 获取总轮数
     */
    public int getTotalRounds() {
        return rounds.size();
    }
    
    /**
     * 获取成功轮数
     */
    public int getSuccessfulRounds() {
        return (int) rounds.stream().filter(OptimizationRoundResult::isSuccess).count();
    }
    
    /**
     * 获取总改进率
     */
    public double getTotalImprovementRatio() {
        if (rounds.isEmpty()) {
            return 0.0;
        }
        
        OptimizationRoundResult firstRound = rounds.get(0);
        OptimizationRoundResult lastRound = rounds.get(rounds.size() - 1);
        
        if (firstRound.getBeforeReport() == null || lastRound.getAfterReport() == null) {
            return 0.0;
        }
        
        int initialViolations = firstRound.getBeforeReport().getTotalViolationCount();
        int finalViolations = lastRound.getAfterReport().getTotalViolationCount();
        
        if (initialViolations == 0) {
            return 0.0;
        }
        
        return (double) (initialViolations - finalViolations) / initialViolations;
    }
    
    /**
     * 检查是否有显著改进
     */
    public boolean hasSignificantImprovement() {
        return getTotalImprovementRatio() >= 0.1; // 10%以上改进
    }
}