package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 业内标准降级算法管理器
 * 
 * 当主要算法（MILP、智能调整等）无法工作或效果不佳时，
 * 使用业内标准的启发式算法作为降级方案
 * 
 * 支持的降级算法：
 * 1. 模拟退火算法 (Simulated Annealing) - 主降级方案
 * 2. 遗传算法 (Genetic Algorithm) - 备用方案
 * 3. 变邻域搜索 (Variable Neighborhood Search) - 局部优化
 * 4. 局部搜索 (Local Search) - 快速改进
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class FallbackAlgorithmManager {
    
    @Autowired
    private SimulatedAnnealingOptimizer simulatedAnnealingOptimizer;
    
    @Autowired
    private GeneticAlgorithmOptimizer geneticAlgorithmOptimizer;
    
    @Autowired
    private VariableNeighborhoodSearch variableNeighborhoodSearch;
    
    @Autowired
    private LocalSearchOptimizer localSearchOptimizer;
    
    /**
     * 执行降级优化
     * 
     * @param originalRoutes 原始路线
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @param strategy 降级策略
     * @return 优化结果
     */
    public FallbackOptimizationResult executeFallbackOptimization(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            FallbackStrategy strategy) {
        
        log.info("🔄 启动业内标准降级算法 - 策略: {}", strategy.getName());
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证输入
            if (originalRoutes == null || originalRoutes.isEmpty()) {
                return createErrorResult("输入路线为空", startTime);
            }
            
            // 根据策略选择算法
            FallbackOptimizationResult result = executeByStrategy(
                originalRoutes, depot, timeMatrix, strategy);
            
            // 如果主策略失败，尝试备用策略
            if (!result.isSuccess() && strategy != FallbackStrategy.GENETIC_ALGORITHM) {
                log.warn("⚠️ 主降级策略失败，尝试备用遗传算法");
                result = executeByStrategy(originalRoutes, depot, timeMatrix, 
                    FallbackStrategy.GENETIC_ALGORITHM);
            }
            
            // 最后尝试局部搜索快速修复
            if (!result.isSuccess()) {
                log.warn("⚠️ 所有高级降级算法失败，使用局部搜索快速修复");
                result = executeByStrategy(originalRoutes, depot, timeMatrix, 
                    FallbackStrategy.LOCAL_SEARCH);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            result.setExecutionTimeMs(executionTime);
            
            log.info("✅ 降级算法执行完成 - 耗时: {}ms, 成功: {}", 
                executionTime, result.isSuccess());
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ 降级算法执行异常", e);
            return createErrorResult("降级算法执行异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 根据策略执行相应的算法
     */
    private FallbackOptimizationResult executeByStrategy(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            FallbackStrategy strategy) {
        
        switch (strategy) {
            case SIMULATED_ANNEALING:
                return simulatedAnnealingOptimizer.optimize(originalRoutes, depot, timeMatrix);
                
            case GENETIC_ALGORITHM:
                return geneticAlgorithmOptimizer.optimize(originalRoutes, depot, timeMatrix);
                
            case VARIABLE_NEIGHBORHOOD_SEARCH:
                return variableNeighborhoodSearch.optimize(originalRoutes, depot, timeMatrix);
                
            case LOCAL_SEARCH:
                return localSearchOptimizer.optimize(originalRoutes, depot, timeMatrix);
                
            case HYBRID:
                return executeHybridStrategy(originalRoutes, depot, timeMatrix);
                
            default:
                log.error("❌ 未知的降级策略: {}", strategy);
                return createErrorResult("未知的降级策略", System.currentTimeMillis());
        }
    }
    
    /**
     * 执行混合策略
     * 组合多种算法以获得最佳效果
     */
    private FallbackOptimizationResult executeHybridStrategy(
            List<List<Accumulation>> originalRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🔀 启动混合降级策略");
        
        // 第1阶段：模拟退火进行全局优化
        FallbackOptimizationResult saResult = simulatedAnnealingOptimizer.optimize(
            originalRoutes, depot, timeMatrix);
        
        List<List<Accumulation>> bestRoutes = saResult.isSuccess() ? 
            saResult.getOptimizedRoutes() : originalRoutes;
        
        // 第2阶段：变邻域搜索进行局部改进
        FallbackOptimizationResult vnsResult = variableNeighborhoodSearch.optimize(
            bestRoutes, depot, timeMatrix);
        
        if (vnsResult.isSuccess()) {
            bestRoutes = vnsResult.getOptimizedRoutes();
        }
        
        // 第3阶段：局部搜索进行最终调优
        FallbackOptimizationResult lsResult = localSearchOptimizer.optimize(
            bestRoutes, depot, timeMatrix);
        
        if (lsResult.isSuccess()) {
            bestRoutes = lsResult.getOptimizedRoutes();
        }
        
        // 评估最终结果
        OptimizationMetrics finalMetrics = evaluateOptimization(
            originalRoutes, bestRoutes, depot, timeMatrix);
        
        return FallbackOptimizationResult.builder()
            .success(true)
            .optimizedRoutes(bestRoutes)
            .originalRouteCount(originalRoutes.size())
            .optimizedRouteCount(bestRoutes.size())
            .strategy(FallbackStrategy.HYBRID)
            .optimizationMetrics(finalMetrics)
            .algorithmDetails("混合策略: SA -> VNS -> LS")
            .message("混合降级策略执行成功")
            .build();
    }
    
    /**
     * 选择最佳降级策略
     * 根据问题特征自动选择最合适的降级算法
     */
    public FallbackStrategy selectBestStrategy(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 分析问题特征
        ProblemCharacteristics characteristics = analyzeProblemCharacteristics(
            routes, depot, timeMatrix);
        
        log.debug("🔍 问题特征分析: {}", characteristics.generateSummary());
        
        // 基于特征选择策略
        if (characteristics.getRouteCount() > 50) {
            // 大规模问题：使用遗传算法
            return FallbackStrategy.GENETIC_ALGORITHM;
        } else if (characteristics.getConstraintViolationRate() > 0.3) {
            // 高约束违反率：使用模拟退火
            return FallbackStrategy.SIMULATED_ANNEALING;
        } else if (characteristics.getTimeVariance() > 10000) {
            // 高时间方差：使用变邻域搜索
            return FallbackStrategy.VARIABLE_NEIGHBORHOOD_SEARCH;
        } else {
            // 一般情况：使用混合策略
            return FallbackStrategy.HYBRID;
        }
    }
    
    /**
     * 分析问题特征
     */
    private ProblemCharacteristics analyzeProblemCharacteristics(
            List<List<Accumulation>> routes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        int routeCount = routes.size();
        int totalAccumulations = routes.stream().mapToInt(List::size).sum();
        
        // 计算路线时间统计
        List<Double> routeTimes = routes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .boxed()
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        
        double avgTime = routeTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double timeVariance = routeTimes.stream()
            .mapToDouble(time -> Math.pow(time - avgTime, 2))
            .average().orElse(0);
        
        // 计算约束违反率
        long violatingRoutes = routeTimes.stream()
            .mapToLong(time -> time > 450.0 ? 1 : 0)
            .sum();
        double violationRate = (double) violatingRoutes / routeCount;
        
        return ProblemCharacteristics.builder()
            .routeCount(routeCount)
            .totalAccumulations(totalAccumulations)
            .averageRouteTime(avgTime)
            .timeVariance(timeVariance)
            .constraintViolationRate(violationRate)
            .problemComplexity(calculateProblemComplexity(routeCount, totalAccumulations))
            .build();
    }
    
    /**
     * 计算路线时间
     */
    private double calculateRouteTime(List<Accumulation> route, 
                                    TransitDepot depot, 
                                    Map<String, TimeInfo> timeMatrix) {
        
        double totalTime = 0.0;
        
        // 配送时间
        for (Accumulation acc : route) {
            if (acc.getDeliveryTime() != null) {
                totalTime += acc.getDeliveryTime();
            }
        }
        
        // 往返时间
        for (Accumulation acc : route) {
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返
            }
        }
        
        return totalTime;
    }
    
    /**
     * 评估优化效果
     */
    private OptimizationMetrics evaluateOptimization(
            List<List<Accumulation>> originalRoutes,
            List<List<Accumulation>> optimizedRoutes,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        // 计算原始指标
        double originalTotalTime = originalRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        long originalViolations = originalRoutes.stream()
            .mapToLong(route -> calculateRouteTime(route, depot, timeMatrix) > 450.0 ? 1 : 0)
            .sum();
        
        // 计算优化后指标
        double optimizedTotalTime = optimizedRoutes.stream()
            .mapToDouble(route -> calculateRouteTime(route, depot, timeMatrix))
            .sum();
        
        long optimizedViolations = optimizedRoutes.stream()
            .mapToLong(route -> calculateRouteTime(route, depot, timeMatrix) > 450.0 ? 1 : 0)
            .sum();
        
        // 计算改进指标
        double timeImprovement = (originalTotalTime - optimizedTotalTime) / originalTotalTime * 100;
        double violationReduction = (originalViolations - optimizedViolations);
        
        return OptimizationMetrics.builder()
            .originalTotalTime(originalTotalTime)
            .optimizedTotalTime(optimizedTotalTime)
            .timeImprovement(timeImprovement)
            .originalViolations((int) originalViolations)
            .optimizedViolations((int) optimizedViolations)
            .violationReduction((int) violationReduction)
            .constraintSatisfactionRate(1.0 - (double) optimizedViolations / optimizedRoutes.size())
            .build();
    }
    
    /**
     * 计算问题复杂度
     */
    private double calculateProblemComplexity(int routeCount, int totalAccumulations) {
        // 简化的复杂度计算
        return Math.log(routeCount) * Math.log(totalAccumulations);
    }
    
    /**
     * 创建错误结果
     */
    private FallbackOptimizationResult createErrorResult(String message, long startTime) {
        return FallbackOptimizationResult.builder()
            .success(false)
            .optimizedRoutes(new ArrayList<>())
            .originalRouteCount(0)
            .optimizedRouteCount(0)
            .strategy(FallbackStrategy.LOCAL_SEARCH)
            .message(message)
            .executionTimeMs(System.currentTimeMillis() - startTime)
            .build();
    }
    
    /**
     * 获取算法统计信息
     */
    public FallbackAlgorithmStatistics getStatistics() {
        return FallbackAlgorithmStatistics.builder()
            .totalExecutions(0) // 这里应该实现统计逻辑
            .successfulExecutions(0)
            .averageExecutionTime(0.0)
            .mostUsedStrategy(FallbackStrategy.SIMULATED_ANNEALING)
            .averageImprovement(0.0)
            .build();
    }
}