package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 单轮优化结果
 * 
 * 记录单轮优化的执行结果，包括优化前后的状态对比、
 * 性能指标和成功状态
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class OptimizationRoundResult {
    
    /**
     * 优化轮次
     */
    private int round;
    
    /**
     * 使用的优化策略
     */
    private OptimizationStrategy strategy;
    
    /**
     * 优化后的聚类结果
     */
    private List<List<Accumulation>> optimizedClusters;
    
    /**
     * 优化前的约束违反报告
     */
    private ConstraintViolationReport beforeReport;
    
    /**
     * 优化后的约束违反报告
     */
    private ConstraintViolationReport afterReport;
    
    /**
     * 改进率 (0.0 到 1.0)
     */
    private double improvementRatio;
    
    /**
     * 是否满足所有约束
     */
    private boolean constraintsSatisfied;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTime;
    
    /**
     * 是否执行成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 获取约束改进数量
     */
    public int getConstraintImprovementCount() {
        if (beforeReport == null || afterReport == null) {
            return 0;
        }
        return beforeReport.getTotalViolationCount() - afterReport.getTotalViolationCount();
    }
    
    /**
     * 获取工作时间改进量（分钟）
     */
    public double getWorkTimeImprovement() {
        if (beforeReport == null || afterReport == null) {
            return 0.0;
        }
        return beforeReport.getMaxWorkTime() - afterReport.getMaxWorkTime();
    }
    
    /**
     * 获取时间差距改进量（分钟）
     */
    public double getTimeGapImprovement() {
        if (beforeReport == null || afterReport == null) {
            return 0.0;
        }
        return beforeReport.getMaxTimeGap() - afterReport.getMaxTimeGap();
    }
    
    /**
     * 检查是否为有效改进
     */
    public boolean isValidImprovement() {
        return success && improvementRatio > 0.0;
    }
    
    /**
     * 检查是否为显著改进
     */
    public boolean isSignificantImprovement() {
        return isValidImprovement() && improvementRatio >= 0.1; // 10%以上改进
    }
}