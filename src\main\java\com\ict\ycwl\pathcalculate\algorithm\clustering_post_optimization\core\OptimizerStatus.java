package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 优化器状态信息
 * 
 * 记录聚类二次优化器的运行状态、统计信息和性能指标
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class OptimizerStatus {
    
    /**
     * 优化器运行状态
     */
    public enum Status {
        IDLE,           // 空闲状态
        OPTIMIZING,     // 正在优化
        COMPLETED,      // 优化完成
        ERROR           // 优化出错
    }
    
    /**
     * 当前状态
     */
    private Status currentStatus;
    
    /**
     * 状态更新时间
     */
    private LocalDateTime lastUpdated;
    
    /**
     * 总优化次数
     */
    private long totalOptimizations;
    
    /**
     * 成功优化次数
     */
    private long successfulOptimizations;
    
    /**
     * 平均优化时间(毫秒)
     */
    private double averageOptimizationTime;
    
    /**
     * 最后一次优化时间(毫秒)
     */
    private long lastOptimizationTime;
    
    /**
     * 约束修复统计
     */
    private ConstraintFixStats constraintFixStats;
    
    /**
     * 各策略使用统计
     */
    private Map<String, Long> strategyUsageStats;
    
    /**
     * 错误信息(如果有)
     */
    private String lastError;
    
    /**
     * 约束修复统计信息
     */
    @Data
    @Builder
    public static class ConstraintFixStats {
        
        /**
         * 修复的450分钟约束违反数量
         */
        private long fixed450MinuteViolations;
        
        /**
         * 修复的30分钟差异约束违反数量
         */
        private long fixed30MinuteGapViolations;
        
        /**
         * 平均约束修复率
         */
        private double averageFixRate;
        
        /**
         * 最大工作时间减少量(分钟)
         */
        private double maxWorkTimeReduction;
        
        /**
         * 时间差距减少量(分钟)
         */
        private double timeGapReduction;
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalOptimizations == 0) {
            return 0.0;
        }
        return (double) successfulOptimizations / totalOptimizations;
    }
    
    /**
     * 更新状态
     */
    public void updateStatus(Status newStatus) {
        this.currentStatus = newStatus;
        this.lastUpdated = LocalDateTime.now();
    }
    
    /**
     * 记录优化完成
     */
    public void recordOptimizationCompleted(long executionTime, boolean success) {
        this.totalOptimizations++;
        if (success) {
            this.successfulOptimizations++;
        }
        this.lastOptimizationTime = executionTime;
        
        // 更新平均优化时间
        this.averageOptimizationTime = (this.averageOptimizationTime * (totalOptimizations - 1) + executionTime) / totalOptimizations;
        
        updateStatus(success ? Status.COMPLETED : Status.ERROR);
    }
    
    /**
     * 记录约束修复
     */
    public void recordConstraintFix(int fixed450Violations, int fixed30GapViolations, 
                                   double maxTimeReduction, double gapReduction) {
        if (constraintFixStats == null) {
            constraintFixStats = ConstraintFixStats.builder()
                .fixed450MinuteViolations(0)
                .fixed30MinuteGapViolations(0)
                .averageFixRate(0.0)
                .maxWorkTimeReduction(0.0)
                .timeGapReduction(0.0)
                .build();
        }
        
        constraintFixStats.setFixed450MinuteViolations(
            constraintFixStats.getFixed450MinuteViolations() + fixed450Violations);
        constraintFixStats.setFixed30MinuteGapViolations(
            constraintFixStats.getFixed30MinuteGapViolations() + fixed30GapViolations);
        constraintFixStats.setMaxWorkTimeReduction(
            Math.max(constraintFixStats.getMaxWorkTimeReduction(), maxTimeReduction));
        constraintFixStats.setTimeGapReduction(
            Math.max(constraintFixStats.getTimeGapReduction(), gapReduction));
        
        // 更新平均修复率
        long totalFixed = constraintFixStats.getFixed450MinuteViolations() + 
                         constraintFixStats.getFixed30MinuteGapViolations();
        constraintFixStats.setAverageFixRate((double) totalFixed / totalOptimizations);
    }
    
    /**
     * 创建默认状态
     */
    public static OptimizerStatus createDefault() {
        return OptimizerStatus.builder()
            .currentStatus(Status.IDLE)
            .lastUpdated(LocalDateTime.now())
            .totalOptimizations(0)
            .successfulOptimizations(0)
            .averageOptimizationTime(0.0)
            .lastOptimizationTime(0)
            .build();
    }
}