package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态规划TSP求解器 - 第三方库优先版本
 * 优先使用OR-Tools精确求解，降级时使用Held-Karp算法
 * 精确求解小规模TSP问题（≤12节点）
 * 时间复杂度：O(2^n * n²)，空间复杂度：O(2^n * n)
 */
@Slf4j
@Component
public class DynamicProgrammingTSP {
    
    private final SafeORToolsTSP orToolsSolver;
    
    /**
     * 状态类：表示DP状态
     */
    private static class DPState {
        int mask;           // 访问节点的位掩码
        int lastNode;       // 当前路径的最后一个节点
        double cost;        // 到达此状态的最小成本
        int parent;         // 父状态的lastNode（用于路径重构）
        
        DPState(int mask, int lastNode, double cost, int parent) {
            this.mask = mask;
            this.lastNode = lastNode;
            this.cost = cost;
            this.parent = parent;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (!(obj instanceof DPState)) return false;
            DPState other = (DPState) obj;
            return mask == other.mask && lastNode == other.lastNode;
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(mask, lastNode);
        }
    }
    
    private Map<String, DPState> dpTable;
    private double[][] costMatrix;
    private int nodeCount;
    private List<Accumulation> cluster;
    
    /**
     * 无参构造器
     */
    public DynamicProgrammingTSP() {
        this.orToolsSolver = new SafeORToolsTSP();
        log.info("🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用");
    }
    
    /**
     * 依赖注入构造器
     */
    public DynamicProgrammingTSP(SafeORToolsTSP orToolsSolver) {
        this.orToolsSolver = orToolsSolver;
        log.info("🧮 DynamicProgrammingTSP初始化 - 使用注入的OR-Tools求解器");
    }
    
    /**
     * 动态规划求解TSP - 第三方库优先版本
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix) {
        
        log.info("🧮 [第三方库优先] 开始动态规划TSP求解 - 节点数: {}", cluster.size());
        long startTime = System.currentTimeMillis();
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        // 第三方库优先策略：OR-Tools可用时优先使用
        if (orToolsSolver.isORToolsAvailable() && cluster.size() <= 20) {
            try {
                log.info("🚀 [第三方库调用] 优先使用OR-Tools精确求解器 - Google OR-Tools 9.8.3296");
                List<Long> orToolsResult = orToolsSolver.solve(depot, cluster, timeMatrix, 60000L); // 60秒时间限制
                
                long duration = System.currentTimeMillis() - startTime;
                log.info("✅ [第三方库成功] OR-Tools精确求解完成 - 耗时: {}ms, 解质量: {} 节点", 
                        duration, orToolsResult.size());
                
                return orToolsResult;
            } catch (Exception e) {
                log.warn("❌ [第三方库失败] OR-Tools求解失败，降级到Held-Karp动态规划 - 错误: {}", e.getMessage());
            }
        } else if (!orToolsSolver.isORToolsAvailable()) {
            log.info("⚠️ [算法降级] OR-Tools不可用，使用Held-Karp动态规划算法");
        } else {
            log.info("⚠️ [算法降级] 节点数{}超过OR-Tools小规模推荐上限，使用Held-Karp动态规划算法", cluster.size());
        }
        
        // 检查规模限制
        if (cluster.size() > 12) {
            log.warn("⚠️ [算法限制] 节点数{}超过动态规划建议上限(12)，最终降级到贪心算法", cluster.size());
            List<Long> greedyResult = solveWithGreedyFallback(depot, cluster, timeMatrix);
            log.info("🔄 [最终降级] 贪心算法完成 - 解质量: {} 节点", greedyResult.size());
            return greedyResult;
        }
        
        // 降级到自实现Held-Karp算法
        log.info("🔧 [自实现算法] 开始Held-Karp动态规划求解");
        
        this.cluster = cluster;
        this.nodeCount = cluster.size();
        this.costMatrix = buildCostMatrix(depot, cluster, timeMatrix);
        this.dpTable = new HashMap<>();
        
        long heldKarpStartTime = System.currentTimeMillis();
        
        try {
            // 执行Held-Karp算法
            List<Integer> optimalPath = solveHeldKarp();
            
            long heldKarpDuration = System.currentTimeMillis() - heldKarpStartTime;
            double memoryUsedMB = estimateMemoryUsage(cluster.size());
            double timeComplexity = estimateTimeComplexity(cluster.size());
            
            log.info("✅ [自实现算法成功] Held-Karp动态规划完成 - 耗时: {}ms, 路径长度: {}", 
                    heldKarpDuration, optimalPath.size());
            log.info("📊 [算法统计] 状态空间: 2^{} * {} = {:.0f}, 内存使用: {:.1f}MB, 时间复杂度: {:.0f}", 
                    cluster.size(), cluster.size(), Math.pow(2, cluster.size()) * cluster.size(), 
                    memoryUsedMB, timeComplexity);
            
            // 转换为聚集区ID序列
            List<Long> result = optimalPath.stream()
                    .map(i -> cluster.get(i).getAccumulationId())
                    .collect(Collectors.toList());
            
            log.info("🎯 [算法结果] 动态规划找到最优解 - 解质量: {} 节点, 路径: {}", 
                    result.size(), result.size() <= 10 ? result : "[" + result.size() + " 节点]");
            
            return result;
                    
        } catch (OutOfMemoryError e) {
            log.error("❌ [内存不足] 动态规划内存不足，最终降级到贪心算法 - 需要内存: {:.1f}MB", 
                     estimateMemoryUsage(cluster.size()));
            List<Long> greedyResult = solveWithGreedyFallback(depot, cluster, timeMatrix);
            log.info("🔄 [最终降级] 贪心算法完成 - 解质量: {} 节点", greedyResult.size());
            return greedyResult;
        } catch (Exception e) {
            log.error("❌ [算法失败] 动态规划求解失败，最终降级到贪心算法 - 错误: {}", e.getMessage());
            List<Long> greedyResult = solveWithGreedyFallback(depot, cluster, timeMatrix);
            log.info("🔄 [最终降级] 贪心算法完成 - 解质量: {} 节点", greedyResult.size());
            return greedyResult;
        }
    }
    
    /**
     * Held-Karp算法核心实现
     */
    private List<Integer> solveHeldKarp() {
        // 第一阶段：计算所有子集的最优成本
        
        // 初始化：从节点0到其他各节点的距离
        for (int i = 1; i < nodeCount; i++) {
            int mask = (1 << i);  // 只包含节点i的集合
            String key = getStateKey(mask, i);
            dpTable.put(key, new DPState(mask, i, costMatrix[0][i], 0));
        }
        
        // 动态规划填表：按子集大小递增
        for (int subsetSize = 2; subsetSize < nodeCount; subsetSize++) {
            // 生成所有大小为subsetSize的子集
            generateSubsets(subsetSize);
        }
        
        // 第二阶段：找到最优解并重构路径
        return reconstructOptimalPath();
    }
    
    /**
     * 生成指定大小的所有子集并计算最优成本
     */
    private void generateSubsets(int subsetSize) {
        // 使用位操作生成所有包含节点0且大小为subsetSize+1的子集
        int totalNodes = nodeCount;
        int maxMask = (1 << totalNodes) - 1;
        
        for (int mask = 1; mask <= maxMask; mask++) {
            // 检查子集是否包含节点0（起点）
            if ((mask & 1) == 0) continue;
            
            // 检查子集大小是否正确
            if (Integer.bitCount(mask) != subsetSize + 1) continue;
            
            // 对每个可能的终点计算最优成本
            for (int lastNode = 1; lastNode < totalNodes; lastNode++) {
                if ((mask & (1 << lastNode)) == 0) continue; // lastNode不在子集中
                
                // 计算到达(mask, lastNode)状态的最小成本
                double minCost = Double.MAX_VALUE;
                int bestPrevNode = -1;
                
                int prevMask = mask ^ (1 << lastNode); // 移除lastNode的子集
                
                for (int prevNode = 0; prevNode < totalNodes; prevNode++) {
                    if ((prevMask & (1 << prevNode)) == 0) continue; // prevNode不在前驱子集中
                    
                    String prevKey = getStateKey(prevMask, prevNode);
                    DPState prevState = dpTable.get(prevKey);
                    
                    if (prevState != null) {
                        double newCost = prevState.cost + costMatrix[prevNode][lastNode];
                        if (newCost < minCost) {
                            minCost = newCost;
                            bestPrevNode = prevNode;
                        }
                    }
                }
                
                // 保存最优状态
                if (bestPrevNode != -1) {
                    String key = getStateKey(mask, lastNode);
                    dpTable.put(key, new DPState(mask, lastNode, minCost, bestPrevNode));
                }
            }
        }
    }
    
    /**
     * 重构最优路径
     */
    private List<Integer> reconstructOptimalPath() {
        // 找到最优的最终状态
        int finalMask = (1 << nodeCount) - 1; // 访问所有节点
        double minFinalCost = Double.MAX_VALUE;
        int bestFinalNode = -1;
        
        for (int lastNode = 1; lastNode < nodeCount; lastNode++) {
            String key = getStateKey(finalMask, lastNode);
            DPState state = dpTable.get(key);
            
            if (state != null) {
                double totalCost = state.cost + costMatrix[lastNode][0]; // 回到起点
                if (totalCost < minFinalCost) {
                    minFinalCost = totalCost;
                    bestFinalNode = lastNode;
                }
            }
        }
        
        if (bestFinalNode == -1) {
            log.error("动态规划无法找到可行解");
            return new ArrayList<>();
        }
        
        log.debug("最优TSP成本: {}", minFinalCost);
        
        // 反向重构路径
        List<Integer> path = new ArrayList<>();
        int currentMask = finalMask;
        int currentNode = bestFinalNode;
        
        while (currentMask != 0) {
            path.add(currentNode);
            
            String key = getStateKey(currentMask, currentNode);
            DPState state = dpTable.get(key);
            
            if (state == null || state.parent == -1) break;
            
            // 移动到父状态
            currentMask ^= (1 << currentNode); // 移除当前节点
            currentNode = state.parent;
        }
        
        // 添加起点
        if (currentNode == 0) {
            path.add(0);
        }
        
        // 翻转路径（因为是反向构建的）
        Collections.reverse(path);
        
        // 移除起点（因为TSP结果不包含起点）
        if (!path.isEmpty() && path.get(0) == 0) {
            path.remove(0);
        }
        
        log.debug("重构路径: {}", path);
        return path;
    }
    
    /**
     * 构建成本矩阵
     */
    private double[][] buildCostMatrix(TransitDepot depot, List<Accumulation> cluster, 
                                     Map<String, TimeInfo> timeMatrix) {
        
        int n = cluster.size();
        double[][] matrix = new double[n][n];
        
        // 聚集区之间的成本
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    double travelTime = getTravelTime(
                        cluster.get(i).getCoordinate(), 
                        cluster.get(j).getCoordinate(), 
                        timeMatrix
                    );
                    matrix[i][j] = travelTime + cluster.get(j).getDeliveryTime();
                } else {
                    matrix[i][j] = Double.MAX_VALUE; // 禁止自环
                }
            }
        }
        
        return matrix;
    }
    
    /**
     * 生成状态键
     */
    private String getStateKey(int mask, int lastNode) {
        return mask + "," + lastNode;
    }
    
    /**
     * 获取两点间行驶时间
     */
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 0.0;
    }
    
    /**
     * 降级到贪心算法
     */
    private List<Long> solveWithGreedyFallback(TransitDepot depot, List<Accumulation> cluster, 
                                             Map<String, TimeInfo> timeMatrix) {
        
        log.debug("使用贪心算法作为降级策略");
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minCost = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double totalCost = travelTime + acc.getDeliveryTime();
                
                if (totalCost < minCost) {
                    minCost = totalCost;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 检查是否适合动态规划求解
     */
    public static boolean isSuitableForDP(int nodeCount) {
        return nodeCount <= 12;
    }
    
    /**
     * 估算动态规划的内存使用量（MB）
     */
    public static double estimateMemoryUsage(int nodeCount) {
        if (nodeCount > 12) return Double.MAX_VALUE;
        
        // 状态数量：2^n * n
        long stateCount = (1L << nodeCount) * nodeCount;
        
        // 每个状态约100字节（包括HashMap开销）
        double memoryMB = stateCount * 100.0 / (1024 * 1024);
        
        return memoryMB;
    }
    
    /**
     * 估算动态规划的时间复杂度（相对单位）
     */
    public static double estimateTimeComplexity(int nodeCount) {
        if (nodeCount > 12) return Double.MAX_VALUE;
        
        // 时间复杂度：O(2^n * n²)
        return Math.pow(2, nodeCount) * nodeCount * nodeCount;
    }
}