package com.ict.ycwl.pathcalculate.algorithm.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 坐标点数据结构
 * 用于表示地理坐标信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor  
@Builder
public class CoordinatePoint {
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 检查坐标是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return longitude != null && latitude != null &&
               longitude >= -180 && longitude <= 180 &&
               latitude >= -90 && latitude <= 90;
    }
    
    /**
     * 计算到另一个坐标点的距离（米）
     * 使用Haversine公式计算球面距离
     * 
     * @param other 目标坐标点
     * @return 距离（米），如果坐标无效则返回-1
     */
    public double distanceTo(CoordinatePoint other) {
        if (other == null || !this.isValid() || !other.isValid()) {
            return -1;
        }
        
        final double R = 6371; // 地球半径（千米）
        double dLat = Math.toRadians(other.latitude - this.latitude);
        double dLon = Math.toRadians(other.longitude - this.longitude);
        
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(this.latitude)) * Math.cos(Math.toRadians(other.latitude)) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        
        return R * c * 1000; // 返回距离（米）
    }
    
    /**
     * 获取坐标字符串表示
     * 格式："经度,纬度"
     * 
     * @return 坐标字符串
     */
    public String getCoordinateString() {
        if (!isValid()) {
            return "";
        }
        return String.format("%.6f,%.6f", longitude, latitude);
    }
    
    /**
     * 从坐标字符串解析坐标点
     * 
     * @param coordinateString 坐标字符串，格式："经度,纬度"
     * @return 坐标点对象，如果解析失败返回null
     */
    public static CoordinatePoint fromString(String coordinateString) {
        if (coordinateString == null || !coordinateString.contains(",")) {
            return null;
        }
        
        try {
            String[] parts = coordinateString.split(",");
            if (parts.length != 2) {
                return null;
            }
            
            double lng = Double.parseDouble(parts[0].trim());
            double lat = Double.parseDouble(parts[1].trim());
            
            CoordinatePoint point = new CoordinatePoint(lng, lat);
            return point.isValid() ? point : null;
            
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 判断两个坐标点是否相等（允许小的误差）
     * 
     * @param other 另一个坐标点
     * @return 是否相等
     */
    public boolean equals(CoordinatePoint other) {
        if (other == null || !this.isValid() || !other.isValid()) {
            return false;
        }
        
        final double EPSILON = 1e-6; // 允许的误差范围
        return Math.abs(this.longitude - other.longitude) < EPSILON &&
               Math.abs(this.latitude - other.latitude) < EPSILON;
    }
    
    /**
     * 复制坐标点对象
     * 
     * @return 复制的坐标点对象
     */
    public CoordinatePoint copy() {
        return CoordinatePoint.builder()
                .longitude(this.longitude)
                .latitude(this.latitude)
                .build();
    }
} 