package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * @TableName route_detail
 */
@TableName(value = "route_detail")
@Data
@Builder
public class RouteDetail implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 打卡点/聚集区时长
     */
    private Integer accumulationCount;

    /**
     * 城区商铺个数
     */
    private Integer cityCount;

    /**
     * 乡镇商铺个数
     */
    private Integer countryCount;

    /**
     * 装车时长
     */
    private String loadingTime;

    /**
     * 途中时长
     */
    private String transitTime;

    /**
     * 卸货配送时长
     */
    private String deliveryTime;

    /**
     * 总时长
     */
    private String totalTime;

    /**
     * 路线Id
     */
    private Long routeId;

    //高速公路里程km
    private double freeewatDist;
    //城区公路行驶里程km
    private double urabanRoadsDist;
    //乡镇公路行驶里程km
    private double townshipRoadsDist;
    //二次中转站时长分钟
    private double secondTransitTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}