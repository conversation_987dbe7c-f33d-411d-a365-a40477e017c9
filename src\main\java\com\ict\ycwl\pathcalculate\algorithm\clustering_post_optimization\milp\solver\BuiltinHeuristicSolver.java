package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 内置启发式求解器
 * 
 * 实现简单的启发式算法作为降级方案
 * 支持整数规划，但不保证最优解
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
public class BuiltinHeuristicSolver implements MILPSolver {
    
    private SolverParameters parameters;
    private static final String SOLVER_NAME = "Builtin Heuristic";
    private static final String SOLVER_VERSION = "1.0.0";
    
    public BuiltinHeuristicSolver() {
        this.parameters = SolverParameters.createDefault();
    }
    
    @Override
    public MILPSolution solve(MILPProblem problem) {
        log.info("🔧 开始启发式求解MILP问题: {} (内置算法)", problem.getProblemId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证问题完整性
            ValidationResult validation = problem.validate();
            if (!validation.isValid()) {
                return createErrorSolution("问题定义不完整: " + validation.getErrors(), startTime);
            }
            
            // 使用贪心算法求解
            Map<String, Double> solution = solveWithGreedyAlgorithm(problem);
            
            long solvingTime = System.currentTimeMillis() - startTime;
            
            // 验证解的可行性
            boolean isFeasible = validateSolution(problem, solution);
            
            // 计算目标函数值
            Double objectiveValue = null;
            if (problem.getObjectiveFunction() != null) {
                objectiveValue = problem.getObjectiveFunction().evaluate(solution);
            }
            
            // 构建解结果
            return MILPSolution.builder()
                .solutionStatus(isFeasible ? MILPProblem.SolutionStatus.FEASIBLE : MILPProblem.SolutionStatus.INFEASIBLE)
                .objectiveValue(objectiveValue)
                .variableValues(solution)
                .solvingTimeMs(solvingTime)
                .solverName(SOLVER_NAME)
                .statusMessage(isFeasible ? "启发式求解成功" : "未找到可行解")
                .build();
                
        } catch (Exception e) {
            log.error("❌ 启发式求解失败", e);
            return createErrorSolution("求解过程异常: " + e.getMessage(), startTime);
        }
    }
    
    /**
     * 贪心算法求解
     */
    private Map<String, Double> solveWithGreedyAlgorithm(MILPProblem problem) {
        Map<String, Double> solution = new HashMap<>();
        Random random = new Random(parameters.getRandomSeed() != null ? parameters.getRandomSeed() : System.currentTimeMillis());
        
        log.debug("   启动贪心算法求解");
        
        // 第1步：初始化所有变量为下界
        for (MILPVariable variable : problem.getVariables().values()) {
            solution.put(variable.getName(), variable.getLowerBound());
        }
        
        // 第2步：特殊处理二进制变量（基于启发式规则）
        List<MILPVariable> binaryVariables = problem.getVariables().values().stream()
            .filter(MILPVariable::isBinary)
            .collect(ArrayList::new, (list, var) -> list.add(var), ArrayList::addAll);
        
        if (!binaryVariables.isEmpty()) {
            log.debug("   处理{}个二进制变量", binaryVariables.size());
            solveBinaryVariablesWithHeuristic(problem, solution, binaryVariables, random);
        }
        
        // 第3步：处理整数变量
        List<MILPVariable> integerVariables = problem.getVariables().values().stream()
            .filter(var -> var.getType() == MILPProblem.VariableType.INTEGER)
            .collect(ArrayList::new, (list, var) -> list.add(var), ArrayList::addAll);
        
        if (!integerVariables.isEmpty()) {
            log.debug("   处理{}个整数变量", integerVariables.size());
            solveIntegerVariablesWithHeuristic(problem, solution, integerVariables);
        }
        
        // 第4步：处理连续变量（使用简化的线性规划方法）
        List<MILPVariable> continuousVariables = problem.getVariables().values().stream()
            .filter(MILPVariable::isContinuous)
            .collect(ArrayList::new, (list, var) -> list.add(var), ArrayList::addAll);
        
        if (!continuousVariables.isEmpty()) {
            log.debug("   处理{}个连续变量", continuousVariables.size());
            solveContinuousVariablesWithHeuristic(problem, solution, continuousVariables);
        }
        
        return solution;
    }
    
    /**
     * 启发式求解二进制变量
     */
    private void solveBinaryVariablesWithHeuristic(MILPProblem problem, 
                                                  Map<String, Double> solution,
                                                  List<MILPVariable> binaryVariables,
                                                  Random random) {
        
        // 基于目标函数系数排序（贪心策略）
        ObjectiveFunction objFunc = problem.getObjectiveFunction();
        boolean isMinimize = objFunc.getType() == MILPProblem.ObjectiveType.MINIMIZE;
        
        binaryVariables.sort((var1, var2) -> {
            double coeff1 = objFunc.getCoefficients().getOrDefault(var1.getName(), 0.0);
            double coeff2 = objFunc.getCoefficients().getOrDefault(var2.getName(), 0.0);
            
            // 最小化问题：系数小的优先设为1；最大化问题：系数大的优先设为1
            return isMinimize ? Double.compare(coeff1, coeff2) : Double.compare(coeff2, coeff1);
        });
        
        // 贪心分配二进制变量
        for (MILPVariable var : binaryVariables) {
            // 尝试设置为1，检查约束是否满足
            solution.put(var.getName(), 1.0);
            
            if (checkConstraintsFeasible(problem, solution)) {
                // 可行，保持为1
                log.trace("     二进制变量 {} 设为 1", var.getName());
            } else {
                // 不可行，设为0
                solution.put(var.getName(), 0.0);
                log.trace("     二进制变量 {} 设为 0", var.getName());
            }
        }
    }
    
    /**
     * 启发式求解整数变量
     */
    private void solveIntegerVariablesWithHeuristic(MILPProblem problem, 
                                                   Map<String, Double> solution,
                                                   List<MILPVariable> integerVariables) {
        
        // 简单策略：设为中间值的整数
        for (MILPVariable var : integerVariables) {
            double midValue = (var.getLowerBound() + var.getUpperBound()) / 2.0;
            double intValue = Math.round(midValue);
            
            // 确保在边界内
            intValue = Math.max(var.getLowerBound(), Math.min(var.getUpperBound(), intValue));
            
            solution.put(var.getName(), intValue);
            log.trace("     整数变量 {} 设为 {}", var.getName(), intValue);
        }
    }
    
    /**
     * 启发式求解连续变量
     */
    private void solveContinuousVariablesWithHeuristic(MILPProblem problem, 
                                                      Map<String, Double> solution,
                                                      List<MILPVariable> continuousVariables) {
        
        // 简单策略：根据目标函数设为边界值
        ObjectiveFunction objFunc = problem.getObjectiveFunction();
        boolean isMinimize = objFunc.getType() == MILPProblem.ObjectiveType.MINIMIZE;
        
        for (MILPVariable var : continuousVariables) {
            double coeff = objFunc.getCoefficients().getOrDefault(var.getName(), 0.0);
            
            double value;
            if (coeff > 0) {
                // 系数为正：最小化时取下界，最大化时取上界
                value = isMinimize ? var.getLowerBound() : var.getUpperBound();
            } else if (coeff < 0) {
                // 系数为负：最小化时取上界，最大化时取下界
                value = isMinimize ? var.getUpperBound() : var.getLowerBound();
            } else {
                // 系数为0：取中间值
                value = (var.getLowerBound() + var.getUpperBound()) / 2.0;
            }
            
            solution.put(var.getName(), value);
            log.trace("     连续变量 {} 设为 {}", var.getName(), value);
        }
    }
    
    /**
     * 检查约束可行性
     */
    private boolean checkConstraintsFeasible(MILPProblem problem, Map<String, Double> solution) {
        for (com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.LinearConstraint constraint : 
             problem.getConstraints().values()) {
            if (!constraint.isSatisfied(solution)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 验证解的完整性和可行性
     */
    private boolean validateSolution(MILPProblem problem, Map<String, Double> solution) {
        // 检查所有变量都有值
        for (String varName : problem.getVariables().keySet()) {
            if (!solution.containsKey(varName)) {
                log.warn("变量 {} 没有解值", varName);
                return false;
            }
        }
        
        // 检查所有约束都满足
        return checkConstraintsFeasible(problem, solution);
    }
    
    /**
     * 创建错误解
     */
    private MILPSolution createErrorSolution(String errorMessage, long startTime) {
        return MILPSolution.builder()
            .solutionStatus(MILPProblem.SolutionStatus.ERROR)
            .solvingTimeMs(System.currentTimeMillis() - startTime)
            .solverName(SOLVER_NAME)
            .statusMessage(errorMessage)
            .build();
    }
    
    @Override
    public void setParameters(SolverParameters parameters) {
        this.parameters = parameters;
    }
    
    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }
    
    @Override
    public String getSolverVersion() {
        return SOLVER_VERSION;
    }
    
    @Override
    public boolean isAvailable() {
        return true; // 内置算法总是可用
    }
    
    @Override
    public boolean supports(MILPProblem problem) {
        return true; // 支持所有类型的问题（但不保证最优解）
    }
    
    @Override
    public SolverCapabilities getCapabilities() {
        Set<MILPProblem.VariableType> variableTypes = new HashSet<>();
        variableTypes.add(MILPProblem.VariableType.CONTINUOUS);
        variableTypes.add(MILPProblem.VariableType.INTEGER);
        variableTypes.add(MILPProblem.VariableType.BINARY);
        
        Set<String> constraintTypes = new HashSet<>();
        constraintTypes.add("LESS_EQUAL");
        constraintTypes.add("GREATER_EQUAL");
        constraintTypes.add("EQUAL");
        constraintTypes.add("RANGE");
        
        Set<MILPProblem.ObjectiveType> objectiveTypes = new HashSet<>();
        objectiveTypes.add(MILPProblem.ObjectiveType.MINIMIZE);
        objectiveTypes.add(MILPProblem.ObjectiveType.MAXIMIZE);
        
        return SolverCapabilities.builder()
            .supportedVariableTypes(variableTypes)
            .supportedConstraintTypes(constraintTypes)
            .supportedObjectiveTypes(objectiveTypes)
            .maxVariables(1000)  // 启发式算法适合中小规模问题
            .maxConstraints(1000)
            .supportsIntegerProgramming(true)  // 支持整数规划
            .supportsQuadraticProgramming(false)
            .supportsMultiObjective(false)
            .supportsParallel(false)
            .supportsSensitivityAnalysis(false)
            .supportsDualSolution(false)
            .tier(SolverCapabilities.SolverTier.BUILTIN)
            .licenseType(SolverCapabilities.LicenseType.FREE)
            .description("内置启发式求解器（不保证最优解）")
            .build();
    }
}