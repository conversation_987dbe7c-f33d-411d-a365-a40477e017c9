package com.ict.ycwl.pathcalculate.AOP;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Order(-1) // 确保在 @Transactional 之前执行
public class DynamicDataSourceAspect {
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer paramNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(dynamicDS)")
    public Object around(ProceedingJoinPoint point, DynamicDS dynamicDS) throws Throwable {
        String dsKey = resolveDataSourceKey(point, dynamicDS.value());
        
        try {
            // 切换数据源
            DynamicDataSourceContextHolder.push(dsKey);
            return point.proceed();
        } finally {
            // 清理数据源
            DynamicDataSourceContextHolder.clear();
        }
    }

    private String resolveDataSourceKey(ProceedingJoinPoint joinPoint, String spelExpression) {
        // 获取方法参数
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        
        // 创建评估上下文
        EvaluationContext context = new StandardEvaluationContext();
        
        // 设置方法参数名和值
        String[] paramNames = paramNameDiscoverer.getParameterNames(signature.getMethod());
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        
        // 添加HTTP请求支持（可选）
        addRequestContext(context);
        
        // 解析SpEL表达式
        return parser.parseExpression(spelExpression)
                    .getValue(context, String.class);
    }

    // 添加HTTP请求参数（如需从HttpServletRequest获取参数）
    private void addRequestContext(EvaluationContext context) {
        try {
            ServletRequestAttributes attrs = (ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes();
            if (attrs != null) {
                HttpServletRequest request = attrs.getRequest();
                context.setVariable("request", request);
                
                // 示例：直接获取请求参数
                request.getParameterMap().forEach((key, values) -> {
                    if (values.length > 0) {
                        context.setVariable(key, values[0]);
                    }
                });
            }
        } catch (IllegalStateException ignored) {
            // 非Web环境忽略
        }
    }
}