package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 统一聚类算法适配器
 * 
 * 设计目标：
 * 1. 无缝切换：H3算法 ↔ K-means算法
 * 2. 接口兼容：与现有PathPlanningUtils完全兼容
 * 3. 配置驱动：通过配置文件控制算法选择
 * 4. 智能降级：H3失败时自动降级到K-means
 * 5. 性能监控：记录两种算法的性能对比数据
 * 
 * 使用方式：
 * 直接替换PathPlanningUtils中的WorkloadBalancedKMeans实例即可
 * 无需修改任何调用代码，保持100%向后兼容
 * 
 * 配置说明：
 * - clustering.algorithm.type=H3：使用H3算法（推荐）
 * - clustering.algorithm.type=KMEANS：使用K-means算法（兼容模式）
 * - clustering.algorithm.type=AUTO：自动选择（H3优先，失败时降级）
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Slf4j
public class UnifiedClusteringAdapter {
    
    // ===================== 算法选择枚举 =====================
    
    /**
     * 聚类算法类型
     */
    public enum ClusteringAlgorithmType {
        H3("H3六边形网格聚类"),
        KMEANS("K-means工作量均衡聚类"),
        AUTO("自动选择算法");
        
        private final String description;
        
        ClusteringAlgorithmType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ===================== 配置参数 =====================
    
    /**
     * 聚类算法类型配置
     * 默认使用AUTO模式：H3优先，失败时降级到K-means
     */
    private String algorithmTypeConfig = "H3";
    
    /**
     * H3算法失败时是否自动降级到K-means
     */
    private boolean autoFallback = true;
    
    /**
     * 是否启用性能对比模式（同时运行两种算法进行对比）
     */
    private boolean performanceComparison = false;
    
    // ===================== 算法实例 =====================
    
    private final H3GeographicClustering h3Algorithm;
    private final WorkloadBalancedKMeans kmeansAlgorithm;
    private final ClusteringQualityEvaluator qualityEvaluator;
    
    /**
     * 构造函数：初始化两种算法实例
     */
    public UnifiedClusteringAdapter() {
        this.qualityEvaluator = new ClusteringQualityEvaluator();
        
        // 初始化时间评估器组件
        TimeEvaluationConfig config = new TimeEvaluationConfig();
        RouteTimeCalculator calculator = new RouteTimeCalculator(config);
        TimeBasedTerminationEvaluator timeEvaluator = new TimeBasedTerminationEvaluator(config, calculator);
        
        log.info("🔍 UnifiedClusteringAdapter无参构造函数：时间评估器状态 = {}", timeEvaluator.isReady());
        log.info("🔍 UnifiedClusteringAdapter无参构造函数：配置有效性 = {}", config.validateConfig());
        
        // 使用时间评估器初始化H3算法
        this.h3Algorithm = new H3GeographicClustering(timeEvaluator);
        this.kmeansAlgorithm = new WorkloadBalancedKMeans(qualityEvaluator);
        
        log.info("🔧 UnifiedClusteringAdapter初始化完成");
        log.info("   - H3六边形网格聚类: ✅ 已加载");
        log.info("   - K-means工作量均衡聚类: ✅ 已加载");
        log.info("   - 配置算法类型: {}", algorithmTypeConfig);
        log.info("   - 自动降级: {}", autoFallback ? "启用" : "禁用");
        log.info("   - 性能对比模式: {}", performanceComparison ? "启用" : "禁用");
    }
    
    /**
     * 构造函数：使用外部传入的质量评估器（用于PathPlanningUtils）
     */
    public UnifiedClusteringAdapter(ClusteringQualityEvaluator qualityEvaluator) {
        this.qualityEvaluator = qualityEvaluator;
        
        // 初始化时间评估器组件
        TimeEvaluationConfig config = new TimeEvaluationConfig();
        RouteTimeCalculator calculator = new RouteTimeCalculator(config);
        TimeBasedTerminationEvaluator timeEvaluator = new TimeBasedTerminationEvaluator(config, calculator);
        
        log.info("🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = {}", timeEvaluator.isReady());
        log.info("🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = {}", config.validateConfig());
        
        // 使用时间评估器初始化H3算法
        this.h3Algorithm = new H3GeographicClustering(timeEvaluator);
        this.kmeansAlgorithm = new WorkloadBalancedKMeans(qualityEvaluator);
        
        log.info("🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）");
        log.info("   - H3六边形网格聚类: ✅ 已加载");
        log.info("   - K-means工作量均衡聚类: ✅ 已加载");
        log.info("   - 配置算法类型: {}", algorithmTypeConfig);
        log.info("   - 自动降级: {}", autoFallback ? "启用" : "禁用");
        log.info("   - 性能对比模式: {}", performanceComparison ? "启用" : "禁用");
    }
    
    // ===================== 主要接口方法 =====================
    
    /**
     * 统一聚类接口
     * 完全兼容原WorkloadBalancedKMeans.clusterByWorkload()方法
     * 
     * @param accumulations 聚集区列表
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 聚类结果：路线列表
     */
    public List<List<Accumulation>> clusterByWorkload(
            List<Accumulation> accumulations,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        long startTime = System.currentTimeMillis();
        ClusteringAlgorithmType algorithmType = parseAlgorithmType();
        
        log.info("🚀 开始统一聚类处理：{} 个聚集区，中转站: {}，算法: {}", 
            accumulations.size(), depot.getTransitDepotName(), algorithmType.getDescription());
        
        // 输入验证
        if (accumulations == null || accumulations.isEmpty()) {
            log.warn("⚠️ 聚集区列表为空，返回空结果");
            return new ArrayList<List<Accumulation>>();
        }
        
        List<List<Accumulation>> result = null;
        
        try {
            // 根据配置选择算法执行策略
            switch (algorithmType) {
                case H3:
                    result = executeH3Algorithm(accumulations, depot, timeMatrix);
                    break;
                    
                case KMEANS:
                    result = executeKMeansAlgorithm(accumulations, depot, timeMatrix);
                    break;
                    
                case AUTO:
                    result = executeAutoAlgorithm(accumulations, depot, timeMatrix);
                    break;
                    
                default:
                    log.warn("⚠️ 未知算法类型: {}，使用AUTO模式", algorithmType);
                    result = executeAutoAlgorithm(accumulations, depot, timeMatrix);
                    break;
            }
            
            // 性能对比模式
            if (performanceComparison && result != null) {
                performAlgorithmComparison(accumulations, depot, timeMatrix, result);
            }
            
            long endTime = System.currentTimeMillis();
            log.info("✅ 统一聚类完成：{}条路线，耗时{}ms", 
                result != null ? result.size() : 0, endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ 统一聚类执行失败", e);
            
            // 最后的降级保障：使用K-means算法
            if (autoFallback && algorithmType != ClusteringAlgorithmType.KMEANS) {
                log.warn("🔄 启用最终降级保障：K-means算法");
                return executeKMeansAlgorithm(accumulations, depot, timeMatrix);
            } else {
                throw new RuntimeException("聚类算法执行失败", e);
            }
        }
    }
    
    // ===================== 算法执行策略 =====================
    
    /**
     * 执行H3算法
     */
    private List<List<Accumulation>> executeH3Algorithm(
            List<Accumulation> accumulations,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🗺️ 执行H3六边形网格聚类算法");
        
        try {
            List<List<Accumulation>> result = h3Algorithm.clusterByH3Grid(accumulations, depot, timeMatrix);
            
            // H3算法质量验证
            if (validateClusteringResult(result, accumulations)) {
                log.info("✅ H3算法执行成功，质量验证通过");
                return result;
            } else {
                log.warn("⚠️ H3算法质量验证失败");
                
                if (autoFallback) {
                    log.info("🔄 H3质量不达标，自动降级到K-means");
                    return executeKMeansAlgorithm(accumulations, depot, timeMatrix);
                } else {
                    return result; // 返回H3结果，即使质量不理想
                }
            }
            
        } catch (Exception e) {
            log.error("❌ H3算法执行异常", e);
            
            if (autoFallback) {
                log.info("🔄 H3算法异常，自动降级到K-means");
                return executeKMeansAlgorithm(accumulations, depot, timeMatrix);
            } else {
                throw new RuntimeException("H3算法执行失败", e);
            }
        }
    }
    
    /**
     * 执行K-means算法
     */
    private List<List<Accumulation>> executeKMeansAlgorithm(
            List<Accumulation> accumulations,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("⚖️ 执行K-means工作量均衡聚类算法");
        
        try {
            List<List<Accumulation>> result = kmeansAlgorithm.clusterByWorkload(accumulations, depot, timeMatrix);
            log.info("✅ K-means算法执行成功");
            return result;
            
        } catch (Exception e) {
            log.error("❌ K-means算法执行异常", e);
            throw new RuntimeException("K-means算法执行失败", e);
        }
    }
    
    /**
     * 自动算法选择
     * 优先使用H3，失败时降级到K-means
     */
    private List<List<Accumulation>> executeAutoAlgorithm(
            List<Accumulation> accumulations,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix) {
        
        log.info("🤖 执行自动算法选择：H3优先，失败时降级");
        
        // 算法选择逻辑
        ClusteringAlgorithmType selectedAlgorithm = selectOptimalAlgorithm(accumulations, depot);
        
        if (selectedAlgorithm == ClusteringAlgorithmType.H3) {
            return executeH3Algorithm(accumulations, depot, timeMatrix);
        } else {
            return executeKMeansAlgorithm(accumulations, depot, timeMatrix);
        }
    }
    
    /**
     * 选择最优算法
     * 基于数据特征智能选择最适合的算法
     */
    private ClusteringAlgorithmType selectOptimalAlgorithm(
            List<Accumulation> accumulations,
            TransitDepot depot) {
        
        int pointCount = accumulations.size();
        
        // H3算法适用性评估
        if (pointCount >= 20) { // H3算法在中大规模数据上更有优势
            log.info("🎯 数据规模{}个点，选择H3算法（推荐用于中大规模数据）", pointCount);
            return ClusteringAlgorithmType.H3;
        } else {
            log.info("🎯 数据规模{}个点，选择K-means算法（推荐用于小规模数据）", pointCount);
            return ClusteringAlgorithmType.KMEANS;
        }
    }
    
    // ===================== 性能对比和质量验证 =====================
    
    /**
     * 执行算法性能对比
     * 仅在性能对比模式下执行，不影响正常业务流程
     */
    private void performAlgorithmComparison(
            List<Accumulation> accumulations,
            TransitDepot depot,
            Map<String, TimeInfo> timeMatrix,
            List<List<Accumulation>> primaryResult) {
        
        log.info("📊 开始算法性能对比分析");
        
        try {
            // 记录主算法（已执行）的结果
            String primaryAlgorithm = parseAlgorithmType().name();
            int primaryRouteCount = primaryResult.size();
            
            // 执行对比算法
            long comparisonStartTime = System.currentTimeMillis();
            List<List<Accumulation>> comparisonResult = null;
            String comparisonAlgorithm = "";
            
            if (parseAlgorithmType() == ClusteringAlgorithmType.H3) {
                // 主算法是H3，对比K-means
                comparisonResult = kmeansAlgorithm.clusterByWorkload(accumulations, depot, timeMatrix);
                comparisonAlgorithm = "KMEANS";
            } else {
                // 主算法是K-means，对比H3
                comparisonResult = h3Algorithm.clusterByH3Grid(accumulations, depot, timeMatrix);
                comparisonAlgorithm = "H3";
            }
            
            long comparisonEndTime = System.currentTimeMillis();
            long comparisonTime = comparisonEndTime - comparisonStartTime;
            int comparisonRouteCount = comparisonResult != null ? comparisonResult.size() : 0;
            
            // 生成对比报告
            log.info("📈 算法性能对比报告:");
            log.info("   主算法 ({}): {} 条路线", primaryAlgorithm, primaryRouteCount);
            log.info("   对比算法 ({}): {} 条路线，耗时{}ms", comparisonAlgorithm, comparisonRouteCount, comparisonTime);
            
            // 质量对比分析
            if (comparisonResult != null) {
                performQualityComparison(primaryResult, comparisonResult, primaryAlgorithm, comparisonAlgorithm);
            }
            
        } catch (Exception e) {
            log.warn("⚠️ 算法性能对比执行异常，跳过对比: {}", e.getMessage());
        }
    }
    
    /**
     * 质量对比分析
     */
    private void performQualityComparison(
            List<List<Accumulation>> primaryResult,
            List<List<Accumulation>> comparisonResult,
            String primaryAlgorithm,
            String comparisonAlgorithm) {
        
        // 计算基本统计指标
        double primaryAvgSize = primaryResult.stream().mapToInt(List::size).average().orElse(0);
        double comparisonAvgSize = comparisonResult.stream().mapToInt(List::size).average().orElse(0);
        
        double primaryStdDev = calculateStandardDeviation(primaryResult);
        double comparisonStdDev = calculateStandardDeviation(comparisonResult);
        
        log.info("🔍 质量对比分析:");
        log.info("   {} - 平均点数/路线: {:.1f}, 标准差: {:.2f}", primaryAlgorithm, primaryAvgSize, primaryStdDev);
        log.info("   {} - 平均点数/路线: {:.1f}, 标准差: {:.2f}", comparisonAlgorithm, comparisonAvgSize, comparisonStdDev);
        
        // 质量优势分析
        if (primaryStdDev < comparisonStdDev) {
            log.info("🏆 {}在点数均衡性方面更优 (标准差{:.2f} vs {:.2f})", 
                primaryAlgorithm, primaryStdDev, comparisonStdDev);
        } else {
            log.info("🏆 {}在点数均衡性方面更优 (标准差{:.2f} vs {:.2f})", 
                comparisonAlgorithm, comparisonStdDev, primaryStdDev);
        }
    }
    
    /**
     * 计算路线大小的标准差
     */
    private double calculateStandardDeviation(List<List<Accumulation>> clusters) {
        double mean = clusters.stream().mapToInt(List::size).average().orElse(0);
        double variance = clusters.stream()
            .mapToDouble(List::size)
            .map(size -> Math.pow(size - mean, 2))
            .average()
            .orElse(0.0);
        return Math.sqrt(variance);
    }
    
    /**
     * 验证聚类结果质量
     * 检查基本的质量指标，确保结果可用
     */
    private boolean validateClusteringResult(
            List<List<Accumulation>> result,
            List<Accumulation> originalAccumulations) {
        
        if (result == null || result.isEmpty()) {
            log.warn("❌ 聚类结果为空");
            return false;
        }
        
        // 检查点数完整性
        int resultPointCount = result.stream().mapToInt(List::size).sum();
        if (resultPointCount != originalAccumulations.size()) {
            log.warn("❌ 聚类结果点数不匹配: {} vs {}", resultPointCount, originalAccumulations.size());
            return false;
        }
        
        // 检查空路线
        long emptyRoutes = result.stream().filter(List::isEmpty).count();
        if (emptyRoutes > 0) {
            log.warn("⚠️ 发现{}条空路线", emptyRoutes);
        }
        
        // 检查路线大小合理性
        int maxRouteSize = result.stream().mapToInt(List::size).max().orElse(0);
        int minRouteSize = result.stream().mapToInt(List::size).min().orElse(0);
        
        if (maxRouteSize > 25) { // 假设25个点是合理上限
            log.warn("⚠️ 发现过大路线: {} 个点", maxRouteSize);
        }
        
        if (minRouteSize == 0) {
            log.warn("⚠️ 发现空路线");
        }
        
        log.info("✅ 聚类结果基本质量验证通过: {}条路线, 点数范围{}-{}", 
            result.size(), minRouteSize, maxRouteSize);
        
        return true; // 基本质量验证通过
    }
    
    // ===================== 配置解析工具 =====================
    
    /**
     * 解析算法类型配置
     */
    private ClusteringAlgorithmType parseAlgorithmType() {
        try {
            return ClusteringAlgorithmType.valueOf(algorithmTypeConfig.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("⚠️ 无效的算法类型配置: {}，使用默认AUTO模式", algorithmTypeConfig);
            return ClusteringAlgorithmType.AUTO;
        }
    }
    
    // ===================== 公共方法 =====================
    
    /**
     * 获取当前使用的算法类型
     */
    public ClusteringAlgorithmType getCurrentAlgorithmType() {
        return parseAlgorithmType();
    }
    
    /**
     * 获取算法描述信息
     */
    public String getAlgorithmDescription() {
        return parseAlgorithmType().getDescription();
    }
    
    /**
     * 检查H3算法是否可用
     */
    public boolean isH3AlgorithmAvailable() {
        try {
            return h3Algorithm != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查K-means算法是否可用
     */
    public boolean isKMeansAlgorithmAvailable() {
        try {
            return kmeansAlgorithm != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取适配器状态信息
     */
    public String getAdapterStatus() {
        return String.format("UnifiedClusteringAdapter[算法类型: %s, H3可用: %s, K-means可用: %s, 自动降级: %s]",
            parseAlgorithmType().getDescription(),
            isH3AlgorithmAvailable() ? "是" : "否",
            isKMeansAlgorithmAvailable() ? "是" : "否",
            autoFallback ? "启用" : "禁用");
    }
}