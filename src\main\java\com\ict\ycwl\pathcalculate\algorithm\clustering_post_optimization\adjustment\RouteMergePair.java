package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 路线合并对
 * 
 * 表示一对可以合并的路线及其相关评估信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class RouteMergePair {
    
    /**
     * 第一条路线
     */
    private List<Accumulation> route1;
    
    /**
     * 第二条路线
     */
    private List<Accumulation> route2;
    
    /**
     * 第一条路线工作时间（分钟）
     */
    private double workTime1;
    
    /**
     * 第二条路线工作时间（分钟）
     */
    private double workTime2;
    
    /**
     * 合并后总工作时间（分钟）
     */
    private double combinedWorkTime;
    
    /**
     * 合并优先级评分（越高越优先）
     */
    private double priority;
    
    /**
     * 获取合并后聚集区总数
     */
    public int getCombinedAccumulationCount() {
        return route1.size() + route2.size();
    }
    
    /**
     * 是否满足时间约束
     */
    public boolean satisfiesTimeConstraint() {
        return combinedWorkTime <= 450.0;
    }
    
    /**
     * 获取时间利用率
     */
    public double getTimeUtilization() {
        return combinedWorkTime / 450.0;
    }
    
    /**
     * 是否接近理想工作时间
     */
    public boolean isNearIdealWorkTime() {
        double idealTime = 350.0; // 理想工作时间350分钟
        double deviation = Math.abs(combinedWorkTime - idealTime);
        return deviation <= 50.0; // 允许50分钟偏差
    }
    
    /**
     * 计算时间平衡度
     */
    public double getTimeBalance() {
        double avgTime = (workTime1 + workTime2) / 2.0;
        double variance = (Math.pow(workTime1 - avgTime, 2) + Math.pow(workTime2 - avgTime, 2)) / 2.0;
        double standardDeviation = Math.sqrt(variance);
        
        // 标准化平衡度评分 (0-1，越接近1越平衡)
        return 1.0 - Math.min(1.0, standardDeviation / avgTime);
    }
    
    /**
     * 获取优先级等级描述
     */
    public String getPriorityLevel() {
        if (priority >= 0.8) {
            return "高优先级";
        } else if (priority >= 0.6) {
            return "中优先级";
        } else {
            return "低优先级";
        }
    }
    
    /**
     * 获取时间约束状态描述
     */
    public String getTimeConstraintStatus() {
        if (satisfiesTimeConstraint()) {
            if (isNearIdealWorkTime()) {
                return "理想时间范围";
            } else if (getTimeUtilization() > 0.9) {
                return "接近上限";
            } else {
                return "时间充裕";
            }
        } else {
            return "超时";
        }
    }
    
    /**
     * 生成合并对描述
     */
    public String generateDescription() {
        return String.format("路线合并对 - %.1f分钟 + %.1f分钟 = %.1f分钟 (%s, %s)",
            workTime1, workTime2, combinedWorkTime, 
            getTimeConstraintStatus(), getPriorityLevel());
    }
    
    /**
     * 计算合并收益评分
     */
    public double calculateMergeBenefit() {
        double timeScore = satisfiesTimeConstraint() ? 1.0 : 0.0;
        double utilizationScore = Math.min(1.0, getTimeUtilization());
        double balanceScore = getTimeBalance();
        double sizeScore = Math.min(1.0, getCombinedAccumulationCount() / 15.0); // 15个聚集区为理想规模
        
        return timeScore * 0.4 + utilizationScore * 0.3 + balanceScore * 0.2 + sizeScore * 0.1;
    }
}