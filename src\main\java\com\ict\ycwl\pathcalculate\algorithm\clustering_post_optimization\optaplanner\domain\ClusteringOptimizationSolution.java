package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import org.optaplanner.core.api.domain.solution.PlanningEntityCollectionProperty;
import org.optaplanner.core.api.domain.solution.PlanningScore;
import org.optaplanner.core.api.domain.solution.PlanningSolution;
import org.optaplanner.core.api.domain.solution.ProblemFactCollectionProperty;
import org.optaplanner.core.api.domain.valuerange.ValueRangeProvider;
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * OptaPlanner规划解决方案：聚类优化问题
 * 
 * 这是OptaPlanner的核心类，定义了完整的优化问题：
 * - 需要分配的聚集区（规划实体）
 * - 可选的聚类（值域）
 * - 问题事实（时间矩阵、中转站等）
 * - 优化得分
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@PlanningSolution
@Data
@NoArgsConstructor
public class ClusteringOptimizationSolution {
    
    /**
     * 中转站信息（问题事实，不会被OptaPlanner修改）
     */
    private TransitDepot transitDepot;
    
    /**
     * 时间矩阵（问题事实，不会被OptaPlanner修改）
     */
    private Map<String, TimeInfo> timeMatrix;
    
    /**
     * 可用的聚类列表（值域提供者）
     * OptaPlanner将从这个列表中选择聚类分配给聚集区
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clusterList")
    private List<Cluster> clusterList;
    
    /**
     * 聚集区分配列表（规划实体集合）
     * 这是OptaPlanner要优化的实体，每个分配代表一个聚集区被分配到某个聚类
     */
    @PlanningEntityCollectionProperty
    private List<AccumulationAssignment> accumulationAssignments;
    
    /**
     * 优化得分（由约束提供者计算）
     * 硬得分：约束违反情况（450分钟限制、30分钟差异限制）
     * 软得分：优化目标（地理合理性、负载均衡等）
     */
    @PlanningScore
    private HardSoftScore score;
    
    /**
     * 约束权重配置
     */
    private ConstraintWeights constraintWeights;
    
    /**
     * 优化参数配置
     */
    private OptimizationParameters optimizationParameters;
    
    /**
     * 构造函数 - 创建完整的优化问题
     * 
     * @param transitDepot 中转站
     * @param timeMatrix 时间矩阵
     * @param clusterList 可用聚类列表
     * @param accumulationAssignments 聚集区分配列表
     */
    public ClusteringOptimizationSolution(
        TransitDepot transitDepot,
        Map<String, TimeInfo> timeMatrix,
        List<Cluster> clusterList,
        List<AccumulationAssignment> accumulationAssignments
    ) {
        this.transitDepot = transitDepot;
        this.timeMatrix = timeMatrix;
        this.clusterList = clusterList;
        this.accumulationAssignments = accumulationAssignments;
        this.constraintWeights = ConstraintWeights.createDefault();
        this.optimizationParameters = OptimizationParameters.createDefault();
        this.score = null; // 将由OptaPlanner计算
    }
    
    /**
     * 获取问题规模信息
     * 
     * @return 问题规模描述
     */
    public String getProblemSizeInfo() {
        int assignmentCount = accumulationAssignments != null ? accumulationAssignments.size() : 0;
        int clusterCount = clusterList != null ? clusterList.size() : 0;
        
        return String.format("聚集区数量: %d, 聚类数量: %d, 搜索空间: %d^%d", 
            assignmentCount, clusterCount, clusterCount, assignmentCount);
    }
    
    /**
     * 获取中转站名称
     * 
     * @return 中转站名称
     */
    public String getTransitDepotName() {
        return transitDepot != null ? transitDepot.getTransitDepotName() : "Unknown";
    }
    
    /**
     * 获取中转站ID
     * 
     * @return 中转站ID
     */
    public String getTransitDepotId() {
        if (transitDepot != null && transitDepot.getTransitDepotId() != null) {
            return String.valueOf(transitDepot.getTransitDepotId());
        }
        return null;
    }
    
    /**
     * 检查解决方案是否已初始化
     * 
     * @return true如果解决方案完整且有效
     */
    public boolean isInitialized() {
        return transitDepot != null 
            && timeMatrix != null && !timeMatrix.isEmpty()
            && clusterList != null && !clusterList.isEmpty()
            && accumulationAssignments != null && !accumulationAssignments.isEmpty();
    }
    
    /**
     * 检查是否所有聚集区都已分配
     * 
     * @return true如果所有聚集区都有分配的聚类
     */
    public boolean isFullyAssigned() {
        if (accumulationAssignments == null) return false;
        
        return accumulationAssignments.stream()
            .allMatch(AccumulationAssignment::isAssigned);
    }
    
    /**
     * 获取未分配的聚集区数量
     * 
     * @return 未分配数量
     */
    public long getUnassignedCount() {
        if (accumulationAssignments == null) return 0;
        
        return accumulationAssignments.stream()
            .filter(assignment -> !assignment.isAssigned())
            .count();
    }
    
    /**
     * 获取已分配的聚集区数量
     * 
     * @return 已分配数量
     */
    public long getAssignedCount() {
        if (accumulationAssignments == null) return 0;
        
        return accumulationAssignments.stream()
            .filter(AccumulationAssignment::isAssigned)
            .count();
    }
    
    /**
     * 复制解决方案（深拷贝）
     * 
     * @return 深拷贝的解决方案
     */
    public ClusteringOptimizationSolution copy() {
        ClusteringOptimizationSolution copy = new ClusteringOptimizationSolution();
        copy.setTransitDepot(this.transitDepot); // TransitDepot是不可变的
        copy.setTimeMatrix(this.timeMatrix); // TimeMatrix是不可变的
        
        // 深拷贝聚类列表
        if (this.clusterList != null) {
            copy.setClusterList(this.clusterList.stream()
                .map(Cluster::copy)
                .collect(Collectors.toList()));
        }
        
        // 深拷贝分配列表
        if (this.accumulationAssignments != null) {
            copy.setAccumulationAssignments(this.accumulationAssignments.stream()
                .map(assignment -> new AccumulationAssignment(assignment.getAccumulation(), assignment.getAssignedCluster()))
                .collect(Collectors.toList()));
        }
        
        copy.setConstraintWeights(this.constraintWeights != null ? this.constraintWeights.copy() : null);
        copy.setOptimizationParameters(this.optimizationParameters != null ? this.optimizationParameters.copy() : null);
        copy.setScore(this.score);
        
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("ClusteringOptimizationSolution{depot='%s', %s, score=%s, assigned=%d/%d}",
            getTransitDepotName(),
            getProblemSizeInfo(),
            score != null ? score.toString() : "未计算",
            getAssignedCount(),
            accumulationAssignments != null ? accumulationAssignments.size() : 0);
    }
}