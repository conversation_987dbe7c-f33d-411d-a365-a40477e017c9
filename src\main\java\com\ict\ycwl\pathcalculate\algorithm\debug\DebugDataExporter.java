package com.ict.ycwl.pathcalculate.algorithm.debug;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.ict.ycwl.pathcalculate.algorithm.core.RouteTimeCalculator;
import com.ict.ycwl.pathcalculate.algorithm.core.TimeEvaluationResult;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算法调试数据导出器
 * 
 * 用于导出算法各个阶段的中间结果，便于调试和效果评估
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Slf4j
public class DebugDataExporter {
    
    private final ObjectMapper objectMapper;
    private final String debugOutputDir;
    private final RouteTimeCalculator routeTimeCalculator;
    
    public DebugDataExporter() {
        this(null);
    }
    
    public DebugDataExporter(RouteTimeCalculator routeTimeCalculator) {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        this.objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        
        this.debugOutputDir = "target/test-results/algorithm/debug/";
        this.routeTimeCalculator = routeTimeCalculator;
        createDebugDirectory();
        
        if (routeTimeCalculator == null) {
            log.info("⚠️ DebugDataExporter未配置RouteTimeCalculator，将使用简单时间计算");
        } else {
            log.info("✅ DebugDataExporter已配置H3时间计算器");
        }
    }
    
    /**
     * 导出聚类阶段结果（包含二次优化后的数据）
     * 使用clustering_results_debug_格式符合README.md文档规范
     */
    public void exportClusteringResults(Map<Long, List<List<Accumulation>>> routeClusters, 
                                      Map<Long, TransitDepot> transitDepots,
                                      String sessionId) {
        log.info("🔍 开始导出聚类结果，会话ID: {}, 路线聚类数: {}", sessionId, routeClusters.size());
        try {
            Map<String, Object> clusteringData = new HashMap<>();
            clusteringData.put("timestamp", getCurrentTimestamp());
            clusteringData.put("sessionId", sessionId);
            clusteringData.put("stage", "clustering");
            clusteringData.put("description", "聚类阶段：聚集区分配到路线的结果（含二次优化）");
            
            Map<String, Object> results = new HashMap<>();
            
            for (Map.Entry<Long, List<List<Accumulation>>> entry : routeClusters.entrySet()) {
                Long depotId = entry.getKey();
                List<List<Accumulation>> clusters = entry.getValue();
                TransitDepot depot = transitDepots.get(depotId);
                
                Map<String, Object> depotResult = new HashMap<>();
                depotResult.put("transitDepotId", depotId);
                depotResult.put("transitDepotName", depot != null ? depot.getTransitDepotName() : "Unknown");
                depotResult.put("totalClusters", clusters.size());
                
                Map<String, Object> clusterDetails = new HashMap<>();
                for (int i = 0; i < clusters.size(); i++) {
                    List<Accumulation> cluster = clusters.get(i);
                    Map<String, Object> clusterInfo = new HashMap<>();
                    clusterInfo.put("accumulationCount", cluster.size());
                    
                    // 使用H3算法的时间估算计算totalWorkTime
                    double h3EstimatedTime = calculateH3WorkTime(cluster, depot);
                    clusterInfo.put("totalWorkTime", h3EstimatedTime);
                    
                    clusterInfo.put("accumulations", cluster.stream().map(acc -> {
                        Map<String, Object> accInfo = new HashMap<>();
                        accInfo.put("id", acc.getAccumulationId());
                        accInfo.put("name", acc.getAccumulationName());
                        accInfo.put("longitude", acc.getLongitude());
                        accInfo.put("latitude", acc.getLatitude());
                        accInfo.put("deliveryTime", acc.getDeliveryTime());
                        return accInfo;
                    }).toArray());
                    
                    clusterDetails.put("cluster_" + (i + 1), clusterInfo);
                }
                
                depotResult.put("clusters", clusterDetails);
                results.put("depot_" + depotId, depotResult);
            }
            
            clusteringData.put("results", results);
            
            // 统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalDepots", routeClusters.size());
            statistics.put("totalRoutes", routeClusters.values().stream().mapToInt(List::size).sum());
            statistics.put("totalAccumulations", routeClusters.values().stream()
                    .flatMap(List::stream)
                    .mapToInt(List::size)
                    .sum());
            
            clusteringData.put("statistics", statistics);
            
            String fileName = "clustering_results_" + sessionId + ".json";
            String fullPath = debugOutputDir + fileName;
            log.info("🔍 准备保存文件: {}", fullPath);
            
            saveToFile(clusteringData, fileName);
            log.info("✅ 聚类结果已导出（含二次优化）: {}", fileName);
            
            // 验证文件是否真的创建了
            File savedFile = new File(fullPath);
            if (savedFile.exists()) {
                log.info("🔍 文件验证成功: {} (大小: {} bytes)", fullPath, savedFile.length());
            } else {
                log.error("❌ 文件创建失败: {}", fullPath);
            }
            
        } catch (Exception e) {
            log.error("导出聚类结果失败", e);
        }
    }
    
    /**
     * 导出TSP优化阶段结果
     */
    public void exportTSPResults(Map<Long, List<RouteResult>> optimizedRoutes, String sessionId) {
        try {
            Map<String, Object> tspData = new HashMap<>();
            tspData.put("timestamp", getCurrentTimestamp());
            tspData.put("sessionId", sessionId);
            tspData.put("stage", "tsp_optimization");
            tspData.put("description", "TSP优化阶段：路线内访问顺序优化结果");
            
            Map<String, Object> results = new HashMap<>();
            
            for (Map.Entry<Long, List<RouteResult>> entry : optimizedRoutes.entrySet()) {
                Long depotId = entry.getKey();
                List<RouteResult> routes = entry.getValue();
                
                Map<String, Object> depotResult = new HashMap<>();
                depotResult.put("transitDepotId", depotId);
                depotResult.put("totalRoutes", routes.size());
                
                Map<String, Object> routeDetails = new HashMap<>();
                for (int i = 0; i < routes.size(); i++) {
                    RouteResult route = routes.get(i);
                    Map<String, Object> routeInfo = new HashMap<>();
                    routeInfo.put("routeName", route.getRouteName());
                    routeInfo.put("totalWorkTime", route.getTotalWorkTime());
                    routeInfo.put("accumulationCount", route.getAccumulationCount());
                    routeInfo.put("accumulationSequence", route.getAccumulationSequence());
                    
                    routeDetails.put("route_" + (i + 1), routeInfo);
                }
                
                depotResult.put("routes", routeDetails);
                results.put("depot_" + depotId, depotResult);
            }
            
            tspData.put("results", results);
            
            // 统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalDepots", optimizedRoutes.size());
            statistics.put("totalRoutes", optimizedRoutes.values().stream().mapToInt(List::size).sum());
            statistics.put("averageWorkTime", optimizedRoutes.values().stream()
                    .flatMap(List::stream)
                    .mapToDouble(RouteResult::getTotalWorkTime)
                    .average()
                    .orElse(0.0));
            
            tspData.put("statistics", statistics);
            
            saveToFile(tspData, "tsp_results_" + sessionId + ".json");
            log.info("TSP优化结果已导出: {}", sessionId);
            
        } catch (Exception e) {
            log.error("导出TSP结果失败", e);
        }
    }
    
    /**
     * 导出凸包处理阶段结果
     */
    public void exportConvexHullResults(Map<Long, List<RouteResult>> routesWithConvexHulls, 
                                      List<ConflictResolution> conflictResolutions,
                                      String sessionId) {
        try {
            Map<String, Object> convexHullData = new HashMap<>();
            convexHullData.put("timestamp", getCurrentTimestamp());
            convexHullData.put("sessionId", sessionId);
            convexHullData.put("stage", "convex_hull_processing");
            convexHullData.put("description", "凸包处理阶段：凸包生成和冲突解决结果");
            
            Map<String, Object> results = new HashMap<>();
            
            for (Map.Entry<Long, List<RouteResult>> entry : routesWithConvexHulls.entrySet()) {
                Long depotId = entry.getKey();
                List<RouteResult> routes = entry.getValue();
                
                Map<String, Object> depotResult = new HashMap<>();
                depotResult.put("transitDepotId", depotId);
                depotResult.put("totalRoutes", routes.size());
                
                Map<String, Object> routeDetails = new HashMap<>();
                for (int i = 0; i < routes.size(); i++) {
                    RouteResult route = routes.get(i);
                    Map<String, Object> routeInfo = new HashMap<>();
                    routeInfo.put("routeName", route.getRouteName());
                    routeInfo.put("hasConvexHull", route.getConvexHull() != null);
                    if (route.getConvexHull() != null) {
                        routeInfo.put("convexHullPoints", route.getConvexHull().stream().map(point -> {
                            Map<String, Object> pointInfo = new HashMap<>();
                            pointInfo.put("longitude", point.getLongitude());
                            pointInfo.put("latitude", point.getLatitude());
                            return pointInfo;
                        }).toArray());
                    }
                    
                    routeDetails.put("route_" + (i + 1), routeInfo);
                }
                
                depotResult.put("routes", routeDetails);
                results.put("depot_" + depotId, depotResult);
            }
            
            convexHullData.put("results", results);
            
            // 冲突解决信息
            if (conflictResolutions != null && !conflictResolutions.isEmpty()) {
                Map<String, Object> conflicts = new HashMap<>();
                for (int i = 0; i < conflictResolutions.size(); i++) {
                    ConflictResolution resolution = conflictResolutions.get(i);
                    Map<String, Object> conflictInfo = new HashMap<>();
                    conflictInfo.put("conflictType", resolution.getConflictType());
                    conflictInfo.put("resolutionStrategy", resolution.getResolutionStrategy());
                    conflictInfo.put("affectedRoutes", resolution.getAffectedRouteIds());
                    conflictInfo.put("resolved", resolution.isResolved());
                    
                    conflicts.put("conflict_" + (i + 1), conflictInfo);
                }
                convexHullData.put("conflictResolutions", conflicts);
            }
            
            saveToFile(convexHullData, "convex_hull_results_" + sessionId + ".json");
            log.info("凸包处理结果已导出: {}", sessionId);
            
        } catch (Exception e) {
            log.error("导出凸包处理结果失败", e);
        }
    }
    
    /**
     * 导出时间均衡阶段结果
     */
    public void exportTimeBalanceResults(Map<Long, List<RouteResult>> balancedRoutes,
                                       TimeBalanceStats balanceStats,
                                       List<TimeBalanceAdjustment> adjustments,
                                       String sessionId) {
        try {
            Map<String, Object> balanceData = new HashMap<>();
            balanceData.put("timestamp", getCurrentTimestamp());
            balanceData.put("sessionId", sessionId);
            balanceData.put("stage", "time_balance");
            balanceData.put("description", "时间均衡阶段：多层级时间均衡优化结果");
            
            Map<String, Object> results = new HashMap<>();
            
            for (Map.Entry<Long, List<RouteResult>> entry : balancedRoutes.entrySet()) {
                Long depotId = entry.getKey();
                List<RouteResult> routes = entry.getValue();
                
                Map<String, Object> depotResult = new HashMap<>();
                depotResult.put("transitDepotId", depotId);
                depotResult.put("totalRoutes", routes.size());
                depotResult.put("totalWorkTime", routes.stream().mapToDouble(RouteResult::getTotalWorkTime).sum());
                depotResult.put("averageWorkTime", routes.stream().mapToDouble(RouteResult::getTotalWorkTime).average().orElse(0.0));
                depotResult.put("workTimeVariance", calculateVariance(routes.stream().mapToDouble(RouteResult::getTotalWorkTime).toArray()));
                
                Map<String, Object> routeDetails = new HashMap<>();
                for (int i = 0; i < routes.size(); i++) {
                    RouteResult route = routes.get(i);
                    Map<String, Object> routeInfo = new HashMap<>();
                    routeInfo.put("routeName", route.getRouteName());
                    routeInfo.put("finalWorkTime", route.getTotalWorkTime());
                    routeInfo.put("accumulationCount", route.getAccumulationCount());
                    
                    routeDetails.put("route_" + (i + 1), routeInfo);
                }
                
                depotResult.put("routes", routeDetails);
                results.put("depot_" + depotId, depotResult);
            }
            
            balanceData.put("results", results);
            
            // 时间均衡统计
            if (balanceStats != null) {
                Map<String, Object> statsInfo = new HashMap<>();
                statsInfo.put("routeTimeVariance", balanceStats.getRouteTimeVariance());
                statsInfo.put("depotTimeVariance", balanceStats.getDepotTimeVariance());
                statsInfo.put("teamTimeVariance", balanceStats.getTeamTimeVariance());
                statsInfo.put("balanceGrade", balanceStats.getBalanceGrade());
                
                balanceData.put("balanceStatistics", statsInfo);
            }
            
            // 调整记录
            if (adjustments != null && !adjustments.isEmpty()) {
                Map<String, Object> adjustmentInfo = new HashMap<>();
                for (int i = 0; i < adjustments.size(); i++) {
                    TimeBalanceAdjustment adjustment = adjustments.get(i);
                    Map<String, Object> adjInfo = new HashMap<>();
                    adjInfo.put("adjustmentType", adjustment.getAdjustmentType());
                    adjInfo.put("fromRouteId", adjustment.getFromRouteId());
                    adjInfo.put("toRouteId", adjustment.getToRouteId());
                    adjInfo.put("accumulationId", adjustment.getAccumulationId());
                    adjInfo.put("timeSaved", adjustment.getTimeSaved());
                    
                    adjustmentInfo.put("adjustment_" + (i + 1), adjInfo);
                }
                balanceData.put("adjustments", adjustmentInfo);
            }
            
            saveToFile(balanceData, "time_balance_results_" + sessionId + ".json");
            log.info("时间均衡结果已导出: {}", sessionId);
            
        } catch (Exception e) {
            log.error("导出时间均衡结果失败", e);
        }
    }
    
    /**
     * 导出最终算法结果
     */
    public void exportFinalResults(PathPlanningResult finalResult, String sessionId) {
        try {
            Map<String, Object> finalData = new HashMap<>();
            finalData.put("timestamp", getCurrentTimestamp());
            finalData.put("sessionId", sessionId);
            finalData.put("stage", "final_results");
            finalData.put("description", "最终结果：完整的路径规划算法输出");
            
            Map<String, Object> results = new HashMap<>();
            results.put("success", finalResult.isSuccess());
            results.put("executionTime", finalResult.getExecutionTime());
            results.put("totalRoutes", finalResult.getRoutes().size());
            results.put("totalWorkTime", finalResult.getTotalWorkTime());
            results.put("averageWorkTime", finalResult.getAverageWorkTime());
            
            if (finalResult.getTimeBalanceStats() != null) {
                Map<String, Object> balanceStats = new HashMap<>();
                balanceStats.put("routeTimeVariance", finalResult.getTimeBalanceStats().getRouteTimeVariance());
                balanceStats.put("depotTimeVariance", finalResult.getTimeBalanceStats().getDepotTimeVariance());
                balanceStats.put("teamTimeVariance", finalResult.getTimeBalanceStats().getTeamTimeVariance());
                balanceStats.put("balanceGrade", finalResult.getTimeBalanceStats().getBalanceGrade());
                results.put("timeBalanceStats", balanceStats);
            }
            
            finalData.put("results", results);
            
            saveToFile(finalData, "final_results_" + sessionId + ".json");
            log.info("最终结果已导出: {}", sessionId);
            
        } catch (Exception e) {
            log.error("导出最终结果失败", e);
        }
    }
    
    /**
     * 生成调试会话摘要
     */
    public void exportSessionSummary(String sessionId, long totalExecutionTime, 
                                   Map<String, Long> stageExecutionTimes) {
        try {
            Map<String, Object> summary = new HashMap<>();
            summary.put("sessionId", sessionId);
            summary.put("timestamp", getCurrentTimestamp());
            summary.put("totalExecutionTime", totalExecutionTime);
            summary.put("stageExecutionTimes", stageExecutionTimes);
            
            Map<String, String> exportedFiles = new HashMap<>();
            exportedFiles.put("clustering", "clustering_results_" + sessionId + ".json");
            exportedFiles.put("tsp", "tsp_results_" + sessionId + ".json");
            exportedFiles.put("convexHull", "convex_hull_results_" + sessionId + ".json");
            exportedFiles.put("timeBalance", "time_balance_results_" + sessionId + ".json");
            exportedFiles.put("final", "final_results_" + sessionId + ".json");
            
            summary.put("exportedFiles", exportedFiles);
            summary.put("debugOutputDirectory", debugOutputDir);
            
            saveToFile(summary, "session_summary_" + sessionId + ".json");
            log.info("调试会话摘要已导出: {}", sessionId);
            
        } catch (Exception e) {
            log.error("导出会话摘要失败", e);
        }
    }
    
    // ================== 工具方法 ==================
    
    private void createDebugDirectory() {
        try {
            Files.createDirectories(Paths.get(debugOutputDir));
        } catch (IOException e) {
            log.error("创建调试输出目录失败: {}", debugOutputDir, e);
        }
    }
    
    private void saveToFile(Object data, String filename) throws IOException {
        String filepath = debugOutputDir + filename;
        objectMapper.writeValue(new File(filepath), data);
        log.debug("调试数据已保存: {}", filepath);
    }
    
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    private double calculateVariance(double[] values) {
        if (values.length <= 1) return 0.0;
        
        double mean = 0.0;
        for (double value : values) {
            mean += value;
        }
        mean /= values.length;
        
        double variance = 0.0;
        for (double value : values) {
            variance += Math.pow(value - mean, 2);
        }
        return variance / values.length;
    }
    
    /**
     * 使用H3算法计算路线工作时间
     * @param cluster 聚类中的点集合
     * @param depot 中转站信息
     * @return H3算法估算的总工作时间（分钟）
     */
    private double calculateH3WorkTime(List<Accumulation> cluster, TransitDepot depot) {
        if (routeTimeCalculator == null) {
            // 如果没有时间计算器，回退到简单的deliveryTime加和
            log.warn("⚠️ RouteTimeCalculator未初始化，使用简单时间计算");
            return cluster.stream().mapToDouble(Accumulation::getDeliveryTime).sum();
        }
        
        try {
            // 将cluster作为单地块路线进行评估
            List<List<Accumulation>> singleBlockRoute = Arrays.asList(cluster);
            TimeEvaluationResult result = routeTimeCalculator.evaluateCurrentRoute(singleBlockRoute, depot);
            
            // 返回总时间（小时转分钟）
            double totalTimeMinutes = result.getTotalTimeHours() * 60.0;
            
            log.debug("🔍 H3时间计算: {}个点 -> {:.1f}分钟 (配送:{:.1f}+行驶:{:.1f}+往返:{:.1f})", 
                cluster.size(), totalTimeMinutes,
                result.getServiceTimeHours() * 60.0,
                result.getTravelTimeHours() * 60.0, 
                result.getDepotTimeHours() * 60.0);
            
            return totalTimeMinutes;
            
        } catch (Exception e) {
            log.error("❌ H3时间计算失败，回退到简单计算", e);
            return cluster.stream().mapToDouble(Accumulation::getDeliveryTime).sum();
        }
    }
    
    /**
     * 生成调试会话ID
     * 格式：debug_yyyyMMdd_HHmmss （符合README.md文档规范）
     */
    public static String generateSessionId() {
        return "debug_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    }
}