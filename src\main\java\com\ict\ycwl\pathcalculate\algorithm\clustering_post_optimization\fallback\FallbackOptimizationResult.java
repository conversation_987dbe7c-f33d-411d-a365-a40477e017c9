package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 业内标准降级算法优化结果
 * 
 * 封装降级算法执行后的完整结果信息，包括优化效果、性能指标、
 * 执行统计等，用于评估降级算法的有效性和性能表现
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
@Slf4j
public class FallbackOptimizationResult {
    
    /**
     * 优化是否成功
     */
    private boolean success;
    
    /**
     * 优化后的路线列表
     */
    private List<List<Accumulation>> optimizedRoutes;
    
    /**
     * 原始路线数量
     */
    private int originalRouteCount;
    
    /**
     * 优化后路线数量
     */
    private int optimizedRouteCount;
    
    /**
     * 使用的降级策略
     */
    private FallbackStrategy strategy;
    
    /**
     * 优化性能指标
     */
    private OptimizationMetrics optimizationMetrics;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 算法详细信息
     */
    private String algorithmDetails;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 算法参数信息
     */
    private String parameterInfo;
    
    /**
     * 收敛信息
     */
    private ConvergenceInfo convergenceInfo;
    
    /**
     * 计算路线数量变化
     */
    public int getRouteCountChange() {
        return optimizedRouteCount - originalRouteCount;
    }
    
    /**
     * 计算路线数量变化率
     */
    public double getRouteCountChangeRate() {
        if (originalRouteCount == 0) return 0.0;
        return (double) getRouteCountChange() / originalRouteCount * 100.0;
    }
    
    /**
     * 判断是否有实质性改进
     */
    public boolean hasSignificantImprovement() {
        if (!success || optimizationMetrics == null) {
            return false;
        }
        
        // 时间改进 > 5% 或 约束违反减少 > 0
        return optimizationMetrics.getTimeImprovement() > 5.0 || 
               optimizationMetrics.getViolationReduction() > 0;
    }
    
    /**
     * 获取性能评级
     */
    public String getPerformanceGrade() {
        if (!success) return "F";
        if (optimizationMetrics == null) return "N/A";
        
        double timeImprovement = optimizationMetrics.getTimeImprovement();
        int violationReduction = optimizationMetrics.getViolationReduction();
        
        // A级：时间改进>15% 且 约束违反减少>2
        if (timeImprovement > 15.0 && violationReduction > 2) return "A";
        
        // B级：时间改进>10% 或 约束违反减少>1
        if (timeImprovement > 10.0 || violationReduction > 1) return "B";
        
        // C级：时间改进>5% 或 约束违反减少>0
        if (timeImprovement > 5.0 || violationReduction > 0) return "C";
        
        // D级：有轻微改进
        if (timeImprovement > 0 || violationReduction >= 0) return "D";
        
        // F级：无改进或恶化
        return "F";
    }
    
    /**
     * 计算执行效率（改进效果/执行时间）
     */
    public double getExecutionEfficiency() {
        if (executionTimeMs == 0 || !success || optimizationMetrics == null) {
            return 0.0;
        }
        
        double improvement = Math.max(optimizationMetrics.getTimeImprovement(), 
                                    optimizationMetrics.getViolationReduction() * 5.0);
        
        return improvement / (executionTimeMs / 1000.0); // 改进效果/秒
    }
    
    /**
     * 生成详细的结果报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("📊 降级算法优化结果报告\n");
        report.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
        
        // 基本信息
        report.append(String.format("🎯 策略: %s\n", strategy != null ? strategy.getName() : "未知"));
        report.append(String.format("✅ 成功: %s\n", success ? "是" : "否"));
        report.append(String.format("📊 性能评级: %s\n", getPerformanceGrade()));
        report.append(String.format("⏱️ 执行时间: %dms (%.2fs)\n", executionTimeMs, executionTimeMs / 1000.0));
        
        // 路线变化
        report.append("\n📈 路线数量变化:\n");
        report.append(String.format("   原始路线: %d条\n", originalRouteCount));
        report.append(String.format("   优化路线: %d条\n", optimizedRouteCount));
        report.append(String.format("   数量变化: %+d条 (%.1f%%)\n", 
            getRouteCountChange(), getRouteCountChangeRate()));
        
        // 性能指标
        if (optimizationMetrics != null) {
            report.append("\n📊 优化效果:\n");
            report.append(String.format("   时间改进: %.1f%%\n", optimizationMetrics.getTimeImprovement()));
            report.append(String.format("   约束违反减少: %d个\n", optimizationMetrics.getViolationReduction()));
            report.append(String.format("   约束满足率: %.1f%%\n", 
                optimizationMetrics.getConstraintSatisfactionRate() * 100));
            report.append(String.format("   执行效率: %.2f 改进/秒\n", getExecutionEfficiency()));
        }
        
        // 收敛信息
        if (convergenceInfo != null) {
            report.append("\n🎯 收敛信息:\n");
            report.append(String.format("   总迭代数: %d\n", convergenceInfo.getTotalIterations()));
            report.append(String.format("   有效改进: %d次\n", convergenceInfo.getEffectiveImprovements()));
            report.append(String.format("   收敛代数: %d\n", convergenceInfo.getConvergenceGeneration()));
            report.append(String.format("   最终评分: %.3f\n", convergenceInfo.getFinalScore()));
        }
        
        // 算法详情
        if (algorithmDetails != null && !algorithmDetails.isEmpty()) {
            report.append(String.format("\n🔧 算法详情: %s\n", algorithmDetails));
        }
        
        // 参数信息
        if (parameterInfo != null && !parameterInfo.isEmpty()) {
            report.append(String.format("⚙️ 参数配置: %s\n", parameterInfo));
        }
        
        // 结果消息
        if (message != null && !message.isEmpty()) {
            report.append(String.format("\n💬 执行消息: %s\n", message));
        }
        
        report.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
        
        return report.toString();
    }
    
    /**
     * 生成简要摘要
     */
    public String generateSummary() {
        if (!success) {
            return String.format("❌ %s执行失败: %s", 
                strategy != null ? strategy.getName() : "未知策略", 
                message != null ? message : "未知错误");
        }
        
        return String.format("✅ %s: %d→%d条路线 | %s级 | %.1fs | %s", 
            strategy != null ? strategy.getCode() : "未知",
            originalRouteCount, optimizedRouteCount,
            getPerformanceGrade(),
            executionTimeMs / 1000.0,
            hasSignificantImprovement() ? "显著改进" : "轻微改进");
    }
    
    /**
     * 收敛信息内部类
     */
    @Data
    @Builder
    public static class ConvergenceInfo {
        
        /**
         * 总迭代次数
         */
        private int totalIterations;
        
        /**
         * 产生有效改进的迭代次数
         */
        private int effectiveImprovements;
        
        /**
         * 收敛时的迭代代数
         */
        private int convergenceGeneration;
        
        /**
         * 最终目标函数值/评分
         */
        private double finalScore;
        
        /**
         * 初始目标函数值/评分
         */
        private double initialScore;
        
        /**
         * 是否达到收敛
         */
        private boolean converged;
        
        /**
         * 收敛原因
         */
        private String convergenceReason;
        
        /**
         * 计算改进率
         */
        public double getImprovementRate() {
            if (totalIterations == 0) return 0.0;
            return (double) effectiveImprovements / totalIterations * 100.0;
        }
        
        /**
         * 计算评分改进
         */
        public double getScoreImprovement() {
            if (initialScore == 0) return 0.0;
            return (finalScore - initialScore) / Math.abs(initialScore) * 100.0;
        }
    }
}