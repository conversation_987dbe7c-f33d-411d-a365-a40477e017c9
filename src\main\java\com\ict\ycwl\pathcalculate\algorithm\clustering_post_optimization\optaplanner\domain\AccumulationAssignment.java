package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import org.optaplanner.core.api.domain.entity.PlanningEntity;
import org.optaplanner.core.api.domain.variable.PlanningVariable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * OptaPlanner规划实体：聚集区分配
 * 
 * 表示将一个聚集区分配给某个聚类的决策变量
 * 这是OptaPlanner优化的核心实体，每个实例代表一个分配决策
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@PlanningEntity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccumulationAssignment {
    
    /**
     * 被分配的聚集区（输入数据，不会被OptaPlanner修改）
     */
    private Accumulation accumulation;
    
    /**
     * OptaPlanner规划变量：分配到的聚类
     * 这是OptaPlanner会动态调整的变量
     */
    @PlanningVariable(valueRangeProviderRefs = "clusterList")
    private Cluster assignedCluster;
    
    /**
     * 构造函数 - 仅设置聚集区，聚类将由OptaPlanner分配
     * 
     * @param accumulation 要分配的聚集区
     */
    public AccumulationAssignment(Accumulation accumulation) {
        this.accumulation = accumulation;
        this.assignedCluster = null; // 初始未分配
    }
    
    /**
     * 获取聚集区的工作时间
     * 
     * @return 工作时间（分钟）
     */
    public double getWorkTime() {
        return accumulation != null && accumulation.getDeliveryTime() != null 
            ? accumulation.getDeliveryTime() 
            : 0.0;
    }
    
    /**
     * 获取聚集区ID
     * 
     * @return 聚集区ID
     */
    public String getAccumulationId() {
        if (accumulation != null && accumulation.getAccumulationId() != null) {
            return String.valueOf(accumulation.getAccumulationId());
        }
        return null;
    }
    
    /**
     * 获取聚集区地理坐标
     * 
     * @return 经纬度坐标
     */
    public double[] getCoordinates() {
        if (accumulation != null && accumulation.getLongitude() != null && accumulation.getLatitude() != null) {
            return new double[]{accumulation.getLongitude(), accumulation.getLatitude()};
        }
        return new double[]{0.0, 0.0};
    }
    
    /**
     * 获取分配的聚类ID
     * 
     * @return 聚类ID，如果未分配则返回null
     */
    public String getAssignedClusterId() {
        return assignedCluster != null ? assignedCluster.getId() : null;
    }
    
    /**
     * 检查是否已分配
     * 
     * @return true如果已分配到聚类
     */
    public boolean isAssigned() {
        return assignedCluster != null;
    }
    
    @Override
    public String toString() {
        return String.format("AccumulationAssignment{accumulation=%s, assignedCluster=%s, workTime=%.1f}",
            getAccumulationId(),
            getAssignedClusterId(),
            getWorkTime());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        AccumulationAssignment that = (AccumulationAssignment) obj;
        return accumulation != null ? accumulation.equals(that.accumulation) : that.accumulation == null;
    }
    
    @Override
    public int hashCode() {
        return accumulation != null ? accumulation.hashCode() : 0;
    }
}