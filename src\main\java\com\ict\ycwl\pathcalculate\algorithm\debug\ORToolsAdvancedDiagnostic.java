package com.ict.ycwl.pathcalculate.algorithm.debug;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * OR-Tools高级诊断工具
 * 深度分析OR-Tools运行时失败的根本原因
 */
@Slf4j
public class ORToolsAdvancedDiagnostic {
    
    /**
     * 全面诊断OR-Tools环境
     */
    public static void performComprehensiveDiagnosis() {
        log.info("========== OR-Tools高级诊断开始 ==========");
        
        // 1. 系统环境诊断
        diagnoseSystemEnvironment();
        
        // 2. Java环境诊断
        diagnoseJavaEnvironment();
        
        // 3. 类路径诊断
        diagnoseClasspath();
        
        // 4. JNI库诊断
        diagnoseJNILibraries();
        
        // 5. 分步测试OR-Tools组件
        diagnoseORToolsComponents();
        
        // 6. protobuf版本冲突诊断
        diagnoseProtobufConflicts();
        
        log.info("========== OR-Tools高级诊断结束 ==========");
    }
    
    /**
     * 系统环境诊断
     */
    private static void diagnoseSystemEnvironment() {
        log.info("--- 系统环境诊断 ---");
        
        Properties props = System.getProperties();
        log.info("操作系统: {} {} {}", 
            props.getProperty("os.name"),
            props.getProperty("os.version"),
            props.getProperty("os.arch"));
        
        log.info("Java版本: {} ({})", 
            props.getProperty("java.version"),
            props.getProperty("java.vendor"));
        
        log.info("Java位数: {}", props.getProperty("sun.arch.data.model"));
        
        // 检查Visual C++ Redistributable
        checkVCRedistributable();
    }
    
    /**
     * Java环境诊断
     */
    private static void diagnoseJavaEnvironment() {
        log.info("--- Java环境诊断 ---");
        
        Runtime runtime = Runtime.getRuntime();
        log.info("最大内存: {}MB", runtime.maxMemory() / 1024 / 1024);
        log.info("总内存: {}MB", runtime.totalMemory() / 1024 / 1024);
        log.info("空闲内存: {}MB", runtime.freeMemory() / 1024 / 1024);
        
        log.info("Java类路径: {}", System.getProperty("java.class.path"));
        log.info("Java库路径: {}", System.getProperty("java.library.path"));
        log.info("临时目录: {}", System.getProperty("java.io.tmpdir"));
    }
    
    /**
     * 类路径诊断
     */
    private static void diagnoseClasspath() {
        log.info("--- 类路径诊断 ---");
        
        // 检查OR-Tools相关JAR包
        String[] ortoolsClasses = {
            "com.google.ortools.Loader",
            "com.google.ortools.constraintsolver.RoutingModel",
            "com.google.ortools.constraintsolver.RoutingIndexManager",
            "com.google.ortools.constraintsolver.Assignment"
        };
        
        for (String className : ortoolsClasses) {
            try {
                Class<?> clazz = Class.forName(className);
                log.info("✅ 类存在: {} (位置: {})", className, 
                    clazz.getProtectionDomain().getCodeSource().getLocation());
            } catch (ClassNotFoundException e) {
                log.error("❌ 类不存在: {}", className);
            } catch (Exception e) {
                log.error("⚠️  类检查异常: {} - {}", className, e.getMessage());
            }
        }
        
        // 检查protobuf相关类
        checkProtobufClasses();
    }
    
    /**
     * JNI库诊断
     */
    private static void diagnoseJNILibraries() {
        log.info("--- JNI库诊断 ---");
        
        try {
            // 尝试加载原生库
            com.google.ortools.Loader.loadNativeLibraries();
            log.info("✅ 原生库加载成功");
            
            // 检查临时文件提取
            checkTempLibraryFiles();
            
        } catch (UnsatisfiedLinkError e) {
            log.error("❌ JNI库加载失败: {}", e.getMessage());
            log.error("详细错误: ", e);
        } catch (Exception e) {
            log.error("⚠️  原生库检查异常: {}", e.getMessage());
        }
    }
    
    /**
     * OR-Tools组件分步诊断
     */
    private static void diagnoseORToolsComponents() {
        log.info("--- OR-Tools组件分步诊断 ---");
        
        // 步骤1：尝试基本类实例化
        try {
            com.google.ortools.constraintsolver.RoutingIndexManager manager = 
                new com.google.ortools.constraintsolver.RoutingIndexManager(3, 1, 0);
            log.info("✅ RoutingIndexManager创建成功");
            
            // 步骤2：尝试RoutingModel创建
            com.google.ortools.constraintsolver.RoutingModel model = 
                new com.google.ortools.constraintsolver.RoutingModel(manager);
            log.info("✅ RoutingModel创建成功");
            
            // 步骤3：尝试基本求解
            com.google.ortools.constraintsolver.Assignment solution = model.solve();
            log.info("✅ 基本求解测试成功，解状态: {}", solution != null ? "有解" : "无解");
            
            // 步骤4：尝试距离回调注册
            testDistanceCallback(model, manager);
            
        } catch (NoClassDefFoundError e) {
            log.error("❌ 类定义找不到: {}", e.getMessage());
            log.error("可能的protobuf版本冲突");
        } catch (ExceptionInInitializerError e) {
            log.error("❌ 类初始化失败: {}", e.getCause().getMessage());
            log.error("详细错误: ", e.getCause());
        } catch (Exception e) {
            log.error("❌ OR-Tools组件测试失败: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            log.error("详细错误: ", e);
        }
    }
    
    /**
     * protobuf版本冲突诊断
     */
    private static void diagnoseProtobufConflicts() {
        log.info("--- protobuf版本冲突诊断 ---");
        
        try {
            // 检查protobuf版本
            String protobufVersion = com.google.protobuf.ProtocolMessageEnum.class.getPackage().getImplementationVersion();
            log.info("protobuf版本: {}", protobufVersion);
            
            // 检查可能冲突的protobuf方法
            checkProtobufMethods();
            
        } catch (Exception e) {
            log.error("protobuf版本检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查Visual C++ Redistributable
     */
    private static void checkVCRedistributable() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("windows")) {
            // 检查常见的VC++ Redistributable路径
            String[] vcPaths = {
                "C:\\Program Files\\Microsoft Visual Studio\\2022\\Redistributable",
                "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Redistributable",
                "C:\\Windows\\System32\\msvcp140.dll",
                "C:\\Windows\\System32\\vcruntime140.dll"
            };
            
            boolean vcFound = false;
            for (String path : vcPaths) {
                if (Files.exists(Paths.get(path))) {
                    log.info("✅ 发现VC++ Redistributable: {}", path);
                    vcFound = true;
                }
            }
            
            if (!vcFound) {
                log.warn("⚠️  未发现VC++ Redistributable，这可能导致JNI库加载失败");
            }
        }
    }
    
    /**
     * 检查临时库文件
     */
    private static void checkTempLibraryFiles() {
        String tmpDir = System.getProperty("java.io.tmpdir");
        log.info("检查临时目录: {}", tmpDir);
        
        try {
            Path tempPath = Paths.get(tmpDir);
            Files.list(tempPath)
                .filter(path -> path.getFileName().toString().contains("ortools") ||
                               path.getFileName().toString().contains("jniortools"))
                .forEach(path -> {
                    try {
                        long size = Files.size(path);
                        log.info("✅ 发现OR-Tools临时文件: {} ({}KB)", 
                            path.getFileName(), size / 1024);
                    } catch (Exception e) {
                        log.warn("⚠️  无法读取临时文件: {}", path);
                    }
                });
        } catch (Exception e) {
            log.error("检查临时文件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查protobuf相关类
     */
    private static void checkProtobufClasses() {
        String[] protobufClasses = {
            "com.google.protobuf.Message",
            "com.google.protobuf.LazyStringArrayList",
            "com.google.protobuf.GeneratedMessageV3"
        };
        
        for (String className : protobufClasses) {
            try {
                Class<?> clazz = Class.forName(className);
                log.info("✅ protobuf类存在: {}", className);
            } catch (ClassNotFoundException e) {
                log.error("❌ protobuf类不存在: {}", className);
            }
        }
    }
    
    /**
     * 检查protobuf方法
     */
    private static void checkProtobufMethods() {
        try {
            // 检查导致冲突的方法
            Class<?> lazyStringClass = Class.forName("com.google.protobuf.LazyStringArrayList");
            
            // 这个方法在某些版本中不存在或访问权限不同
            try {
                lazyStringClass.getMethod("emptyList");
                log.info("✅ LazyStringArrayList.emptyList() 方法存在");
            } catch (NoSuchMethodException e) {
                log.warn("⚠️  LazyStringArrayList.emptyList() 方法不存在");
            } catch (IllegalAccessError e) {
                log.error("❌ LazyStringArrayList.emptyList() 访问权限错误");
            }
            
        } catch (Exception e) {
            log.error("protobuf方法检查失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试距离回调
     */
    private static void testDistanceCallback(com.google.ortools.constraintsolver.RoutingModel model,
                                           com.google.ortools.constraintsolver.RoutingIndexManager manager) {
        try {
            // 简单的距离矩阵
            long[][] distanceMatrix = {
                {0, 10, 20},
                {10, 0, 15},
                {20, 15, 0}
            };
            
            int transitCallbackIndex = model.registerTransitCallback(
                (long fromIndex, long toIndex) -> {
                    int fromNode = manager.indexToNode(fromIndex);
                    int toNode = manager.indexToNode(toIndex);
                    return distanceMatrix[fromNode][toNode];
                }
            );
            
            model.setArcCostEvaluatorOfAllVehicles(transitCallbackIndex);
            log.info("✅ 距离回调注册成功");
            
        } catch (Exception e) {
            log.error("❌ 距离回调测试失败: {}", e.getMessage());
        }
    }
    
    /**
     * 主诊断方法
     */
    public static void main(String[] args) {
        performComprehensiveDiagnosis();
    }
}