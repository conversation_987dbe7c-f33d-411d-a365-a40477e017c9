package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 求解器参数配置
 * 
 * 封装MILP求解器的参数配置
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class SolverParameters {
    
    /**
     * 时间限制（秒）
     */
    @Builder.Default
    private double timeLimit = 300.0; // 默认5分钟
    
    /**
     * MIP间隙容忍度
     */
    @Builder.Default
    private double mipGapTolerance = 0.01; // 默认1%
    
    /**
     * 数值容忍度
     */
    @Builder.Default
    private double numericalTolerance = 1e-6;
    
    /**
     * 最大迭代次数
     */
    @Builder.Default
    private int maxIterations = 10000;
    
    /**
     * 求解器线程数
     */
    @Builder.Default
    private int threadCount = 1;
    
    /**
     * 是否启用预处理
     */
    @Builder.Default
    private boolean enablePresolve = true;
    
    /**
     * 是否启用切割平面
     */
    @Builder.Default
    private boolean enableCuttingPlanes = true;
    
    /**
     * 是否启用启发式算法
     */
    @Builder.Default
    private boolean enableHeuristics = true;
    
    /**
     * 求解器特定参数
     */
    @Builder.Default
    private Map<String, Object> solverSpecificParameters = null;
    
    /**
     * 输出详细程度
     */
    @Builder.Default
    private VerbosityLevel verbosity = VerbosityLevel.NORMAL;
    
    /**
     * 随机种子
     */
    @Builder.Default
    private Integer randomSeed = null;
    
    /**
     * 是否启用并行求解
     */
    @Builder.Default
    private boolean enableParallel = false;
    
    /**
     * 内存限制（MB）
     */
    @Builder.Default
    private int memoryLimit = 1024; // 默认1GB
    
    /**
     * 创建默认参数
     */
    public static SolverParameters createDefault() {
        return SolverParameters.builder().build();
    }
    
    /**
     * 创建快速求解参数
     */
    public static SolverParameters createFast() {
        return SolverParameters.builder()
            .timeLimit(60.0)  // 1分钟
            .mipGapTolerance(0.05)  // 5%间隙
            .enablePresolve(true)
            .enableHeuristics(true)
            .enableCuttingPlanes(false)  // 禁用切割平面加速
            .build();
    }
    
    /**
     * 创建高精度求解参数
     */
    public static SolverParameters createHighPrecision() {
        return SolverParameters.builder()
            .timeLimit(1800.0)  // 30分钟
            .mipGapTolerance(0.001)  // 0.1%间隙
            .numericalTolerance(1e-9)
            .maxIterations(50000)
            .enablePresolve(true)
            .enableCuttingPlanes(true)
            .enableHeuristics(true)
            .build();
    }
    
    /**
     * 输出详细程度枚举
     */
    public enum VerbosityLevel {
        SILENT(0, "静默"),
        MINIMAL(1, "最小输出"),
        NORMAL(2, "正常输出"),
        VERBOSE(3, "详细输出"),
        DEBUG(4, "调试输出");
        
        private final int level;
        private final String description;
        
        VerbosityLevel(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getDescription() {
            return description;
        }
    }
}