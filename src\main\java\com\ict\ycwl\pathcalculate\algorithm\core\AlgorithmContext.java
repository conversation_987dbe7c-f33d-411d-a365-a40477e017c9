package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.Data;

import java.util.*;

/**
 * 算法执行上下文
 * 用于在各个算法阶段之间传递数据和状态
 */
@Data
public class AlgorithmContext {
    
    /**
     * 算法开始时间
     */
    private long startTime;
    
    /**
     * 原始输入数据
     */
    private List<Accumulation> originalAccumulations;
    private List<TransitDepot> originalTransitDepots;
    private List<Team> originalTeams;
    private Map<String, TimeInfo> timeMatrix;
    
    /**
     * 预处理后的数据结构
     */
    // 中转站分组：中转站ID -> 其下所有聚集区
    private Map<Long, List<Accumulation>> depotGroups = new HashMap<>();
    
    // 班组分组：班组ID -> 其下所有中转站
    private Map<Long, List<TransitDepot>> teamGroups = new HashMap<>();
    
    // 快速查找索引
    private Map<Long, TransitDepot> transitDepotIndex = new HashMap<>();
    private Map<Long, Team> teamIndex = new HashMap<>();
    private Map<Long, Accumulation> accumulationIndex = new HashMap<>();
    
    /**
     * 聚类阶段数据
     */
    // 路线聚类：中转站ID -> 路线聚类列表
    private Map<Long, List<List<Accumulation>>> routeClusters = new HashMap<>();
    
    /**
     * TSP优化阶段数据
     */
    // 优化后路线：中转站ID -> 路线结果列表
    private Map<Long, List<RouteResult>> optimizedRoutes = new HashMap<>();
    
    /**
     * 统计信息
     */
    private Map<String, Object> statistics = new HashMap<>();
    
    /**
     * 算法参数
     */
    private AlgorithmParameters parameters = new AlgorithmParameters();
    
    /**
     * 调试相关数据
     */
    private String debugSessionId;
    private List<ConflictResolution> conflictResolutions = new ArrayList<>();
    private List<TimeBalanceAdjustment> timeBalanceAdjustments = new ArrayList<>();
    
    // ============ 便捷方法 ============
    
    /**
     * 根据ID获取中转站
     */
    public TransitDepot getTransitDepotById(Long transitDepotId) {
        return transitDepotIndex.get(transitDepotId);
    }
    
    /**
     * 根据ID获取班组
     */
    public Team getTeamById(Long teamId) {
        return teamIndex.get(teamId);
    }
    
    /**
     * 根据ID获取聚集区
     */
    public Accumulation getAccumulationById(Long accumulationId) {
        return accumulationIndex.get(accumulationId);
    }
    
    /**
     * 添加路线聚类
     */
    public void addRouteClusters(Long transitDepotId, List<List<Accumulation>> clusters) {
        routeClusters.put(transitDepotId, clusters);
    }
    
    /**
     * 添加优化后的路线
     */
    public void addOptimizedRoute(Long transitDepotId, RouteResult route) {
        optimizedRoutes.computeIfAbsent(transitDepotId, k -> new ArrayList<>()).add(route);
    }
    
    /**
     * 获取总路线数
     */
    public int getTotalRouteCount() {
        return routeClusters.values().stream()
                .mapToInt(List::size)
                .sum();
    }
    
    /**
     * 获取所有优化后的路线
     */
    public List<RouteResult> getAllOptimizedRoutes() {
        List<RouteResult> allRoutes = new ArrayList<>();
        optimizedRoutes.values().forEach(allRoutes::addAll);
        return allRoutes;
    }
    
    /**
     * 获取时间信息
     */
    public TimeInfo getTimeInfo(Double fromLng, Double fromLat, Double toLng, Double toLat) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", fromLng, fromLat, toLng, toLat);
        return timeMatrix.get(key);
    }
    
    /**
     * 获取中转站映射表（用于调试导出）
     */
    public Map<Long, TransitDepot> getTransitDepotMap() {
        return transitDepotIndex;
    }
    
    /**
     * 添加冲突解决记录
     */
    public void addConflictResolution(ConflictResolution resolution) {
        conflictResolutions.add(resolution);
    }
    
    /**
     * 添加时间均衡调整记录
     */
    public void addTimeBalanceAdjustment(TimeBalanceAdjustment adjustment) {
        timeBalanceAdjustments.add(adjustment);
    }
} 