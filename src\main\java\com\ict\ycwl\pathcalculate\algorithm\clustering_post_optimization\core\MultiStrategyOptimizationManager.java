package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.OptaPlannerConstraintOptimizer;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.optaplanner.domain.OptimizationParameters;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.JSPRITVRPOptimizer;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.AdvancedRouteCountEvaluator;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountEvaluation;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountRecommendation;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.UnifiedConstraintModel;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment.IntelligentRouteCountAdjuster;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment.RouteAdjustmentResult;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback.FallbackAlgorithmManager;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback.FallbackOptimizationResult;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback.FallbackStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多策略优化管理器
 * 
 * 根据约束违反模式选择最适合的第三方库和优化策略，
 * 协调OptaPlanner、JSPRIT、OR-Tools等高性能库的使用
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class MultiStrategyOptimizationManager {
    
    // PHASE1 模块依赖注入
    @Autowired
    private AdvancedRouteCountEvaluator routeCountEvaluator;
    
    @Autowired
    private UnifiedConstraintModel constraintModel;
    
    @Autowired
    private IntelligentRouteCountAdjuster routeAdjuster;
    
    @Autowired
    private FallbackAlgorithmManager fallbackManager;
    
    // PHASE2 模块依赖注入
    @Autowired
    private OptaPlannerConstraintOptimizer optaPlannerOptimizer;
    
    @Autowired
    private JSPRITVRPOptimizer jspritVRPOptimizer;
    
    /**
     * 选择最优策略
     * 
     * @param violationReport 约束违反报告
     * @return 推荐的优化策略
     */
    public OptimizationStrategy selectOptimalStrategy(ConstraintViolationReport violationReport) {
        
        log.debug("🎯 策略选择 - 450分钟违反: {}, 30分钟差异违反: {}, 地理违反: {}", 
            violationReport.getMaxTimeViolationCount(),
            violationReport.getTimeGapViolationCount(),
            violationReport.getGeographicViolationCount());
        
        // 使用报告中的推荐策略
        OptimizationStrategy recommended = violationReport.getRecommendedStrategy();
        
        log.debug("   📋 推荐策略: {}", recommended.getName());
        
        return recommended;
    }
    
    /**
     * 执行指定策略（集成PHASE1+PHASE2完整6阶段流程）
     * 
     * @param strategy 优化策略
     * @param clusters 原始聚类
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 优化后的聚类
     */
    public List<List<Accumulation>> executeStrategy(
        OptimizationStrategy strategy,
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        log.info("🚀 执行完整优化策略: {} - 中转站: {}, 原始路线数: {}", 
            strategy.getName(), depot.getTransitDepotName(), clusters.size());
        
        try {
            // 执行PHASE1+PHASE2完整流程
            return executeComprehensiveOptimization(strategy, clusters, depot, timeMatrix);
            
        } catch (Exception e) {
            log.error("❌ 策略执行失败: {}, 错误: {}", strategy.getName(), e.getMessage());
            // 发生异常时使用降级机制
            return executeFallbackOptimization(clusters, depot, timeMatrix, e);
        }
    }
    
    /**
     * 执行完整的6阶段优化流程
     * PHASE1: 评估→MILP→调整→降级 + PHASE2: OptaPlanner→JSPRIT
     */
    private List<List<Accumulation>> executeComprehensiveOptimization(
        OptimizationStrategy strategy,
        List<List<Accumulation>> originalClusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        log.info("📊 开始6阶段完整优化流程");
        List<List<Accumulation>> currentClusters = deepCopyOfClusters(originalClusters);
        
        // ========== PHASE1: 基础分析和调整层 ==========
        
        // PHASE1-001: 路线数量评估
        log.info("   🔍 PHASE1-001: 执行4维路线数量评估");
        RouteCountEvaluation evaluation = routeCountEvaluator.evaluateDepotRouteCount(
            depot, currentClusters, timeMatrix);
        
        RouteCountRecommendation recommendation = evaluation.getRecommendation();
        log.info("   ✅ 评估完成: {} (置信度: {:.1f}%)", 
            recommendation.getRecommendedAction().getDescription(), 
            recommendation.getConfidence() * 100);
        
        // PHASE1-003: 智能路线调整（如果评估建议需要调整）
        if (recommendation.getRouteCountAdjustment() != 0) {
            log.info("   🔧 PHASE1-003: 执行智能路线调整 (调整量: {:+d})", 
                recommendation.getRouteCountAdjustment());
            
            RouteAdjustmentResult adjustmentResult = routeAdjuster.adjustRouteCount(
                currentClusters, recommendation, depot, timeMatrix);
            
            if (adjustmentResult.isSuccess()) {
                currentClusters = adjustmentResult.getAdjustedRoutes();
                log.info("   ✅ 路线调整完成: {} → {}条路线", 
                    originalClusters.size(), currentClusters.size());
            } else {
                log.warn("   ⚠️ 路线调整失败，保持原始聚类: {}", 
                    adjustmentResult.getMessage());
            }
        } else {
            log.info("   ℹ️ 评估建议无需调整路线数量，跳过PHASE1-003");
        }
        
        // ========== PHASE2: 高级优化层 ==========
        
        // 根据策略执行相应的PHASE2优化
        return executePhase2Optimization(strategy, currentClusters, depot, timeMatrix, evaluation);
    }
    
    /**
     * 执行PHASE2优化（基于PHASE1的结果）
     */
    private List<List<Accumulation>> executePhase2Optimization(
        OptimizationStrategy strategy,
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        RouteCountEvaluation phase1Evaluation
    ) {
        
        log.info("   📊 开始PHASE2优化: {}", strategy.getName());
        
        try {
            switch (strategy) {
                case OPTAPLANNER_CONSTRAINTS:
                    return executeOptaPlannerStrategy(clusters, depot, timeMatrix);
                    
                case JSPRIT_LOAD_BALANCE:
                    return executeJSPRITStrategy(clusters, depot, timeMatrix);
                    
                case ORTOOLS_GEOMETRIC:
                    return executeORToolsStrategy(clusters, depot, timeMatrix);
                    
                case HYBRID_OPTAPLANNER_JSPRIT:
                    return executeHybridStrategy(clusters, depot, timeMatrix);
                    
                case SIMPLE_HEURISTIC:
                    return executeHeuristicStrategy(clusters, depot, timeMatrix);
                    
                default:
                    log.warn("⚠️ 未知PHASE2策略，使用JSPRIT负载均衡: {}", strategy);
                    return executeJSPRITStrategy(clusters, depot, timeMatrix);
            }
            
        } catch (Exception e) {
            log.error("❌ PHASE2优化失败，启动PHASE1-004降级机制: {}", e.getMessage());
            return executeFallbackOptimization(clusters, depot, timeMatrix, e);
        }
    }
    
    /**
     * 执行PHASE1-004降级优化
     */
    private List<List<Accumulation>> executeFallbackOptimization(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        Exception originalException
    ) {
        
        log.info("   🛡️ PHASE1-004: 执行降级算法管理");
        
        try {
            // 使用降级算法管理器（使用模拟退火作为默认策略）
            FallbackOptimizationResult fallbackResult = fallbackManager.executeFallbackOptimization(
                clusters, depot, timeMatrix, FallbackStrategy.SIMULATED_ANNEALING);
            
            if (fallbackResult.isSuccess() && fallbackResult.getOptimizedRoutes() != null) {
                List<List<Accumulation>> optimizedRoutes = fallbackResult.getOptimizedRoutes();
                log.info("   ✅ 降级算法优化成功，路线数: {} → {}", 
                    clusters.size(), optimizedRoutes.size());
                return optimizedRoutes;
            } else {
                log.warn("   ⚠️ 降级算法也失败，返回原始聚类: {}", fallbackResult.getMessage());
                return deepCopyOfClusters(clusters);
            }
            
        } catch (Exception fallbackException) {
            log.error("   ❌ 降级算法异常，返回原始聚类: {}", fallbackException.getMessage());
            return deepCopyOfClusters(clusters);
        }
    }
    
    /**
     * 执行OptaPlanner约束求解策略
     */
    private List<List<Accumulation>> executeOptaPlannerStrategy(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("   🔧 执行OptaPlanner约束求解...");
        
        try {
            // 使用快速优化参数（适合聚类二次优化的时间要求）
            List<List<Accumulation>> optimizedClusters = optaPlannerOptimizer.quickOptimize(
                depot, clusters, timeMatrix);
            
            // 验证优化结果
            if (optimizedClusters != null && !optimizedClusters.isEmpty()) {
                log.debug("   ✅ OptaPlanner优化成功，聚类数: {} → {}", 
                    clusters.size(), optimizedClusters.size());
                return optimizedClusters;
            } else {
                log.warn("   ⚠️ OptaPlanner优化结果为空，使用降级策略");
                return balanceClusterWorkloads(clusters, depot, timeMatrix);
            }
            
        } catch (Exception e) {
            log.error("   ❌ OptaPlanner优化异常，使用降级策略: {}", e.getMessage());
            return balanceClusterWorkloads(clusters, depot, timeMatrix);
        }
    }
    
    /**
     * 执行JSPRIT负载均衡策略
     */
    private List<List<Accumulation>> executeJSPRITStrategy(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("   🔧 执行JSPRIT VRP负载均衡...");
        
        try {
            // 使用JSPRIT快速优化进行VRP负载均衡
            List<List<Accumulation>> jspritResult = jspritVRPOptimizer.quickOptimize(
                depot, clusters, timeMatrix);
            
            // 验证JSPRIT优化结果
            if (jspritResult != null && !jspritResult.isEmpty()) {
                log.debug("   ✅ JSPRIT VRP优化成功，路线数: {} → {}", 
                    clusters.size(), jspritResult.size());
                return jspritResult;
            } else {
                log.warn("   ⚠️ JSPRIT VRP优化结果为空，使用降级策略");
                return balanceClusterWorkloads(clusters, depot, timeMatrix);
            }
            
        } catch (Exception e) {
            log.error("   ❌ JSPRIT VRP优化异常，使用降级策略: {}", e.getMessage());
            return balanceClusterWorkloads(clusters, depot, timeMatrix);
        }
    }
    
    /**
     * 执行OR-Tools几何优化策略
     */
    private List<List<Accumulation>> executeORToolsStrategy(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("   🔧 执行OR-Tools几何优化...");
        
        // TODO: 实现OR-Tools集成
        // 这里是占位实现
        
        return balanceClusterWorkloads(clusters, depot, timeMatrix);
    }
    
    /**
     * 执行混合策略（OptaPlanner + JSPRIT）
     */
    private List<List<Accumulation>> executeHybridStrategy(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("   🔧 执行混合优化策略（OptaPlanner + JSPRIT）...");
        
        try {
            // 第1阶段：OptaPlanner约束求解（处理硬约束）
            log.debug("   📊 第1阶段：OptaPlanner约束优化");
            List<List<Accumulation>> optaplannerResult = executeOptaPlannerStrategy(clusters, depot, timeMatrix);
            
            // 验证第1阶段结果
            if (optaplannerResult == null || optaplannerResult.isEmpty()) {
                log.warn("   ⚠️ OptaPlanner阶段失败，跳过JSPRIT阶段");
                return balanceClusterWorkloads(clusters, depot, timeMatrix);
            }
            
            // 第2阶段：JSPRIT VRP负载均衡（精细化优化）
            log.debug("   📊 第2阶段：JSPRIT VRP负载均衡优化");
            List<List<Accumulation>> jspritResult = jspritVRPOptimizer.quickOptimize(depot, optaplannerResult, timeMatrix);
            
            // 验证第2阶段结果
            if (jspritResult != null && !jspritResult.isEmpty()) {
                log.debug("   ✅ 混合策略完成：OptaPlanner + JSPRIT");
                return jspritResult;
            } else {
                log.warn("   ⚠️ JSPRIT阶段失败，返回OptaPlanner结果");
                return optaplannerResult;
            }
            
        } catch (Exception e) {
            log.error("   ❌ 混合策略异常，使用降级策略: {}", e.getMessage());
            return balanceClusterWorkloads(clusters, depot, timeMatrix);
        }
    }
    
    /**
     * 执行简单启发式策略
     */
    private List<List<Accumulation>> executeHeuristicStrategy(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("   🔧 执行简单启发式策略...");
        
        return balanceClusterWorkloads(clusters, depot, timeMatrix);
    }
    
    /**
     * 简单的负载均衡实现（占位算法）
     */
    private List<List<Accumulation>> balanceClusterWorkloads(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        
        log.debug("   🔧 开始智能负载均衡优化...");
        List<List<Accumulation>> result = deepCopyOfClusters(clusters);
        
        // 增强的多轮优化策略
        int maxIterations = Math.max(20, clusters.size() * 3);
        boolean hasImprovement = true;
        int consecutiveNoImprovement = 0;
        
        for (int iteration = 0; iteration < maxIterations && hasImprovement; iteration++) {
            boolean iterationImproved = false;
            
            // 1. 处理严重超时的聚类（>450分钟）
            iterationImproved |= handleSevereTimeViolations(result, depot, timeMatrix);
            
            // 2. 智能负载均衡
            iterationImproved |= performIntelligentLoadBalancing(result, depot, timeMatrix);
            
            // 3. 微调优化
            if (iteration % 5 == 4) {
                iterationImproved |= performFinetuneOptimization(result, depot, timeMatrix);
            }
            
            if (!iterationImproved) {
                consecutiveNoImprovement++;
            } else {
                consecutiveNoImprovement = 0;
            }
            
            // 连续3轮无改进则停止
            if (consecutiveNoImprovement >= 3) {
                hasImprovement = false;
            }
            
            log.debug("   📊 第{}轮优化 - 改进: {}", iteration + 1, iterationImproved);
        }
        
        log.debug("   ✅ 负载均衡优化完成，共{}轮", maxIterations);
        return result;
    }
    
    /**
     * 处理严重的时间约束违反（>450分钟）
     */
    private boolean handleSevereTimeViolations(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        boolean improved = false;
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            double workTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
            
            if (workTime > 450.0 && cluster.size() > 1) {
                // 找到最适合移动的聚集区
                Accumulation bestToMove = findBestAccumulationToMove(cluster, depot, timeMatrix);
                int bestTargetIndex = findBestTargetCluster(clusters, bestToMove, depot, timeMatrix, i);
                
                if (bestTargetIndex != -1) {
                    clusters.get(i).remove(bestToMove);
                    clusters.get(bestTargetIndex).add(bestToMove);
                    improved = true;
                    
                    double newWorkTime = calculateClusterWorkTime(clusters.get(i), depot, timeMatrix);
                    log.debug("   🔥 移动聚集区{}解决严重超时: {:.1f}→{:.1f}分钟", 
                        bestToMove.getAccumulationId(), workTime, newWorkTime);
                }
            }
        }
        
        return improved;
    }
    
    /**
     * 智能负载均衡
     */
    private boolean performIntelligentLoadBalancing(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        boolean improved = false;
        
        // 计算方差，找到最不均衡的聚类对
        List<Double> workTimes = clusters.stream()
            .map(cluster -> calculateClusterWorkTime(cluster, depot, timeMatrix))
            .collect(Collectors.toList());
        
        double variance = calculateVariance(workTimes);
        if (variance < 100) return false; // 已经足够均衡
        
        // 找到最重和最轻的聚类
        int heaviestIndex = workTimes.indexOf(Collections.max(workTimes));
        int lightestIndex = workTimes.indexOf(Collections.min(workTimes));
        
        if (heaviestIndex != lightestIndex && clusters.get(heaviestIndex).size() > 1) {
            List<Accumulation> heaviest = clusters.get(heaviestIndex);
            
            // 智能选择最优移动的聚集区
            Accumulation bestToMove = findOptimalMoveCandidate(
                heaviest, clusters.get(lightestIndex), depot, timeMatrix);
            
            if (bestToMove != null) {
                double beforeHeaviest = workTimes.get(heaviestIndex);
                double beforeLightest = workTimes.get(lightestIndex);
                
                // 执行移动
                heaviest.remove(bestToMove);
                clusters.get(lightestIndex).add(bestToMove);
                
                double afterHeaviest = calculateClusterWorkTime(heaviest, depot, timeMatrix);
                double afterLightest = calculateClusterWorkTime(clusters.get(lightestIndex), depot, timeMatrix);
                
                // 检查移动是否有效
                if (afterLightest <= 450.0 && (afterHeaviest + afterLightest) < (beforeHeaviest + beforeLightest)) {
                    improved = true;
                    log.debug("   ⚖️ 负载均衡移动: 聚类{} {:.1f}→{:.1f}, 聚类{} {:.1f}→{:.1f}", 
                        heaviestIndex, beforeHeaviest, afterHeaviest,
                        lightestIndex, beforeLightest, afterLightest);
                } else {
                    // 回退无效移动
                    clusters.get(lightestIndex).remove(bestToMove);
                    heaviest.add(bestToMove);
                }
            }
        }
        
        return improved;
    }
    
    /**
     * 微调优化
     */
    private boolean performFinetuneOptimization(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        boolean improved = false;
        
        // 尝试小规模的跨聚类交换来减少总体时间差异
        for (int i = 0; i < clusters.size(); i++) {
            for (int j = i + 1; j < clusters.size(); j++) {
                if (clusters.get(i).isEmpty() || clusters.get(j).isEmpty()) continue;
                
                // 尝试交换最合适的聚集区
                improved |= tryOptimalSwap(clusters, i, j, depot, timeMatrix);
            }
        }
        
        return improved;
    }
    
    /**
     * 找到最适合移动的聚集区
     */
    private Accumulation findBestAccumulationToMove(
        List<Accumulation> cluster,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        Accumulation best = null;
        double maxTimeReduction = 0;
        double originalTime = calculateClusterWorkTime(cluster, depot, timeMatrix);
        
        for (Accumulation acc : cluster) {
            List<Accumulation> temp = new ArrayList<>(cluster);
            temp.remove(acc);
            double newTime = calculateClusterWorkTime(temp, depot, timeMatrix);
            double reduction = originalTime - newTime;
            
            if (reduction > maxTimeReduction) {
                maxTimeReduction = reduction;
                best = acc;
            }
        }
        
        return best;
    }
    
    /**
     * 找到最佳目标聚类
     */
    private int findBestTargetCluster(
        List<List<Accumulation>> clusters,
        Accumulation toMove,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        int excludeIndex
    ) {
        int bestIndex = -1;
        double minIncrease = Double.MAX_VALUE;
        
        for (int i = 0; i < clusters.size(); i++) {
            if (i == excludeIndex) continue;
            
            List<Accumulation> target = clusters.get(i);
            double originalTime = calculateClusterWorkTime(target, depot, timeMatrix);
            
            List<Accumulation> temp = new ArrayList<>(target);
            temp.add(toMove);
            double newTime = calculateClusterWorkTime(temp, depot, timeMatrix);
            
            if (newTime <= 450.0 && (newTime - originalTime) < minIncrease) {
                minIncrease = newTime - originalTime;
                bestIndex = i;
            }
        }
        
        return bestIndex;
    }
    
    /**
     * 找到最优移动候选
     */
    private Accumulation findOptimalMoveCandidate(
        List<Accumulation> source,
        List<Accumulation> target,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        Accumulation best = null;
        double bestScore = Double.MIN_VALUE;
        
        double originalSourceTime = calculateClusterWorkTime(source, depot, timeMatrix);
        double originalTargetTime = calculateClusterWorkTime(target, depot, timeMatrix);
        
        for (Accumulation acc : source) {
            // 计算移动后的时间变化
            List<Accumulation> tempSource = new ArrayList<>(source);
            tempSource.remove(acc);
            List<Accumulation> tempTarget = new ArrayList<>(target);
            tempTarget.add(acc);
            
            double newSourceTime = calculateClusterWorkTime(tempSource, depot, timeMatrix);
            double newTargetTime = calculateClusterWorkTime(tempTarget, depot, timeMatrix);
            
            if (newTargetTime <= 450.0) {
                // 评分：减少的总时间差异
                double timeDiffBefore = Math.abs(originalSourceTime - originalTargetTime);
                double timeDiffAfter = Math.abs(newSourceTime - newTargetTime);
                double score = timeDiffBefore - timeDiffAfter;
                
                if (score > bestScore) {
                    bestScore = score;
                    best = acc;
                }
            }
        }
        
        return best;
    }
    
    /**
     * 尝试最优交换
     */
    private boolean tryOptimalSwap(
        List<List<Accumulation>> clusters,
        int index1,
        int index2,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        List<Accumulation> cluster1 = clusters.get(index1);
        List<Accumulation> cluster2 = clusters.get(index2);
        
        double time1Before = calculateClusterWorkTime(cluster1, depot, timeMatrix);
        double time2Before = calculateClusterWorkTime(cluster2, depot, timeMatrix);
        double totalBefore = time1Before + time2Before;
        
        // 寻找最佳交换对
        Accumulation bestFrom1 = null, bestFrom2 = null;
        double bestImprovement = 0;
        
        for (Accumulation acc1 : cluster1) {
            for (Accumulation acc2 : cluster2) {
                // 模拟交换
                List<Accumulation> temp1 = new ArrayList<>(cluster1);
                List<Accumulation> temp2 = new ArrayList<>(cluster2);
                
                temp1.remove(acc1);
                temp1.add(acc2);
                temp2.remove(acc2);
                temp2.add(acc1);
                
                double time1After = calculateClusterWorkTime(temp1, depot, timeMatrix);
                double time2After = calculateClusterWorkTime(temp2, depot, timeMatrix);
                
                if (time1After <= 450.0 && time2After <= 450.0) {
                    double totalAfter = time1After + time2After;
                    double improvement = totalBefore - totalAfter;
                    
                    if (improvement > bestImprovement) {
                        bestImprovement = improvement;
                        bestFrom1 = acc1;
                        bestFrom2 = acc2;
                    }
                }
            }
        }
        
        // 执行最佳交换
        if (bestFrom1 != null && bestFrom2 != null) {
            cluster1.remove(bestFrom1);
            cluster1.add(bestFrom2);
            cluster2.remove(bestFrom2);
            cluster2.add(bestFrom1);
            
            log.debug("   🔄 优化交换: 聚集区{} ↔ 聚集区{}, 改进{:.1f}分钟", 
                bestFrom1.getAccumulationId(), bestFrom2.getAccumulationId(), bestImprovement);
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算方差
     */
    private double calculateVariance(List<Double> values) {
        if (values.size() <= 1) return 0.0;
        
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double sumSquaredDiffs = values.stream()
            .mapToDouble(value -> Math.pow(value - mean, 2))
            .sum();
        
        return sumSquaredDiffs / values.size();
    }
    
    /**
     * 找到工作时间最重的聚类
     */
    private int findHeaviestCluster(List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        int heaviestIndex = 0;
        double maxTime = 0.0;
        
        for (int i = 0; i < clusters.size(); i++) {
            double time = calculateClusterWorkTime(clusters.get(i), depot, timeMatrix);
            if (time > maxTime) {
                maxTime = time;
                heaviestIndex = i;
            }
        }
        
        return heaviestIndex;
    }
    
    /**
     * 找到工作时间最轻的聚类
     */
    private int findLightestCluster(List<List<Accumulation>> clusters, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        int lightestIndex = 0;
        double minTime = Double.MAX_VALUE;
        
        for (int i = 0; i < clusters.size(); i++) {
            double time = calculateClusterWorkTime(clusters.get(i), depot, timeMatrix);
            if (time < minTime) {
                minTime = time;
                lightestIndex = i;
            }
        }
        
        return lightestIndex;
    }
    
    /**
     * 计算聚类工作时间
     */
    private double calculateClusterWorkTime(List<Accumulation> cluster, TransitDepot depot, Map<String, TimeInfo> timeMatrix) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        // 简化计算：配送时间 + 往返时间
        double totalTime = cluster.stream()
            .mapToDouble(acc -> acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 0.0)
            .sum();
        
        // 加上往返中转站的时间
        for (Accumulation acc : cluster) {
            String key = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返
            }
        }
        
        return totalTime;
    }
    
    /**
     * 深拷贝聚类
     */
    private List<List<Accumulation>> deepCopyOfClusters(List<List<Accumulation>> clusters) {
        return clusters.stream()
            .map(cluster -> cluster.stream()
                .map(Accumulation::copy)
                .collect(Collectors.toList()))
            .collect(Collectors.toList());
    }
}