package com.ict.ycwl.pathcalculate.algorithm.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 时间评估结果
 * 
 * 包含路线时间评估的完整信息：
 * - 时间分解：配送时间、行驶时间、中转站往返时间
 * - 评估指标：效率分数、时间利用率等
 * - 决策支持：是否超限、是否接近上限等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
public class TimeEvaluationResult {
    
    // ===================== 时间分解 =====================
    
    /**
     * 总预估时间(小时)
     * 包含所有时间成本的总和
     */
    private final double totalTimeHours;
    
    /**
     * 配送服务时间(小时)
     * 来自各个点的deliveryTime之和
     */
    private final double serviceTimeHours;
    
    /**
     * 行驶时间(小时)
     * 包含地块内行驶 + 地块间连接行驶
     */
    private final double travelTimeHours;
    
    /**
     * 中转站往返时间(小时)
     * 从中转站到路线的往返时间
     */
    private final double depotTimeHours;
    
    // ===================== 评估指标 =====================
    
    /**
     * 效率分数(0-100)
     * 基于点数与时间的比值计算
     */
    private final double efficiencyScore;
    
    /**
     * 时间利用率(0-1)
     * 相对于最大工作时间的利用程度
     */
    private final double timeUtilizationRatio;
    
    /**
     * 地理紧凑度(0-1)
     * 反映路线的地理分布紧凑程度
     */
    private final double geographicCompactness;
    
    // ===================== 决策标志 =====================
    
    /**
     * 是否超过时间限制
     */
    private final boolean exceedsTimeLimit;
    
    /**
     * 是否接近时间限制
     */
    private final boolean approachesTimeLimit;
    
    /**
     * 是否在最优时间范围内
     */
    private final boolean inOptimalRange;
    
    // ===================== 详细信息 =====================
    
    /**
     * 评估详情说明
     */
    private final String evaluationDetails;
    
    /**
     * 额外的评估指标
     */
    @Builder.Default
    private final Map<String, Object> additionalMetrics = new HashMap<>();
    
    // ===================== 工厂方法 =====================
    
    /**
     * 创建基础时间评估结果
     */
    public static TimeEvaluationResult createBasic(
            double totalTimeHours,
            double serviceTimeHours,
            double travelTimeHours,
            double depotTimeHours,
            TimeEvaluationConfig config) {
        
        double timeUtilization = totalTimeHours / config.getMaxWorkTimeHours();
        boolean exceedsLimit = totalTimeHours > config.getFlexibilityTimeThreshold();
        boolean approachesLimit = timeUtilization > config.getCautionThreshold();
        boolean inOptimal = timeUtilization <= config.getOptimalThreshold();
        
        double efficiencyScore = calculateEfficiencyScore(totalTimeHours, serviceTimeHours);
        double compactness = calculateCompactness(travelTimeHours, totalTimeHours);
        
        String details = String.format(
            "总时间%.1fh (配送%.1fh + 行驶%.1fh + 往返%.1fh), 时间利用率%.1f%%",
            totalTimeHours, serviceTimeHours, travelTimeHours, depotTimeHours,
            timeUtilization * 100
        );
        
        return TimeEvaluationResult.builder()
            .totalTimeHours(totalTimeHours)
            .serviceTimeHours(serviceTimeHours)
            .travelTimeHours(travelTimeHours)
            .depotTimeHours(depotTimeHours)
            .efficiencyScore(efficiencyScore)
            .timeUtilizationRatio(timeUtilization)
            .geographicCompactness(compactness)
            .exceedsTimeLimit(exceedsLimit)
            .approachesTimeLimit(approachesLimit)
            .inOptimalRange(inOptimal)
            .evaluationDetails(details)
            .build();
    }
    
    /**
     * 计算效率分数
     * 基于配送时间占总时间的比例
     */
    private static double calculateEfficiencyScore(double totalTime, double serviceTime) {
        if (totalTime <= 0) return 0.0;
        
        double serviceRatio = serviceTime / totalTime;
        // 配送时间占比越高，效率越好
        return Math.min(100.0, serviceRatio * 120.0); // 最高100分
    }
    
    /**
     * 计算地理紧凑度
     * 基于行驶时间占总时间的比例（越小越紧凑）
     */
    private static double calculateCompactness(double travelTime, double totalTime) {
        if (totalTime <= 0) return 1.0;
        
        double travelRatio = travelTime / totalTime;
        // 行驶时间占比越小，紧凑度越高
        return Math.max(0.0, 1.0 - travelRatio * 2.0);
    }
    
    // ===================== 便捷方法 =====================
    
    /**
     * 获取总时间(分钟)
     */
    public double getTotalTimeMinutes() {
        return totalTimeHours * 60.0;
    }
    
    /**
     * 获取配送时间(分钟)
     */
    public double getServiceTimeMinutes() {
        return serviceTimeHours * 60.0;
    }
    
    /**
     * 获取行驶时间(分钟)
     */
    public double getTravelTimeMinutes() {
        return travelTimeHours * 60.0;
    }
    
    /**
     * 是否建议停止合并
     */
    public boolean shouldStopMerging() {
        return exceedsTimeLimit || (approachesTimeLimit && efficiencyScore < 60.0);
    }
    
    /**
     * 获取时间利用率百分比
     */
    public double getTimeUtilizationPercentage() {
        return timeUtilizationRatio * 100.0;
    }
    
    /**
     * 添加额外指标
     */
    public void addMetric(String key, Object value) {
        additionalMetrics.put(key, value);
    }
    
    /**
     * 获取格式化的评估报告
     */
    public String getFormattedReport() {
        StringBuilder report = new StringBuilder();
        report.append("📊 时间评估报告:\n");
        report.append(String.format("   ⏰ 总时间: %.1f小时 (%.0f分钟)\n", totalTimeHours, getTotalTimeMinutes()));
        report.append(String.format("   📦 配送时间: %.1f小时 (%.1f%%)\n", 
            serviceTimeHours, (serviceTimeHours/totalTimeHours)*100));
        report.append(String.format("   🚗 行驶时间: %.1f小时 (%.1f%%)\n", 
            travelTimeHours, (travelTimeHours/totalTimeHours)*100));
        report.append(String.format("   🏪 往返时间: %.1f小时 (%.1f%%)\n", 
            depotTimeHours, (depotTimeHours/totalTimeHours)*100));
        report.append(String.format("   📈 效率分数: %.1f/100\n", efficiencyScore));
        report.append(String.format("   🎯 时间利用率: %.1f%%\n", getTimeUtilizationPercentage()));
        report.append(String.format("   📍 地理紧凑度: %.1f%%\n", geographicCompactness * 100));
        
        String status;
        if (exceedsTimeLimit) {
            status = "❌ 超过时间限制";
        } else if (approachesTimeLimit) {
            status = "⚠️ 接近时间限制";
        } else if (inOptimalRange) {
            status = "✅ 最优时间范围";
        } else {
            status = "🔄 可继续优化";
        }
        report.append(String.format("   🏆 评估状态: %s\n", status));
        
        return report.toString();
    }
}