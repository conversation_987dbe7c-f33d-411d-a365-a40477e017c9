package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class SystemParameter {
    @TableId
    private int id;
    //聚集区密集度系数
    private double accumulationIntensity;
    //商铺平均卸货时长(分钟)城区
    private double shoreUnloadCityTime;
    //商铺平均卸货时长(分钟)乡村
    private double shoreUnloadTownshipTime;
    //车辆时速(千米每时)-高速公路
    private double freeway;
    //车辆时速(千米每时)-城区公路
    private double urbanRoads;
    //车辆时速(千米每时)-乡镇公路
    private double townshipRoads;
    //装车时长分钟
    private double loadingTime;
}
