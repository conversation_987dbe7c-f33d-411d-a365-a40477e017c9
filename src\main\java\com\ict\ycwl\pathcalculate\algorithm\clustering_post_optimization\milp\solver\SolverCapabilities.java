package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * 求解器能力描述
 * 
 * 描述MILP求解器支持的功能和限制
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class SolverCapabilities {
    
    /**
     * 支持的变量类型
     */
    private Set<MILPProblem.VariableType> supportedVariableTypes;
    
    /**
     * 支持的约束类型
     */
    private Set<String> supportedConstraintTypes;
    
    /**
     * 支持的目标函数类型
     */
    private Set<MILPProblem.ObjectiveType> supportedObjectiveTypes;
    
    /**
     * 最大变量数限制
     */
    private Integer maxVariables;
    
    /**
     * 最大约束数限制
     */
    private Integer maxConstraints;
    
    /**
     * 是否支持整数规划
     */
    private boolean supportsIntegerProgramming;
    
    /**
     * 是否支持二次规划
     */
    private boolean supportsQuadraticProgramming;
    
    /**
     * 是否支持随机规划
     */
    private boolean supportsStochasticProgramming;
    
    /**
     * 是否支持多目标优化
     */
    private boolean supportsMultiObjective;
    
    /**
     * 是否支持并行求解
     */
    private boolean supportsParallel;
    
    /**
     * 是否支持增量求解
     */
    private boolean supportsIncrementalSolving;
    
    /**
     * 是否支持敏感性分析
     */
    private boolean supportsSensitivityAnalysis;
    
    /**
     * 是否支持对偶解
     */
    private boolean supportsDualSolution;
    
    /**
     * 求解器等级
     */
    private SolverTier tier;
    
    /**
     * 许可证类型
     */
    private LicenseType licenseType;
    
    /**
     * 求解器描述
     */
    private String description;
    
    /**
     * 检查是否支持指定问题
     */
    public boolean canSolve(MILPProblem problem) {
        // 检查变量类型支持
        for (MILPProblem.VariableType varType : problem.getVariables().values().stream()
            .map(var -> var.getType()).toArray(MILPProblem.VariableType[]::new)) {
            if (!supportedVariableTypes.contains(varType)) {
                return false;
            }
        }
        
        // 检查目标函数类型支持
        if (problem.getObjectiveFunction() != null) {
            if (!supportedObjectiveTypes.contains(problem.getObjectiveFunction().getType())) {
                return false;
            }
        }
        
        // 检查规模限制
        if (maxVariables != null && problem.getVariables().size() > maxVariables) {
            return false;
        }
        
        if (maxConstraints != null && problem.getConstraints().size() > maxConstraints) {
            return false;
        }
        
        // 检查是否需要整数规划支持
        boolean needsIntegerSolving = problem.getVariables().values().stream()
            .anyMatch(var -> var.getType() == MILPProblem.VariableType.INTEGER || 
                           var.getType() == MILPProblem.VariableType.BINARY);
        
        if (needsIntegerSolving && !supportsIntegerProgramming) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取能力评分（0-100）
     */
    public int getCapabilityScore() {
        int score = 0;
        
        // 基础能力
        score += supportedVariableTypes.size() * 10;
        score += supportedObjectiveTypes.size() * 10;
        
        // 高级功能
        if (supportsIntegerProgramming) score += 20;
        if (supportsQuadraticProgramming) score += 15;
        if (supportsMultiObjective) score += 10;
        if (supportsParallel) score += 10;
        if (supportsSensitivityAnalysis) score += 5;
        if (supportsDualSolution) score += 5;
        
        // 求解器等级加分
        switch (tier) {
            case COMMERCIAL:
                score += 15;
                break;
            case OPEN_SOURCE_ADVANCED:
                score += 10;
                break;
            case OPEN_SOURCE_BASIC:
                score += 5;
                break;
            case ACADEMIC:
                score += 8;
                break;
        }
        
        return Math.min(100, score);
    }
    
    /**
     * 生成能力摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("求解器能力: %s (%s)\n", description, tier.getDescription()));
        summary.append(String.format("变量类型: %s\n", supportedVariableTypes));
        summary.append(String.format("目标函数: %s\n", supportedObjectiveTypes));
        summary.append(String.format("整数规划: %s\n", supportsIntegerProgramming ? "支持" : "不支持"));
        summary.append(String.format("并行求解: %s\n", supportsParallel ? "支持" : "不支持"));
        summary.append(String.format("能力评分: %d/100\n", getCapabilityScore()));
        
        if (maxVariables != null) {
            summary.append(String.format("最大变量数: %d\n", maxVariables));
        }
        if (maxConstraints != null) {
            summary.append(String.format("最大约束数: %d\n", maxConstraints));
        }
        
        return summary.toString();
    }
    
    /**
     * 求解器等级枚举
     */
    public enum SolverTier {
        COMMERCIAL("商业求解器", "高性能商业级求解器"),
        OPEN_SOURCE_ADVANCED("高级开源", "功能完整的开源求解器"),
        OPEN_SOURCE_BASIC("基础开源", "基础功能开源求解器"),
        ACADEMIC("学术版本", "学术研究用求解器"),
        BUILTIN("内置算法", "内置简单算法");
        
        private final String name;
        private final String description;
        
        SolverTier(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 许可证类型枚举
     */
    public enum LicenseType {
        COMMERCIAL("商业许可", "需要购买商业许可证"),
        GPL("GPL许可", "GNU通用公共许可证"),
        MIT("MIT许可", "MIT许可证"),
        APACHE("Apache许可", "Apache许可证"),
        ACADEMIC("学术许可", "仅限学术使用"),
        FREE("免费使用", "免费使用许可");
        
        private final String name;
        private final String description;
        
        LicenseType(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
}