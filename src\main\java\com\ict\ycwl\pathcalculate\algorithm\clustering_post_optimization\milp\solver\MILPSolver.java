package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;
import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPSolution;

/**
 * MILP求解器接口
 * 
 * 定义混合整数线性规划求解器的统一接口
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
public interface MILPSolver {
    
    /**
     * 求解MILP问题
     * 
     * @param problem 待求解的MILP问题
     * @return 求解结果
     */
    MILPSolution solve(MILPProblem problem);
    
    /**
     * 设置求解器参数
     * 
     * @param parameters 求解器参数
     */
    void setParameters(SolverParameters parameters);
    
    /**
     * 获取求解器名称
     * 
     * @return 求解器名称
     */
    String getSolverName();
    
    /**
     * 获取求解器版本
     * 
     * @return 求解器版本
     */
    String getSolverVersion();
    
    /**
     * 检查求解器是否可用
     * 
     * @return 求解器可用性
     */
    boolean isAvailable();
    
    /**
     * 检查是否支持指定问题类型
     * 
     * @param problem MILP问题
     * @return 是否支持
     */
    boolean supports(MILPProblem problem);
    
    /**
     * 获取求解器能力描述
     * 
     * @return 求解器能力
     */
    SolverCapabilities getCapabilities();
    
    /**
     * 停止求解（如果正在求解）
     */
    default void stop() {
        // 默认实现：不支持停止操作
    }
}