package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.DoubleSummaryStatistics;

/**
 * VRP问题统计信息
 * 
 * 包含VRP问题的各种统计数据，用于问题分析和求解器配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPProblemStatistics {
    
    /**
     * 车辆数量
     */
    private Integer vehicleCount;
    
    /**
     * 服务数量
     */
    private Integer serviceCount;
    
    /**
     * 总需求时间（分钟）
     */
    private Double totalDemandTimeMinutes;
    
    /**
     * 总车辆容量（分钟）
     */
    private Double totalVehicleCapacityMinutes;
    
    /**
     * 平均服务时间（分钟）
     */
    private Double averageServiceTimeMinutes;
    
    /**
     * 最大服务时间（分钟）
     */
    private Double maxServiceTimeMinutes;
    
    /**
     * 最小服务时间（分钟）
     */
    private Double minServiceTimeMinutes;
    
    /**
     * 服务时间标准差
     */
    private Double serviceTimeStandardDeviation;
    
    /**
     * 车辆容量利用率
     */
    private Double capacityUtilizationRate;
    
    /**
     * 平均每车服务数量
     */
    private Double averageServicesPerVehicle;
    
    /**
     * 问题复杂度评分（1-10，10为最复杂）
     */
    private Integer problemComplexityScore;
    
    /**
     * 地理分散度（平均服务间距离，公里）
     */
    private Double geographicDispersionKm;
    
    /**
     * 是否包含时间窗口约束
     */
    private Boolean hasTimeWindowConstraints;
    
    /**
     * 预估求解时间（秒）
     */
    private Double estimatedSolvingTimeSeconds;
    
    /**
     * 从车辆和服务列表计算统计信息
     * 
     * @param vehicles 车辆列表
     * @param services 服务列表
     * @return 统计信息
     */
    public static VRPProblemStatistics calculateFrom(List<VRPVehicle> vehicles, List<VRPService> services) {
        if (vehicles == null || services == null) {
            return VRPProblemStatistics.builder().build();
        }
        
        VRPProblemStatistics.VRPProblemStatisticsBuilder builder = VRPProblemStatistics.builder();
        
        // 基本计数
        int vehicleCount = vehicles.size();
        int serviceCount = services.size();
        
        builder.vehicleCount(vehicleCount);
        builder.serviceCount(serviceCount);
        
        // 车辆容量统计
        double totalCapacity = vehicles.stream()
            .mapToDouble(v -> v.getTimeCapacityMinutes() != null ? v.getTimeCapacityMinutes() : 0.0)
            .sum();
        
        builder.totalVehicleCapacityMinutes(totalCapacity);
        
        if (serviceCount > 0) {
            // 服务时间统计
            DoubleSummaryStatistics serviceTimeStats = services.stream()
                .mapToDouble(s -> s.getServiceTimeMinutes() != null ? s.getServiceTimeMinutes() : 0.0)
                .summaryStatistics();
            
            builder.averageServiceTimeMinutes(serviceTimeStats.getAverage());
            builder.maxServiceTimeMinutes(serviceTimeStats.getMax());
            builder.minServiceTimeMinutes(serviceTimeStats.getMin());
            
            // 总需求时间
            double totalDemand = services.stream()
                .mapToDouble(s -> s.getDemandTimeMinutes() != null ? s.getDemandTimeMinutes() : 0.0)
                .sum();
            
            builder.totalDemandTimeMinutes(totalDemand);
            
            // 容量利用率
            double utilizationRate = totalCapacity > 0 ? totalDemand / totalCapacity : 0.0;
            builder.capacityUtilizationRate(Math.min(1.0, utilizationRate));
            
            // 服务时间标准差
            double avgServiceTime = serviceTimeStats.getAverage();
            double variance = services.stream()
                .mapToDouble(s -> {
                    double time = s.getServiceTimeMinutes() != null ? s.getServiceTimeMinutes() : 0.0;
                    return Math.pow(time - avgServiceTime, 2);
                })
                .average()
                .orElse(0.0);
            
            builder.serviceTimeStandardDeviation(Math.sqrt(variance));
            
            // 平均每车服务数量
            double servicesPerVehicle = vehicleCount > 0 ? (double) serviceCount / vehicleCount : 0.0;
            builder.averageServicesPerVehicle(servicesPerVehicle);
            
            // 地理分散度计算
            double geographicDispersion = calculateGeographicDispersion(services);
            builder.geographicDispersionKm(geographicDispersion);
            
            // 时间窗口约束检查
            boolean hasTimeWindows = services.stream()
                .anyMatch(s -> s.getEarliestStartTime() != null || s.getLatestStartTime() != null);
            builder.hasTimeWindowConstraints(hasTimeWindows);
            
            // 问题复杂度评分
            int complexityScore = calculateComplexityScore(vehicleCount, serviceCount, utilizationRate, 
                geographicDispersion, hasTimeWindows);
            builder.problemComplexityScore(complexityScore);
            
            // 预估求解时间
            double estimatedTime = estimateSolvingTime(vehicleCount, serviceCount, complexityScore);
            builder.estimatedSolvingTimeSeconds(estimatedTime);
        }
        
        return builder.build();
    }
    
    /**
     * 计算地理分散度
     * 
     * @param services 服务列表
     * @return 平均服务间距离（公里）
     */
    private static double calculateGeographicDispersion(List<VRPService> services) {
        if (services.size() < 2) {
            return 0.0;
        }
        
        double totalDistance = 0.0;
        int pairCount = 0;
        
        for (int i = 0; i < services.size(); i++) {
            for (int j = i + 1; j < services.size(); j++) {
                VRPService service1 = services.get(i);
                VRPService service2 = services.get(j);
                
                if (service1.getServiceLocation() != null && service2.getServiceLocation() != null) {
                    double distance = service1.getServiceLocation().calculateDistanceTo(service2.getServiceLocation());
                    totalDistance += distance;
                    pairCount++;
                }
            }
        }
        
        return pairCount > 0 ? totalDistance / pairCount : 0.0;
    }
    
    /**
     * 计算问题复杂度评分
     * 
     * @param vehicleCount 车辆数量
     * @param serviceCount 服务数量
     * @param utilizationRate 利用率
     * @param geographicDispersion 地理分散度
     * @param hasTimeWindows 是否有时间窗口
     * @return 复杂度评分（1-10）
     */
    private static int calculateComplexityScore(int vehicleCount, int serviceCount, double utilizationRate, 
                                               double geographicDispersion, boolean hasTimeWindows) {
        double score = 1.0;
        
        // 规模复杂度（30%权重）
        double sizeComplexity = Math.min(5.0, (serviceCount / 10.0) + (vehicleCount / 5.0));
        score += sizeComplexity * 0.3;
        
        // 容量复杂度（25%权重）
        double capacityComplexity = utilizationRate > 0.9 ? 3.0 : utilizationRate > 0.7 ? 2.0 : 1.0;
        score += capacityComplexity * 0.25;
        
        // 地理复杂度（25%权重）
        double geoComplexity = geographicDispersion > 30 ? 3.0 : geographicDispersion > 15 ? 2.0 : 1.0;
        score += geoComplexity * 0.25;
        
        // 约束复杂度（20%权重）
        double constraintComplexity = hasTimeWindows ? 2.0 : 1.0;
        score += constraintComplexity * 0.2;
        
        return Math.max(1, Math.min(10, (int) Math.round(score)));
    }
    
    /**
     * 预估求解时间
     * 
     * @param vehicleCount 车辆数量
     * @param serviceCount 服务数量
     * @param complexityScore 复杂度评分
     * @return 预估求解时间（秒）
     */
    private static double estimateSolvingTime(int vehicleCount, int serviceCount, int complexityScore) {
        // 基础求解时间：基于问题规模
        double baseTime = Math.log(serviceCount + 1) * Math.log(vehicleCount + 1) * 2;
        
        // 复杂度调整因子
        double complexityFactor = 1.0 + (complexityScore - 1) * 0.3;
        
        // 预估时间
        double estimatedTime = baseTime * complexityFactor;
        
        // 合理范围限制（5秒到300秒）
        return Math.max(5.0, Math.min(300.0, estimatedTime));
    }
    
    /**
     * 获取问题规模描述
     * 
     * @return 规模描述
     */
    public String getProblemSizeCategory() {
        if (serviceCount == null) {
            return "未知";
        }
        
        if (serviceCount <= 20) {
            return "小规模";
        } else if (serviceCount <= 50) {
            return "中等规模";
        } else if (serviceCount <= 100) {
            return "大规模";
        } else {
            return "超大规模";
        }
    }
    
    /**
     * 获取复杂度描述
     * 
     * @return 复杂度描述
     */
    public String getComplexityDescription() {
        if (problemComplexityScore == null) {
            return "未知";
        }
        
        if (problemComplexityScore <= 3) {
            return "简单";
        } else if (problemComplexityScore <= 6) {
            return "中等";
        } else if (problemComplexityScore <= 8) {
            return "复杂";
        } else {
            return "极其复杂";
        }
    }
    
    /**
     * 获取容量利用率描述
     * 
     * @return 利用率描述
     */
    public String getUtilizationDescription() {
        if (capacityUtilizationRate == null) {
            return "未知";
        }
        
        if (capacityUtilizationRate < 0.5) {
            return "利用率偏低";
        } else if (capacityUtilizationRate < 0.8) {
            return "利用率适中";
        } else if (capacityUtilizationRate < 0.95) {
            return "利用率较高";
        } else {
            return "利用率很高";
        }
    }
    
    /**
     * 获取统计摘要
     * 
     * @return 统计摘要
     */
    public String getStatisticsSummary() {
        return String.format("%s问题，%d车辆/%d服务，%s，%s，预估求解时间%.1f秒",
            getProblemSizeCategory(),
            vehicleCount != null ? vehicleCount : 0,
            serviceCount != null ? serviceCount : 0,
            getComplexityDescription(),
            getUtilizationDescription(),
            estimatedSolvingTimeSeconds != null ? estimatedSolvingTimeSeconds : 0.0);
    }
    
    @Override
    public String toString() {
        return getStatisticsSummary();
    }
}