package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 线性约束
 * 
 * 表示MILP问题中的线性约束条件：Σ(coefficient * variable) ≤/=/≥ bound
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Data
public class LinearConstraint {
    
    /**
     * 约束名称
     */
    private String name;
    
    /**
     * 约束系数（变量名 -> 系数）
     */
    private Map<String, Double> coefficients;
    
    /**
     * 约束类型
     */
    private ConstraintType type;
    
    /**
     * 约束右端值
     */
    private double rightHandSide;
    
    /**
     * 下界（用于范围约束）
     */
    private Double lowerBound;
    
    /**
     * 上界（用于范围约束）
     */
    private Double upperBound;
    
    /**
     * 约束描述
     */
    private String description;
    
    /**
     * 约束优先级（软约束使用）
     */
    private int priority;
    
    /**
     * 违反惩罚系数
     */
    private double penaltyCoefficient;
    
    public LinearConstraint() {
        this.coefficients = new HashMap<>();
        this.type = ConstraintType.LESS_EQUAL;
        this.rightHandSide = 0.0;
        this.priority = 1;
        this.penaltyCoefficient = 1.0;
    }
    
    public LinearConstraint(String name) {
        this();
        this.name = name;
    }
    
    /**
     * 添加变量系数
     */
    public LinearConstraint addTerm(String variableName, double coefficient) {
        coefficients.put(variableName, coefficient);
        return this;
    }
    
    /**
     * 添加多个变量系数
     */
    public LinearConstraint addTerms(Map<String, Double> terms) {
        coefficients.putAll(terms);
        return this;
    }
    
    /**
     * 设置为小于等于约束
     */
    public LinearConstraint setLessEqual(double rhs) {
        this.type = ConstraintType.LESS_EQUAL;
        this.rightHandSide = rhs;
        this.upperBound = rhs;
        this.lowerBound = null;
        return this;
    }
    
    /**
     * 设置为大于等于约束
     */
    public LinearConstraint setGreaterEqual(double rhs) {
        this.type = ConstraintType.GREATER_EQUAL;
        this.rightHandSide = rhs;
        this.lowerBound = rhs;
        this.upperBound = null;
        return this;
    }
    
    /**
     * 设置为等于约束
     */
    public LinearConstraint setEqual(double rhs) {
        this.type = ConstraintType.EQUAL;
        this.rightHandSide = rhs;
        this.lowerBound = rhs;
        this.upperBound = rhs;
        return this;
    }
    
    /**
     * 设置为范围约束
     */
    public LinearConstraint setRange(double lowerBound, double upperBound) {
        this.type = ConstraintType.RANGE;
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
        this.rightHandSide = upperBound; // 默认使用上界作为右端值
        return this;
    }
    
    /**
     * 设置上界
     */
    public void setUpperBound(double upperBound) {
        this.upperBound = upperBound;
        if (type == ConstraintType.LESS_EQUAL || type == ConstraintType.EQUAL) {
            this.rightHandSide = upperBound;
        }
    }
    
    /**
     * 设置下界
     */
    public void setLowerBound(double lowerBound) {
        this.lowerBound = lowerBound;
        if (type == ConstraintType.GREATER_EQUAL || type == ConstraintType.EQUAL) {
            this.rightHandSide = lowerBound;
        }
    }
    
    /**
     * 计算约束左端值
     */
    public double calculateLeftHandSide(Map<String, Double> variableValues) {
        double sum = 0.0;
        for (Map.Entry<String, Double> entry : coefficients.entrySet()) {
            String varName = entry.getKey();
            double coefficient = entry.getValue();
            Double value = variableValues.get(varName);
            if (value != null) {
                sum += coefficient * value;
            }
        }
        return sum;
    }
    
    /**
     * 检查约束是否满足
     */
    public boolean isSatisfied(Map<String, Double> variableValues) {
        double lhs = calculateLeftHandSide(variableValues);
        
        switch (type) {
            case LESS_EQUAL:
                return lhs <= rightHandSide + 1e-9;
            case GREATER_EQUAL:
                return lhs >= rightHandSide - 1e-9;
            case EQUAL:
                return Math.abs(lhs - rightHandSide) <= 1e-9;
            case RANGE:
                return (lowerBound == null || lhs >= lowerBound - 1e-9) &&
                       (upperBound == null || lhs <= upperBound + 1e-9);
            default:
                return false;
        }
    }
    
    /**
     * 计算约束违反程度
     */
    public double calculateViolation(Map<String, Double> variableValues) {
        double lhs = calculateLeftHandSide(variableValues);
        
        switch (type) {
            case LESS_EQUAL:
                return Math.max(0.0, lhs - rightHandSide);
            case GREATER_EQUAL:
                return Math.max(0.0, rightHandSide - lhs);
            case EQUAL:
                return Math.abs(lhs - rightHandSide);
            case RANGE:
                double violation = 0.0;
                if (lowerBound != null && lhs < lowerBound) {
                    violation += lowerBound - lhs;
                }
                if (upperBound != null && lhs > upperBound) {
                    violation += lhs - upperBound;
                }
                return violation;
            default:
                return 0.0;
        }
    }
    
    /**
     * 获取约束边界描述
     */
    public String getBoundDescription() {
        switch (type) {
            case LESS_EQUAL:
                return "≤ " + String.format("%.2f", rightHandSide);
            case GREATER_EQUAL:
                return "≥ " + String.format("%.2f", rightHandSide);
            case EQUAL:
                return "= " + String.format("%.2f", rightHandSide);
            case RANGE:
                return String.format("[%.2f, %.2f]", 
                    lowerBound != null ? lowerBound : Double.NEGATIVE_INFINITY,
                    upperBound != null ? upperBound : Double.POSITIVE_INFINITY);
            default:
                return "未知类型";
        }
    }
    
    /**
     * 生成约束表达式字符串
     */
    public String toExpressionString() {
        StringBuilder sb = new StringBuilder();
        
        // 构建左端表达式
        boolean first = true;
        for (Map.Entry<String, Double> entry : coefficients.entrySet()) {
            double coeff = entry.getValue();
            String varName = entry.getKey();
            
            if (!first) {
                sb.append(coeff >= 0 ? " + " : " - ");
                coeff = Math.abs(coeff);
            } else {
                if (coeff < 0) {
                    sb.append("-");
                    coeff = Math.abs(coeff);
                }
                first = false;
            }
            
            if (Math.abs(coeff - 1.0) > 1e-9) {
                sb.append(String.format("%.3f", coeff));
            }
            sb.append(varName);
        }
        
        if (sb.length() == 0) {
            sb.append("0");
        }
        
        // 添加约束关系
        sb.append(" ").append(getTypeSymbol()).append(" ");
        
        // 添加右端值
        switch (type) {
            case RANGE:
                sb.append(String.format("[%.2f, %.2f]", 
                    lowerBound != null ? lowerBound : Double.NEGATIVE_INFINITY,
                    upperBound != null ? upperBound : Double.POSITIVE_INFINITY));
                break;
            default:
                sb.append(String.format("%.2f", rightHandSide));
                break;
        }
        
        return sb.toString();
    }
    
    /**
     * 获取约束类型符号
     */
    public String getTypeSymbol() {
        switch (type) {
            case LESS_EQUAL:
                return "≤";
            case GREATER_EQUAL:
                return "≥";
            case EQUAL:
                return "=";
            case RANGE:
                return "∈";
            default:
                return "?";
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        if (name != null) {
            sb.append(name).append(": ");
        }
        sb.append(toExpressionString());
        return sb.toString();
    }
}

