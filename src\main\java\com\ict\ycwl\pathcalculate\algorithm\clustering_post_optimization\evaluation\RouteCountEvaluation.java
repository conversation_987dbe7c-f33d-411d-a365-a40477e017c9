package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.Builder;
import lombok.Data;

/**
 * 路线数量评估结果
 * 
 * 综合4维分析结果的评估报告
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class RouteCountEvaluation {
    
    /**
     * 被评估的中转站
     */
    private TransitDepot depot;
    
    /**
     * 当前路线数量
     */
    private int currentRouteCount;
    
    /**
     * 工作量理论分析结果
     */
    private WorkloadAnalysis workloadAnalysis;
    
    /**
     * 约束违反分析结果
     */
    private ConstraintViolationAnalysis constraintAnalysis;
    
    /**
     * 效率理论分析结果
     */
    private EfficiencyAnalysis efficiencyAnalysis;
    
    /**
     * 数学建模分析结果
     */
    private MathematicalModelAnalysis modelAnalysis;
    
    /**
     * 最终推荐决策
     */
    private RouteCountRecommendation recommendation;
    
    /**
     * 评估执行时间（毫秒）
     */
    private long evaluationTimeMs;
    
    /**
     * 评估置信度 (0.0-1.0)
     */
    private double confidence;
    
    /**
     * 评估摘要说明
     */
    private String summary;
    
    /**
     * 获取综合评分
     */
    public double getComprehensiveScore() {
        if (recommendation == null) {
            return 0.0;
        }
        return recommendation.getComprehensiveScore();
    }
    
    /**
     * 是否需要调整路线数量
     */
    public boolean needsAdjustment() {
        return recommendation != null && 
               recommendation.getRecommendedAction() != RouteCountAction.MAINTAIN;
    }
    
    /**
     * 获取推荐路线数量变化
     */
    public int getRecommendedChange() {
        if (recommendation == null) {
            return 0;
        }
        return recommendation.getRouteCountAdjustment();
    }
    
    /**
     * 生成评估报告
     */
    public String generateReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 路线数量评估报告 ===\n");
        report.append(String.format("中转站: %s\n", depot.getTransitDepotName()));
        report.append(String.format("当前路线数: %d条\n", currentRouteCount));
        report.append(String.format("评估耗时: %dms\n", evaluationTimeMs));
        report.append(String.format("置信度: %.1f%%\n", confidence * 100));
        report.append("\n");
        
        // 工作量分析摘要
        if (workloadAnalysis != null) {
            report.append("【工作量分析】\n");
            report.append(String.format("  总工作量: %.1f分钟\n", workloadAnalysis.getTotalWorkload()));
            report.append(String.format("  理想路线数: %.1f条\n", workloadAnalysis.getIdealRouteCount()));
            report.append(String.format("  负载均衡指数: %.3f\n", workloadAnalysis.getLoadBalanceIndex()));
            report.append("\n");
        }
        
        // 约束分析摘要
        if (constraintAnalysis != null) {
            report.append("【约束违反分析】\n");
            report.append(String.format("  违反路线数: %d条 (%.1f%%)\n", 
                constraintAnalysis.getViolationCount(),
                constraintAnalysis.getViolationRate() * 100));
            report.append(String.format("  最大工作时间: %.1f分钟\n", constraintAnalysis.getMaxWorkTime()));
            report.append(String.format("  时间差距: %.1f分钟\n", constraintAnalysis.getTimeGap()));
            report.append("\n");
        }
        
        // 推荐决策
        if (recommendation != null) {
            report.append("【推荐决策】\n");
            report.append(String.format("  建议行动: %s\n", getActionDescription(recommendation.getRecommendedAction())));
            report.append(String.format("  推荐路线数: %d条 (%+d)\n", 
                recommendation.getRecommendedRouteCount(),
                recommendation.getRouteCountAdjustment()));
            report.append(String.format("  综合评分: %.3f\n", recommendation.getComprehensiveScore()));
            report.append("\n");
        }
        
        // 评估摘要
        if (summary != null) {
            report.append("【摘要】\n");
            report.append(summary);
        }
        
        return report.toString();
    }
    
    /**
     * 获取行动描述
     */
    private String getActionDescription(RouteCountAction action) {
        switch (action) {
            case INCREASE:
                return "增加路线数量";
            case DECREASE:
                return "减少路线数量";
            case MAINTAIN:
                return "保持当前数量";
            default:
                return "未知行动";
        }
    }
}