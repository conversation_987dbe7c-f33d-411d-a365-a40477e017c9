package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 效率理论分析结果
 * 
 * 基于运筹学理论的路线效率分析
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class EfficiencyAnalysis {
    
    /**
     * 各路线效率指标
     */
    private List<RouteEfficiencyMetrics> routeMetrics;
    
    /**
     * 平均效率分数
     */
    private double averageEfficiency;
    
    /**
     * 效率方差
     */
    private double efficiencyVariance;
    
    /**
     * 边际效率增加（增加1条路线的效率变化预测）
     */
    private double marginalEfficiencyIncrease;
    
    /**
     * 边际效率减少（减少1条路线的效率变化预测）
     */
    private double marginalEfficiencyDecrease;
    
    /**
     * 是否效率接近最优
     */
    private boolean isEfficiencyOptimal;
    
    /**
     * 效率改进潜力评分 (0.0-1.0)
     */
    private double improvementPotential;
    
    /**
     * 获取效率等级
     */
    public EfficiencyGrade getEfficiencyGrade() {
        if (averageEfficiency >= 0.9) {
            return EfficiencyGrade.EXCELLENT;
        } else if (averageEfficiency >= 0.8) {
            return EfficiencyGrade.GOOD;
        } else if (averageEfficiency >= 0.7) {
            return EfficiencyGrade.FAIR;
        } else if (averageEfficiency >= 0.6) {
            return EfficiencyGrade.POOR;
        } else {
            return EfficiencyGrade.VERY_POOR;
        }
    }
    
    /**
     * 是否需要基于效率调整路线数量
     */
    public boolean needsEfficiencyBasedAdjustment() {
        return !isEfficiencyOptimal && improvementPotential > 0.2;
    }
    
    /**
     * 获取基于效率的推荐调整方向
     */
    public RouteCountAction getEfficiencyBasedRecommendation() {
        if (marginalEfficiencyIncrease > 0.1) {
            return RouteCountAction.INCREASE;
        } else if (marginalEfficiencyDecrease > 0.1) {
            return RouteCountAction.DECREASE;
        } else {
            return RouteCountAction.MAINTAIN;
        }
    }
    
    /**
     * 获取效率均衡性评分
     */
    public double getEfficiencyBalanceScore() {
        if (efficiencyVariance == 0.0) {
            return 1.0;
        }
        // 方差越小，均衡性越好
        return Math.max(0.0, 1.0 - efficiencyVariance);
    }
    
    /**
     * 计算低效路线比例
     */
    public double getLowEfficiencyRouteRatio() {
        if (routeMetrics == null || routeMetrics.isEmpty()) {
            return 0.0;
        }
        
        long lowEfficiencyCount = routeMetrics.stream()
            .mapToDouble(RouteEfficiencyMetrics::getEfficiencyScore)
            .filter(score -> score < 0.6)
            .count();
        
        return (double) lowEfficiencyCount / routeMetrics.size();
    }
    
    /**
     * 路线效率指标
     */
    @Data
    @Builder
    public static class RouteEfficiencyMetrics {
        
        /**
         * 路线索引
         */
        private int routeIndex;
        
        /**
         * 工作时间利用率 (实际工作时间/理想工作时间)
         */
        private double timeUtilization;
        
        /**
         * 聚集区密度效率 (聚集区数量/工作时间)
         */
        private double densityEfficiency;
        
        /**
         * 地理效率 (1/平均距离)
         */
        private double geographicEfficiency;
        
        /**
         * 综合效率评分 (0.0-1.0)
         */
        private double efficiencyScore;
        
        /**
         * 效率瓶颈类型
         */
        private EfficiencyBottleneck bottleneck;
        
        /**
         * 改进建议
         */
        private String improvementSuggestion;
        
        /**
         * 获取效率等级
         */
        public EfficiencyGrade getGrade() {
            if (efficiencyScore >= 0.9) {
                return EfficiencyGrade.EXCELLENT;
            } else if (efficiencyScore >= 0.8) {
                return EfficiencyGrade.GOOD;
            } else if (efficiencyScore >= 0.7) {
                return EfficiencyGrade.FAIR;
            } else if (efficiencyScore >= 0.6) {
                return EfficiencyGrade.POOR;
            } else {
                return EfficiencyGrade.VERY_POOR;
            }
        }
    }
    
    /**
     * 效率等级枚举
     */
    public enum EfficiencyGrade {
        EXCELLENT("优秀", 5, "效率极高"),
        GOOD("良好", 4, "效率较高"),
        FAIR("一般", 3, "效率中等"),
        POOR("较差", 2, "效率较低"),
        VERY_POOR("很差", 1, "效率很低");
        
        private final String description;
        private final int score;
        private final String detail;
        
        EfficiencyGrade(String description, int score, String detail) {
            this.description = description;
            this.score = score;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getScore() {
            return score;
        }
        
        public String getDetail() {
            return detail;
        }
    }
    
    /**
     * 效率瓶颈类型枚举
     */
    public enum EfficiencyBottleneck {
        TIME_UNDERUTILIZATION("时间利用不足"),
        TIME_OVERUTILIZATION("时间过度利用"),
        LOW_DENSITY("聚集区密度过低"),
        HIGH_GEOGRAPHIC_DISPERSION("地理分散度过高"),
        BALANCED("相对均衡"),
        UNKNOWN("未知瓶颈");
        
        private final String description;
        
        EfficiencyBottleneck(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}