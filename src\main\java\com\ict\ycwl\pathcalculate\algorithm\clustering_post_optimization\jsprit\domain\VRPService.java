package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * JSPRIT VRP服务模型
 * 
 * 表示VRP问题中的一个配送服务任务，对应于一个聚集区的配送需求
 * 包含服务时间、位置、时间窗口等约束信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPService {
    
    /**
     * 服务唯一标识
     */
    private String serviceId;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务位置
     */
    private VRPLocation serviceLocation;
    
    /**
     * 服务时间（分钟）
     * 包含配送时间和处理时间
     */
    private Double serviceTimeMinutes;
    
    /**
     * 服务需求量（时间维度）
     * 用于车辆容量约束计算
     */
    private Double demandTimeMinutes;
    
    /**
     * 服务优先级（1-10，10为最高优先级）
     */
    private Integer priority;
    
    /**
     * 最早服务时间（分钟，相对于工作日开始）
     */
    private Double earliestStartTime;
    
    /**
     * 最晚服务时间（分钟，相对于工作日开始）
     */
    private Double latestStartTime;
    
    /**
     * 所属中转站ID
     */
    private String assignedDepotId;
    
    /**
     * 原始聚集区对象
     */
    private Accumulation originalAccumulation;
    
    /**
     * 服务类型
     */
    private ServiceType serviceType;
    
    /**
     * 服务成本系数
     */
    private Double serviceCost;
    
    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        STANDARD("标准配送"),
        PRIORITY("优先配送"),
        TIME_SENSITIVE("时间敏感配送");
        
        private final String description;
        
        ServiceType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 从聚集区创建VRP服务
     * 
     * @param accumulation 聚集区
     * @param depot 所属中转站
     * @param timeMatrix 时间矩阵
     * @return VRP服务
     */
    public static VRPService fromAccumulation(
        Accumulation accumulation, 
        TransitDepot depot, 
        Map<String, TimeInfo> timeMatrix
    ) {
        if (accumulation == null) {
            throw new IllegalArgumentException("聚集区不能为空");
        }
        if (depot == null) {
            throw new IllegalArgumentException("中转站不能为空");
        }
        
        VRPLocation serviceLocation = VRPLocation.fromAccumulation(accumulation);
        
        // 计算服务时间（配送时间）
        Double serviceTime = calculateServiceTime(accumulation, depot, timeMatrix);
        
        // 计算需求量（时间维度）
        Double demandTime = calculateDemandTime(accumulation, depot, timeMatrix);
        
        return VRPService.builder()
            .serviceId("service_" + accumulation.getAccumulationId())
            .serviceName(accumulation.getAccumulationName())
            .serviceLocation(serviceLocation)
            .serviceTimeMinutes(serviceTime)
            .demandTimeMinutes(demandTime)
            .priority(determinePriority(accumulation))
            .earliestStartTime(0.0) // 工作日开始时间
            .latestStartTime(420.0) // 7小时内完成配送
            .assignedDepotId(String.valueOf(depot.getTransitDepotId()))
            .originalAccumulation(accumulation)
            .serviceType(determineServiceType(accumulation))
            .serviceCost(1.0)
            .build();
    }
    
    /**
     * 计算服务时间
     * 
     * @param accumulation 聚集区
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 服务时间（分钟）
     */
    private static Double calculateServiceTime(
        Accumulation accumulation,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        // 基础配送时间
        Double deliveryTime = accumulation.getDeliveryTime();
        if (deliveryTime == null || deliveryTime <= 0) {
            deliveryTime = 30.0; // 默认30分钟配送时间
        }
        
        // 不包含往返时间，只包含现场服务时间
        return deliveryTime;
    }
    
    /**
     * 计算需求量（时间维度）
     * 
     * @param accumulation 聚集区
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 需求时间（分钟）
     */
    private static Double calculateDemandTime(
        Accumulation accumulation,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        // 配送时间
        Double deliveryTime = accumulation.getDeliveryTime();
        if (deliveryTime == null || deliveryTime <= 0) {
            deliveryTime = 30.0;
        }
        
        // 往返交通时间
        Double travelTime = 0.0;
        if (timeMatrix != null) {
            String key = depot.getTransitDepotId() + "-" + accumulation.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(key);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                travelTime = timeInfo.getTravelTime() * 2; // 往返
            }
        }
        
        // 如果没有时间矩阵数据，使用距离估算
        if (travelTime <= 0.0) {
            VRPLocation depotLocation = VRPLocation.fromTransitDepot(depot);
            VRPLocation accLocation = VRPLocation.fromAccumulation(accumulation);
            double distance = depotLocation.calculateDistanceTo(accLocation);
            travelTime = distance * 2.0; // 假设1公里/分钟，往返
        }
        
        return deliveryTime + travelTime;
    }
    
    /**
     * 确定服务优先级
     * 
     * @param accumulation 聚集区
     * @return 优先级（1-10）
     */
    private static Integer determinePriority(Accumulation accumulation) {
        // 根据聚集区的业务重要性确定优先级
        // 这里使用简化逻辑，实际可根据业务规则调整
        
        Double deliveryTime = accumulation.getDeliveryTime();
        if (deliveryTime == null) {
            return 5; // 默认中等优先级
        }
        
        if (deliveryTime > 60.0) {
            return 8; // 大型聚集区，高优先级
        } else if (deliveryTime > 30.0) {
            return 6; // 中型聚集区，中高优先级
        } else {
            return 4; // 小型聚集区，中低优先级
        }
    }
    
    /**
     * 确定服务类型
     * 
     * @param accumulation 聚集区
     * @return 服务类型
     */
    private static ServiceType determineServiceType(Accumulation accumulation) {
        Double deliveryTime = accumulation.getDeliveryTime();
        if (deliveryTime == null) {
            return ServiceType.STANDARD;
        }
        
        if (deliveryTime > 90.0) {
            return ServiceType.PRIORITY; // 超大聚集区，优先处理
        } else if (deliveryTime < 15.0) {
            return ServiceType.TIME_SENSITIVE; // 小聚集区，时间敏感
        } else {
            return ServiceType.STANDARD; // 标准处理
        }
    }
    
    /**
     * 验证服务配置
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return serviceId != null && !serviceId.trim().isEmpty()
            && serviceLocation != null && serviceLocation.isValid()
            && serviceTimeMinutes != null && serviceTimeMinutes > 0
            && demandTimeMinutes != null && demandTimeMinutes > 0
            && assignedDepotId != null && !assignedDepotId.trim().isEmpty()
            && originalAccumulation != null;
    }
    
    /**
     * 检查服务是否属于指定中转站
     * 
     * @param depotId 中转站ID
     * @return 是否属于指定中转站
     */
    public boolean belongsToDepot(String depotId) {
        return assignedDepotId != null && assignedDepotId.equals(depotId);
    }
    
    /**
     * 检查服务是否可以在指定时间窗口内完成
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否可以完成
     */
    public boolean canBeServedInTimeWindow(Double startTime, Double endTime) {
        if (startTime == null || endTime == null) {
            return true; // 没有时间约束
        }
        
        return startTime >= (earliestStartTime != null ? earliestStartTime : 0.0)
            && (startTime + serviceTimeMinutes) <= (latestStartTime != null ? latestStartTime : Double.MAX_VALUE)
            && (startTime + serviceTimeMinutes) <= endTime;
    }
    
    /**
     * 获取服务的时间窗口长度
     * 
     * @return 时间窗口长度（分钟）
     */
    public Double getTimeWindowLength() {
        if (earliestStartTime == null || latestStartTime == null) {
            return Double.MAX_VALUE;
        }
        return latestStartTime - earliestStartTime;
    }
    
    /**
     * 计算与另一个服务的地理距离
     * 
     * @param otherService 另一个服务
     * @return 距离（公里）
     */
    public Double calculateDistanceTo(VRPService otherService) {
        if (otherService == null || otherService.serviceLocation == null) {
            return Double.MAX_VALUE;
        }
        return this.serviceLocation.calculateDistanceTo(otherService.serviceLocation);
    }
    
    /**
     * 创建服务副本
     * 
     * @return 服务副本
     */
    public VRPService copy() {
        return VRPService.builder()
            .serviceId(this.serviceId)
            .serviceName(this.serviceName)
            .serviceLocation(this.serviceLocation)
            .serviceTimeMinutes(this.serviceTimeMinutes)
            .demandTimeMinutes(this.demandTimeMinutes)
            .priority(this.priority)
            .earliestStartTime(this.earliestStartTime)
            .latestStartTime(this.latestStartTime)
            .assignedDepotId(this.assignedDepotId)
            .originalAccumulation(this.originalAccumulation)
            .serviceType(this.serviceType)
            .serviceCost(this.serviceCost)
            .build();
    }
    
    @Override
    public String toString() {
        return String.format("VRPService{id='%s', name='%s', depot='%s', time=%.1fmin, demand=%.1fmin, priority=%d, type=%s}", 
            serviceId, serviceName, assignedDepotId, serviceTimeMinutes, demandTimeMinutes, priority, serviceType);
    }
}