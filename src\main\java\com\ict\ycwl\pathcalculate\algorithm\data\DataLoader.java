package com.ict.ycwl.pathcalculate.algorithm.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * JSON测试数据加载工具类
 * 用于从JSON文件中加载路径规划算法的测试数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DataLoader {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 从指定版本的JSON文件中加载完整的路径规划请求数据
     * 
     * @param version 数据版本（如："v1.0", "v2.0"等）
     * @return 路径规划请求对象
     * @throws IOException 当文件读取失败时抛出
     */
    public static PathPlanningRequest loadTestData(String version) throws IOException {
        // TODO: 实现完整数据加载逻辑
        
        // 加载各个组件数据
        List<Accumulation> accumulations = loadAccumulations(version);
        List<TransitDepot> transitDepots = loadTransitDepots(version);
        List<Team> teams = loadTeams(version);
        Map<String, TimeInfo> timeMatrix = loadTimeMatrix(version);
        
        return PathPlanningRequest.builder()
                .accumulations(accumulations)
                .transitDepots(transitDepots)
                .teams(teams)
                .timeMatrix(timeMatrix)
                .build();
    }
    
    /**
     * 加载聚集区数据
     * 
     * @param version 数据版本
     * @return 聚集区列表
     * @throws IOException 文件读取异常
     */
    public static List<Accumulation> loadAccumulations(String version) throws IOException {
        String resourcePath = String.format("/algorithm/data/%s/accumulations.json", version);
        
        try (InputStream inputStream = DataLoader.class.getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到聚集区数据文件: " + resourcePath);
            }
            
            JsonNode rootNode = objectMapper.readTree(inputStream);
            JsonNode dataNode = rootNode.get("data");
            
            List<Accumulation> accumulations = new ArrayList<>();
            
            if (dataNode != null && dataNode.isArray()) {
                for (JsonNode accNode : dataNode) {
                    Accumulation accumulation = parseAccumulation(accNode);
                    if (accumulation != null && accumulation.isValid()) {
                        accumulations.add(accumulation);
                    }
                }
            }
            
            return accumulations;
        }
    }
    
    /**
     * 加载中转站数据
     * 
     * @param version 数据版本
     * @return 中转站列表
     * @throws IOException 文件读取异常
     */
    public static List<TransitDepot> loadTransitDepots(String version) throws IOException {
        String resourcePath = String.format("/algorithm/data/%s/transit_depots.json", version);
        
        try (InputStream inputStream = DataLoader.class.getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到中转站数据文件: " + resourcePath);
            }
            
            JsonNode rootNode = objectMapper.readTree(inputStream);
            JsonNode dataNode = rootNode.get("data");
            
            List<TransitDepot> transitDepots = new ArrayList<>();
            
            if (dataNode != null && dataNode.isArray()) {
                for (JsonNode depotNode : dataNode) {
                    TransitDepot transitDepot = parseTransitDepot(depotNode);
                    if (transitDepot != null && transitDepot.isValid()) {
                        transitDepots.add(transitDepot);
                    }
                }
            }
            
            return transitDepots;
        }
    }
    
    /**
     * 加载班组数据
     * 
     * @param version 数据版本
     * @return 班组列表
     * @throws IOException 文件读取异常
     */
    public static List<Team> loadTeams(String version) throws IOException {
        String resourcePath = String.format("/algorithm/data/%s/teams.json", version);
        
        try (InputStream inputStream = DataLoader.class.getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到班组数据文件: " + resourcePath);
            }
            
            JsonNode rootNode = objectMapper.readTree(inputStream);
            JsonNode dataNode = rootNode.get("data");
            
            List<Team> teams = new ArrayList<>();
            
            if (dataNode != null && dataNode.isArray()) {
                for (JsonNode teamNode : dataNode) {
                    Team team = parseTeam(teamNode);
                    if (team != null && team.isValid()) {
                        teams.add(team);
                    }
                }
            }
            
            return teams;
        }
    }
    
    /**
     * 加载时间矩阵数据
     * 
     * @param version 数据版本
     * @return 时间矩阵映射
     * @throws IOException 文件读取异常
     */
    public static Map<String, TimeInfo> loadTimeMatrix(String version) throws IOException {
        String resourcePath = String.format("/algorithm/data/%s/time_matrix.json", version);
        
        try (InputStream inputStream = DataLoader.class.getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到时间矩阵数据文件: " + resourcePath);
            }
            
            JsonNode rootNode = objectMapper.readTree(inputStream);
            JsonNode dataNode = rootNode.get("data");
            
            Map<String, TimeInfo> timeMatrix = new HashMap<>();
            
            if (dataNode != null && dataNode.isObject()) {
                dataNode.fields().forEachRemaining(entry -> {
                    String key = entry.getKey();
                    JsonNode timeInfoNode = entry.getValue();
                    
                    TimeInfo timeInfo = parseTimeInfo(timeInfoNode);
                    if (timeInfo != null && timeInfo.isValid()) {
                        timeMatrix.put(key, timeInfo);
                    }
                });
            }
            
            return timeMatrix;
        }
    }
    
    /**
     * 解析聚集区JSON节点
     * 
     * @param accNode JSON节点
     * @return 聚集区对象
     */
    private static Accumulation parseAccumulation(JsonNode accNode) {
        // TODO: 实现聚集区JSON解析逻辑
        try {
            return Accumulation.builder()
                    .accumulationId(accNode.get("accumulationId").asLong())
                    .accumulationName(accNode.get("accumulationName").asText())
                    .longitude(accNode.get("longitude").asDouble())
                    .latitude(accNode.get("latitude").asDouble())
                    .transitDepotId(accNode.get("transitDepotId").asLong())
                    .deliveryTime(accNode.get("deliveryTime").asDouble())
                    .build();
        } catch (Exception e) {
            System.err.println("解析聚集区数据失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析中转站JSON节点
     * 
     * @param depotNode JSON节点
     * @return 中转站对象
     */
    private static TransitDepot parseTransitDepot(JsonNode depotNode) {
        // TODO: 实现中转站JSON解析逻辑
        try {
            return TransitDepot.builder()
                    .transitDepotId(depotNode.get("transitDepotId").asLong())
                    .transitDepotName(depotNode.get("transitDepotName").asText())
                    .longitude(depotNode.get("longitude").asDouble())
                    .latitude(depotNode.get("latitude").asDouble())
                    .groupId(depotNode.get("groupId").asLong())
                    .routeCount(depotNode.get("routeCount").asInt())
                    .build();
        } catch (Exception e) {
            System.err.println("解析中转站数据失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析班组JSON节点
     * 
     * @param teamNode JSON节点
     * @return 班组对象
     */
    private static Team parseTeam(JsonNode teamNode) {
        // TODO: 实现班组JSON解析逻辑
        try {
            List<Long> transitDepotIds = new ArrayList<>();
            JsonNode idsNode = teamNode.get("transitDepotIds");
            
            if (idsNode != null && idsNode.isArray()) {
                for (JsonNode idNode : idsNode) {
                    transitDepotIds.add(idNode.asLong());
                }
            }
            
            return Team.builder()
                    .teamId(teamNode.get("teamId").asLong())
                    .teamName(teamNode.get("teamName").asText())
                    .transitDepotIds(transitDepotIds)
                    .build();
        } catch (Exception e) {
            System.err.println("解析班组数据失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析时间信息JSON节点
     * 
     * @param timeInfoNode JSON节点
     * @return 时间信息对象
     */
    private static TimeInfo parseTimeInfo(JsonNode timeInfoNode) {
        try {
            Double travelTime = timeInfoNode.has("travelTime") ? 
                    timeInfoNode.get("travelTime").asDouble() : null;
            Double distance = timeInfoNode.has("distance") ? 
                    timeInfoNode.get("distance").asDouble() : 0.0;
            
            return TimeInfo.builder()
                    .travelTime(travelTime)
                    .distance(distance)
                    .build();
        } catch (Exception e) {
            System.err.println("解析时间信息数据失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取可用的数据版本列表
     * 
     * @return 版本列表
     */
    public static List<String> getAvailableVersions() {
        // TODO: 实现版本发现逻辑
        // 可以通过扫描resources/algorithm/data/目录下的子目录来实现
        List<String> versions = new ArrayList<>();
        versions.add("v1.0"); // 默认版本
        return versions;
    }
    
    /**
     * 验证数据完整性
     * 
     * @param request 请求对象
     * @return 验证结果和错误信息
     */
    public static DataValidationResult validateData(PathPlanningRequest request) {
        // TODO: 实现数据完整性验证逻辑
        List<String> errors = new ArrayList<>();
        
        // 检查数据是否为空
        if (request.getAccumulations() == null || request.getAccumulations().isEmpty()) {
            errors.add("聚集区数据为空");
        }
        
        if (request.getTransitDepots() == null || request.getTransitDepots().isEmpty()) {
            errors.add("中转站数据为空");
        }
        
        if (request.getTeams() == null || request.getTeams().isEmpty()) {
            errors.add("班组数据为空");
        }
        
        if (request.getTimeMatrix() == null || request.getTimeMatrix().isEmpty()) {
            errors.add("时间矩阵数据为空");
        }
        
        // 检查ID关联关系
        if (errors.isEmpty()) {
            validateRelationships(request, errors);
        }
        
        return new DataValidationResult(errors.isEmpty(), errors);
    }
    
    /**
     * 验证数据关联关系
     * 
     * @param request 请求对象
     * @param errors 错误列表
     */
    private static void validateRelationships(PathPlanningRequest request, List<String> errors) {
        // TODO: 实现关联关系验证
        // 1. 检查聚集区的transitDepotId是否在中转站列表中存在
        // 2. 检查中转站的groupId是否在班组列表中存在
        // 3. 检查时间矩阵是否覆盖所有必要的点对
    }
    
    /**
     * 数据验证结果类
     */
    public static class DataValidationResult {
        private final boolean valid;
        private final List<String> errors;
        
        public DataValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }
        
        public boolean isValid() { return valid; }
        public List<String> getErrors() { return errors; }
        
        @Override
        public String toString() {
            if (valid) {
                return "数据验证通过";
            } else {
                return "数据验证失败: " + String.join(", ", errors);
            }
        }
    }
} 