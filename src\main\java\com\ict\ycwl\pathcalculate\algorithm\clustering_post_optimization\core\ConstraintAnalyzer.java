package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 约束违反检测和分析器
 * 
 * 负责识别聚类结果中的约束违反情况，分析违反模式，
 * 并为优化策略选择提供决策依据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class ConstraintAnalyzer {
    
    // 约束阈值常量
    private static final double MAX_WORK_TIME_MINUTES = 450.0;
    private static final double MAX_TIME_GAP_MINUTES = 30.0;
    private static final double IDEAL_WORK_TIME_MINUTES = 350.0;
    private static final double MIN_WORK_TIME_MINUTES = 300.0;
    
    /**
     * 检测约束违反情况
     * 
     * @param clusters 聚类结果
     * @param depot 中转站信息
     * @param timeMatrix 时间矩阵
     * @return 约束违反报告
     */
    public ConstraintViolationReport analyzeViolations(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        log.debug("🔍 开始分析约束违反情况，中转站: {}, 聚类数: {}", 
            depot.getTransitDepotName(), clusters.size());
        
        // 1. 计算每个聚类的工作时间
        List<Double> clusterWorkTimes = calculateClusterWorkTimes(clusters, depot, timeMatrix);
        
        // 2. 检测450分钟约束违反
        List<Integer> maxTimeViolations = detectMaxTimeViolations(clusterWorkTimes);
        
        // 3. 检测30分钟时间差异违反
        boolean timeGapViolation = detectTimeGapViolation(clusterWorkTimes);
        
        // 4. 检测地理分散性问题
        List<Integer> geographicViolations = detectGeographicViolations(clusters, depot);
        
        // 5. 计算严重程度
        ViolationSeverity severity = calculateSeverity(maxTimeViolations, timeGapViolation, 
                                                      geographicViolations);
        
        // 6. 推荐优化策略
        OptimizationStrategy recommendedStrategy = recommendOptimizationStrategy(
            maxTimeViolations, timeGapViolation, geographicViolations, severity);
        
        ConstraintViolationReport report = ConstraintViolationReport.builder()
            .depot(depot)
            .clusterWorkTimes(clusterWorkTimes)
            .maxTimeViolations(maxTimeViolations)
            .timeGapViolation(timeGapViolation)
            .geographicViolations(geographicViolations)
            .severity(severity)
            .recommendedStrategy(recommendedStrategy)
            .maxWorkTime(Collections.max(clusterWorkTimes))
            .minWorkTime(Collections.min(clusterWorkTimes))
            .averageWorkTime(clusterWorkTimes.stream().mapToDouble(Double::doubleValue).average().orElse(0.0))
            .workTimeVariance(calculateVariance(clusterWorkTimes))
            .maxTimeGap(Collections.max(clusterWorkTimes) - Collections.min(clusterWorkTimes))
            .build();
        
        log.debug("📊 约束分析完成 - 450分钟违反: {}, 30分钟差异违反: {}, 严重程度: {}", 
            maxTimeViolations.size(), timeGapViolation ? 1 : 0, severity);
        
        return report;
    }
    
    /**
     * 计算每个聚类的工作时间
     */
    private List<Double> calculateClusterWorkTimes(
        List<List<Accumulation>> clusters,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        return clusters.stream()
            .map(cluster -> calculateSingleClusterWorkTime(cluster, depot, timeMatrix))
            .collect(Collectors.toList());
    }
    
    /**
     * 计算单个聚类的工作时间
     */
    private double calculateSingleClusterWorkTime(
        List<Accumulation> cluster,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix
    ) {
        if (cluster.isEmpty()) {
            return 0.0;
        }
        
        double totalTime = 0.0;
        
        // 1. 累加配送时间
        totalTime += cluster.stream()
            .mapToDouble(acc -> acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 0.0)
            .sum();
        
        // 2. 累加往返中转站时间
        for (Accumulation acc : cluster) {
            String depotToAccKey = depot.getTransitDepotId() + "-" + acc.getAccumulationId();
            TimeInfo timeInfo = timeMatrix.get(depotToAccKey);
            if (timeInfo != null && timeInfo.getTravelTime() != null) {
                totalTime += timeInfo.getTravelTime() * 2; // 往返时间
            }
        }
        
        // 3. 估算聚类内部路径时间（简化计算）
        if (cluster.size() > 1) {
            double avgInternalTime = estimateInternalTravelTime(cluster, timeMatrix);
            totalTime += avgInternalTime;
        }
        
        return totalTime;
    }
    
    /**
     * 估算聚类内部行程时间
     */
    private double estimateInternalTravelTime(List<Accumulation> cluster, Map<String, TimeInfo> timeMatrix) {
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        double totalInternalTime = 0.0;
        int pairCount = 0;
        
        // 计算聚类内所有点对之间的平均时间
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                Accumulation acc1 = cluster.get(i);
                Accumulation acc2 = cluster.get(j);
                
                String key = acc1.getAccumulationId() + "-" + acc2.getAccumulationId();
                TimeInfo timeInfo = timeMatrix.get(key);
                
                if (timeInfo != null && timeInfo.getTravelTime() != null) {
                    totalInternalTime += timeInfo.getTravelTime();
                    pairCount++;
                }
            }
        }
        
        if (pairCount == 0) {
            return 0.0;
        }
        
        // 使用平均时间乘以聚类规模的系数作为估算
        double avgTime = totalInternalTime / pairCount;
        return avgTime * (cluster.size() - 1) * 0.6; // 0.6是经验系数
    }
    
    /**
     * 检测450分钟约束违反
     */
    private List<Integer> detectMaxTimeViolations(List<Double> clusterWorkTimes) {
        List<Integer> violations = new ArrayList<>();
        
        for (int i = 0; i < clusterWorkTimes.size(); i++) {
            if (clusterWorkTimes.get(i) > MAX_WORK_TIME_MINUTES) {
                violations.add(i);
            }
        }
        
        return violations;
    }
    
    /**
     * 检测30分钟时间差异违反
     */
    private boolean detectTimeGapViolation(List<Double> clusterWorkTimes) {
        if (clusterWorkTimes.size() <= 1) {
            return false;
        }
        
        double maxTime = Collections.max(clusterWorkTimes);
        double minTime = Collections.min(clusterWorkTimes);
        
        return (maxTime - minTime) > MAX_TIME_GAP_MINUTES;
    }
    
    /**
     * 检测地理分散性问题
     */
    private List<Integer> detectGeographicViolations(List<List<Accumulation>> clusters, TransitDepot depot) {
        List<Integer> violations = new ArrayList<>();
        
        for (int i = 0; i < clusters.size(); i++) {
            List<Accumulation> cluster = clusters.get(i);
            
            if (isGeographicallyDispersed(cluster, depot)) {
                violations.add(i);
            }
        }
        
        return violations;
    }
    
    /**
     * 判断聚类是否地理分散
     */
    private boolean isGeographicallyDispersed(List<Accumulation> cluster, TransitDepot depot) {
        if (cluster.size() <= 2) {
            return false;
        }
        
        // 计算聚类的地理中心
        double centerLat = cluster.stream().mapToDouble(Accumulation::getLatitude).average().orElse(0.0);
        double centerLng = cluster.stream().mapToDouble(Accumulation::getLongitude).average().orElse(0.0);
        
        // 计算每个点到中心的距离
        double avgDistanceFromCenter = cluster.stream()
            .mapToDouble(acc -> calculateDistance(acc.getLatitude(), acc.getLongitude(), centerLat, centerLng))
            .average().orElse(0.0);
        
        // 如果平均距离超过阈值，认为地理分散
        return avgDistanceFromCenter > 15.0; // 15公里阈值
    }
    
    /**
     * 计算两点间距离（公里）
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLng = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLng / 2) * Math.sin(dLng / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 计算严重程度
     */
    private ViolationSeverity calculateSeverity(List<Integer> maxTimeViolations, 
                                               boolean timeGapViolation,
                                               List<Integer> geographicViolations) {
        int totalViolations = maxTimeViolations.size() + 
                             (timeGapViolation ? 1 : 0) + 
                             geographicViolations.size();
        
        if (totalViolations == 0) {
            return ViolationSeverity.NONE;
        } else if (totalViolations <= 2) {
            return ViolationSeverity.MINOR;
        } else if (totalViolations <= 5) {
            return ViolationSeverity.MODERATE;
        } else {
            return ViolationSeverity.SEVERE;
        }
    }
    
    /**
     * 推荐优化策略
     */
    private OptimizationStrategy recommendOptimizationStrategy(
        List<Integer> maxTimeViolations,
        boolean timeGapViolation,
        List<Integer> geographicViolations,
        ViolationSeverity severity
    ) {
        // 策略选择决策树
        if (maxTimeViolations.size() >= 3) {
            // 严重的450分钟约束违反 -> OptaPlanner (约束求解能力强)
            return OptimizationStrategy.OPTAPLANNER_CONSTRAINTS;
        } else if (timeGapViolation && maxTimeViolations.size() >= 1) {
            // 同时有时间约束和差异问题 -> JSPRIT (负载均衡能力强)
            return OptimizationStrategy.JSPRIT_LOAD_BALANCE;
        } else if (geographicViolations.size() >= 2) {
            // 地理分散问题 -> OR-Tools (几何优化能力强)
            return OptimizationStrategy.ORTOOLS_GEOMETRIC;
        } else if (timeGapViolation) {
            // 主要是负载均衡问题 -> JSPRIT
            return OptimizationStrategy.JSPRIT_LOAD_BALANCE;
        } else if (maxTimeViolations.size() > 0) {
            // 轻微的时间约束违反 -> OptaPlanner
            return OptimizationStrategy.OPTAPLANNER_CONSTRAINTS;
        } else {
            // 默认策略
            return OptimizationStrategy.OPTAPLANNER_CONSTRAINTS;
        }
    }
    
    /**
     * 计算方差
     */
    private double calculateVariance(List<Double> values) {
        if (values.size() <= 1) {
            return 0.0;
        }
        
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double sumSquaredDiffs = values.stream()
            .mapToDouble(value -> Math.pow(value - mean, 2))
            .sum();
        
        return sumSquaredDiffs / values.size();
    }
    
    /**
     * 违反严重程度枚举
     */
    public enum ViolationSeverity {
        NONE,       // 无违反
        MINOR,      // 轻微违反
        MODERATE,   // 中等违反
        SEVERE      // 严重违反
    }
}