package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountRecommendation;
import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TimeInfo;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 统一约束模型
 * 
 * 实现时间约束与平衡一致化的MILP模型构建器
 * 核心解决450分钟约束与时间平衡的统一优化问题
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Slf4j
@Component
public class UnifiedConstraintModel {
    
    // 约束参数常量
    private static final double MAX_ROUTE_TIME_MINUTES = 450.0;     // 450分钟上限
    private static final int MAX_TOTAL_ROUTES = 130;                // 130条路线上限
    private static final double IDEAL_ROUTE_TIME_MINUTES = 350.0;   // 理想路线时间
    private static final double TIME_BALANCE_WEIGHT = 1.0;          // 时间平衡权重
    private static final double GEOGRAPHIC_WEIGHT = 0.3;            // 地理合理性权重
    
    // 模型构建配置
    private static final double BIG_M = 1e6;                       // 大M常数
    private static final double EPSILON = 1e-6;                    // 数值精度
    
    /**
     * 构建统一约束模型（无冲突设计）
     */
    public MILPProblem buildUnifiedConstraintModel(
        List<List<Accumulation>> originalRoutes,
        TransitDepot depot,
        Map<String, TimeInfo> timeMatrix,
        RouteCountRecommendation routeCountRecommendation
    ) {
        
        log.info("🔧 开始构建统一约束模型 - 中转站: {}, 原始路线数: {}", 
            depot.getTransitDepotName(), originalRoutes.size());
        
        // 创建MILP问题实例
        String problemId = "unified_constraint_" + depot.getTransitDepotId() + "_" + System.currentTimeMillis();
        String description = String.format("中转站%s的路线时间约束与平衡统一优化模型", depot.getTransitDepotName());
        
        MILPProblem problem = new MILPProblem(problemId, description);
        
        try {
            // 第1步：根据评估结果确定目标路线数量
            int targetRouteCount = determineTargetRouteCount(originalRoutes, routeCountRecommendation);
            log.debug("   目标路线数: {} (推荐调整: {:+d})", targetRouteCount, 
                routeCountRecommendation != null ? routeCountRecommendation.getRouteCountAdjustment() : 0);
            
            // 第2步：创建决策变量
            createDecisionVariables(problem, originalRoutes, depot, targetRouteCount);
            
            // 第3步：建立统一时间约束（硬约束）
            addUnifiedTimeConstraints(problem, originalRoutes, depot, timeMatrix, targetRouteCount);
            
            // 第4步：添加路线数量约束
            addRouteCountConstraints(problem, targetRouteCount);
            
            // 第5步：添加聚集区分配约束
            addAccumulationAssignmentConstraints(problem, originalRoutes, targetRouteCount);
            
            // 第6步：添加地理合理性约束（软约束）
            addGeographicConstraints(problem, originalRoutes, depot, timeMatrix, targetRouteCount);
            
            // 第7步：设置时间平衡优化目标
            addTimeBalanceObjective(problem, originalRoutes, depot, timeMatrix, targetRouteCount);
            
            // 第8步：验证模型完整性
            ValidationResult validation = problem.validate();
            if (!validation.isValid()) {
                log.warn("⚠️ MILP模型验证失败: {}", validation.getErrors());
            } else {
                log.info("✅ MILP模型构建完成: {}", problem.getStatistics().generateSummary().split("\n")[1]);
            }
            
            return problem;
            
        } catch (Exception e) {
            log.error("❌ 构建统一约束模型失败", e);
            throw new RuntimeException("统一约束模型构建失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 确定目标路线数量
     */
    private int determineTargetRouteCount(List<List<Accumulation>> originalRoutes, 
                                        RouteCountRecommendation recommendation) {
        
        int currentCount = originalRoutes.size();
        
        if (recommendation == null) {
            return currentCount; // 保持当前数量
        }
        
        int recommendedCount = recommendation.getRecommendedRouteCount();
        
        // 确保在合理范围内
        int finalCount = Math.max(1, Math.min(MAX_TOTAL_ROUTES, recommendedCount));
        
        log.debug("   路线数量确定: 当前{}条 → 推荐{}条 → 最终{}条", 
            currentCount, recommendedCount, finalCount);
        
        return finalCount;
    }
    
    /**
     * 创建决策变量
     */
    private void createDecisionVariables(MILPProblem problem, 
                                       List<List<Accumulation>> originalRoutes,
                                       TransitDepot depot,
                                       int targetRouteCount) {
        
        log.debug("   创建决策变量: 目标路线数={}", targetRouteCount);
        
        // 获取所有聚集区
        Set<String> allAccumulations = new HashSet<>();
        for (List<Accumulation> route : originalRoutes) {
            for (Accumulation acc : route) {
                allAccumulations.add(String.valueOf(acc.getAccumulationId()));
            }
        }
        
        // 1. 聚集区分配变量: x_{i,j} = 1 if 聚集区i分配给路线j
        for (String accId : allAccumulations) {
            for (int j = 0; j < targetRouteCount; j++) {
                String varName = String.format("x_%s_%d", accId, j);
                problem.addBinaryVariable(varName);
            }
        }
        
        // 2. 路线使用变量: y_j = 1 if 路线j被使用
        for (int j = 0; j < targetRouteCount; j++) {
            String varName = String.format("y_%d", j);
            problem.addBinaryVariable(varName);
        }
        
        // 3. 路线时间变量: t_j = 路线j的总时间
        for (int j = 0; j < targetRouteCount; j++) {
            String varName = String.format("t_%d", j);
            problem.addContinuousVariable(varName, 0.0, MAX_ROUTE_TIME_MINUTES);
        }
        
        // 4. 时间平衡辅助变量: d_j = |t_j - t_avg| （时间偏差）
        for (int j = 0; j < targetRouteCount; j++) {
            String varName = String.format("d_%d", j);
            problem.addContinuousVariable(varName, 0.0, MAX_ROUTE_TIME_MINUTES);
        }
        
        // 5. 平均时间变量: t_avg
        problem.addContinuousVariable("t_avg", 0.0, MAX_ROUTE_TIME_MINUTES);
        
        log.debug("   ✅ 决策变量创建完成: 聚集区分配{}个, 路线使用{}个, 时间变量{}个", 
            allAccumulations.size() * targetRouteCount, targetRouteCount, targetRouteCount * 2 + 1);
    }
    
    /**
     * 建立统一时间约束（硬约束）
     */
    private void addUnifiedTimeConstraints(MILPProblem problem, 
                                         List<List<Accumulation>> originalRoutes,
                                         TransitDepot depot,
                                         Map<String, TimeInfo> timeMatrix,
                                         int targetRouteCount) {
        
        // 添加统一时间约束: 450分钟上限 + 时间计算
        
        // 获取所有聚集区及其时间信息
        Map<String, Double> accWorkTimes = new HashMap<>();
        Map<String, Double> accTravelTimes = new HashMap<>();
        
        for (List<Accumulation> route : originalRoutes) {
            for (Accumulation acc : route) {
                String accId = String.valueOf(acc.getAccumulationId());
                
                // 工作时间
                accWorkTimes.put(accId, acc.getDeliveryTime() != null ? acc.getDeliveryTime() : 0.0);
                
                // 往返时间
                String key = depot.getTransitDepotId() + "-" + accId;
                TimeInfo timeInfo = timeMatrix.get(key);
                double travelTime = (timeInfo != null && timeInfo.getTravelTime() != null) ? 
                    timeInfo.getTravelTime() * 2 : 0.0; // 往返
                accTravelTimes.put(accId, travelTime);
            }
        }
        
        // 为每条路线添加时间计算约束和450分钟上限约束
        for (int j = 0; j < targetRouteCount; j++) {
            
            // 约束1: 路线时间计算 t_j = Σ(x_{i,j} * (work_time_i + travel_time_i))
            LinearConstraint timeCalcConstraint = problem.createConstraint();
            timeCalcConstraint.addTerm(String.format("t_%d", j), 1.0);
            
            for (String accId : accWorkTimes.keySet()) {
                double totalTime = accWorkTimes.get(accId) + accTravelTimes.get(accId);
                timeCalcConstraint.addTerm(String.format("x_%s_%d", accId, j), -totalTime);
            }
            
            timeCalcConstraint.setEqual(0.0);
            problem.addConstraint(String.format("时间计算_路线%d", j), timeCalcConstraint);
            
            // 约束2: 450分钟上限约束 t_j ≤ 450.0 （硬约束）
            LinearConstraint timeUpperBoundConstraint = problem.createConstraint();
            timeUpperBoundConstraint.addTerm(String.format("t_%d", j), 1.0);
            timeUpperBoundConstraint.setLessEqual(MAX_ROUTE_TIME_MINUTES);
            problem.addConstraint(String.format("时间上限_路线%d", j), timeUpperBoundConstraint);
        }
        
        // 时间约束添加完成: {}条路线的时间计算和450分钟上限约束
    }
    
    /**
     * 添加路线数量约束
     */
    private void addRouteCountConstraints(MILPProblem problem, int targetRouteCount) {
        
        // 添加路线数量约束: 最多{}条路线
        
        // 约束: 使用的路线数量限制
        LinearConstraint routeCountConstraint = problem.createConstraint();
        for (int j = 0; j < targetRouteCount; j++) {
            routeCountConstraint.addTerm(String.format("y_%d", j), 1.0);
        }
        routeCountConstraint.setLessEqual(targetRouteCount);
        problem.addConstraint("路线数量上限", routeCountConstraint);
        
        log.debug("   ✅ 路线数量约束添加完成");
    }
    
    /**
     * 添加聚集区分配约束
     */
    private void addAccumulationAssignmentConstraints(MILPProblem problem, 
                                                    List<List<Accumulation>> originalRoutes,
                                                    int targetRouteCount) {
        
        // 添加聚集区分配约束
        
        // 获取所有聚集区
        Set<String> allAccumulations = new HashSet<>();
        for (List<Accumulation> route : originalRoutes) {
            for (Accumulation acc : route) {
                allAccumulations.add(String.valueOf(acc.getAccumulationId()));
            }
        }
        
        // 约束1: 每个聚集区必须且只能分配给一条路线
        for (String accId : allAccumulations) {
            LinearConstraint assignmentConstraint = problem.createConstraint();
            
            for (int j = 0; j < targetRouteCount; j++) {
                assignmentConstraint.addTerm(String.format("x_%s_%d", accId, j), 1.0);
            }
            
            assignmentConstraint.setEqual(1.0);
            problem.addConstraint(String.format("聚集区分配_%s", accId), assignmentConstraint);
        }
        
        // 约束2: 路线使用与聚集区分配的关联
        for (int j = 0; j < targetRouteCount; j++) {
            LinearConstraint routeUsageConstraint = problem.createConstraint();
            
            // 如果路线j有聚集区分配，则必须使用该路线
            for (String accId : allAccumulations) {
                routeUsageConstraint.addTerm(String.format("x_%s_%d", accId, j), 1.0);
            }
            routeUsageConstraint.addTerm(String.format("y_%d", j), -BIG_M);
            
            routeUsageConstraint.setLessEqual(0.0);
            problem.addConstraint(String.format("路线使用关联_%d", j), routeUsageConstraint);
        }
        
        log.debug("   ✅ 聚集区分配约束添加完成: {}个聚集区", allAccumulations.size());
    }
    
    /**
     * 添加地理合理性约束（软约束）
     */
    private void addGeographicConstraints(MILPProblem problem, 
                                        List<List<Accumulation>> originalRoutes,
                                        TransitDepot depot,
                                        Map<String, TimeInfo> timeMatrix,
                                        int targetRouteCount) {
        
        // 添加地理合理性约束（软约束）
        
        // 这里可以添加地理距离相关的软约束
        // 例如：相邻聚集区尽量分配到同一路线等
        // 当前先实现基础功能，地理约束可以在后续版本中完善
        
        log.debug("   ⚠️ 地理合理性约束暂时跳过（后续版本实现）");
    }
    
    /**
     * 设置时间平衡优化目标
     */
    private void addTimeBalanceObjective(MILPProblem problem, 
                                       List<List<Accumulation>> originalRoutes,
                                       TransitDepot depot,
                                       Map<String, TimeInfo> timeMatrix,
                                       int targetRouteCount) {
        
        log.debug("   设置时间平衡优化目标: 最小化时间方差");
        
        // 约束1: 计算平均时间 t_avg = (Σt_j * y_j) / (Σy_j)
        // 为简化，使用线性化处理：Σt_j * y_j = t_avg * Σy_j
        LinearConstraint avgTimeConstraint = problem.createConstraint();
        
        for (int j = 0; j < targetRouteCount; j++) {
            avgTimeConstraint.addTerm(String.format("t_%d", j), 1.0);
            avgTimeConstraint.addTerm(String.format("y_%d", j), -IDEAL_ROUTE_TIME_MINUTES); // 使用理想时间作为基准
        }
        avgTimeConstraint.addTerm("t_avg", -(double)targetRouteCount);
        
        avgTimeConstraint.setEqual(0.0);
        problem.addConstraint("平均时间计算", avgTimeConstraint);
        
        // 约束2: 计算时间偏差 d_j ≥ |t_j - t_avg|
        for (int j = 0; j < targetRouteCount; j++) {
            // d_j ≥ t_j - t_avg
            LinearConstraint deviationConstraint1 = problem.createConstraint();
            deviationConstraint1.addTerm(String.format("d_%d", j), 1.0);
            deviationConstraint1.addTerm(String.format("t_%d", j), -1.0);
            deviationConstraint1.addTerm("t_avg", 1.0);
            deviationConstraint1.setGreaterEqual(0.0);
            problem.addConstraint(String.format("时间偏差1_%d", j), deviationConstraint1);
            
            // d_j ≥ t_avg - t_j
            LinearConstraint deviationConstraint2 = problem.createConstraint();
            deviationConstraint2.addTerm(String.format("d_%d", j), 1.0);
            deviationConstraint2.addTerm(String.format("t_%d", j), 1.0);
            deviationConstraint2.addTerm("t_avg", -1.0);
            deviationConstraint2.setGreaterEqual(0.0);
            problem.addConstraint(String.format("时间偏差2_%d", j), deviationConstraint2);
        }
        
        // 目标函数: 最小化时间偏差之和（近似方差最小化）
        Map<String, Double> objectiveCoefficients = new HashMap<>();
        
        for (int j = 0; j < targetRouteCount; j++) {
            objectiveCoefficients.put(String.format("d_%d", j), TIME_BALANCE_WEIGHT);
        }
        
        problem.setMinimizeObjective(objectiveCoefficients);
        
        log.debug("   ✅ 时间平衡目标设置完成: 最小化{}条路线的时间偏差之和", targetRouteCount);
    }
}