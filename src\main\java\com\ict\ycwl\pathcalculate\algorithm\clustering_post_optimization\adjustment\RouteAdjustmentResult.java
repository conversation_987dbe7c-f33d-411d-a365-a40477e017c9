package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation.RouteCountAction;
import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 路线调整结果
 * 
 * 封装路线数量调整操作的完整结果信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class RouteAdjustmentResult {
    
    /**
     * 调整是否成功
     */
    private boolean success;
    
    /**
     * 原始路线数量
     */
    private int originalRouteCount;
    
    /**
     * 调整后路线数量
     */
    private int adjustedRouteCount;
    
    /**
     * 调整后的路线列表
     */
    private List<List<Accumulation>> adjustedRoutes;
    
    /**
     * 执行的调整动作
     */
    private RouteCountAction adjustmentAction;
    
    /**
     * 约束验证结果
     */
    private ConstraintValidationResult constraintValidation;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 路线数量变化量
     */
    public int getRouteCountChange() {
        return adjustedRouteCount - originalRouteCount;
    }
    
    /**
     * 是否增加了路线
     */
    public boolean isRouteCountIncreased() {
        return getRouteCountChange() > 0;
    }
    
    /**
     * 是否减少了路线
     */
    public boolean isRouteCountDecreased() {
        return getRouteCountChange() < 0;
    }
    
    /**
     * 是否保持路线数量不变
     */
    public boolean isRouteCountMaintained() {
        return getRouteCountChange() == 0;
    }
    
    /**
     * 获取成功率描述
     */
    public String getSuccessDescription() {
        if (success && constraintValidation != null && constraintValidation.isValid()) {
            return "完全成功";
        } else if (success && constraintValidation != null && !constraintValidation.isValid()) {
            return "部分成功（存在约束违反）";
        } else {
            return "失败";
        }
    }
    
    /**
     * 生成结果摘要
     */
    public String generateSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("路线调整结果 - %s\n", getSuccessDescription()));
        summary.append(String.format("路线数量变化: %d → %d (%+d)\n", 
            originalRouteCount, adjustedRouteCount, getRouteCountChange()));
        summary.append(String.format("调整动作: %s\n", adjustmentAction));
        summary.append(String.format("执行时间: %d ms\n", executionTimeMs));
        
        if (constraintValidation != null) {
            summary.append(String.format("约束验证: %s\n", 
                constraintValidation.isValid() ? "通过" : "失败"));
            if (!constraintValidation.getViolations().isEmpty()) {
                summary.append(String.format("约束违反数量: %d\n", 
                    constraintValidation.getViolations().size()));
            }
        }
        
        if (message != null) {
            summary.append(String.format("消息: %s\n", message));
        }
        
        return summary.toString();
    }
}