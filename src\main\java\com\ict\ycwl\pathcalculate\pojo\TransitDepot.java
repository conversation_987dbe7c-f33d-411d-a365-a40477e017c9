package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("transit_depot")
public class TransitDepot {

    @TableId(type = IdType.ASSIGN_ID)
    private Long transitDepotId;

    private String transitDepotName;

    private String status;

    private String longitude;

    private String latitude;

    private Long areaId;

    private Long groupId;

    private int isDelete;
}
