package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well19937c;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 增强遗传算法TSP求解器 - 第三方库优先版本
 * 集成Apache Commons Math统计库，简化遗传算法实现
 * 用于大规模TSP问题（>20节点）
 */
@Slf4j
@Component
public class EnhancedGeneticTSP {
    
    // 使用Apache Commons Math随机数生成器（高质量PRNG）
    private final RandomGenerator random = new Well19937c();
    
    // 个体类
    private static class Individual {
        List<Integer> genes;        // 基因序列（访问顺序）
        double fitness;             // 适应度
        double distance;            // 路径距离
        
        public Individual(int size) {
            this.genes = new ArrayList<>();
            this.fitness = 0.0;
            this.distance = Double.MAX_VALUE;
        }
        
        public Individual(Individual other) {
            this.genes = new ArrayList<>(other.genes);
            this.fitness = other.fitness;
            this.distance = other.distance;
        }
        
        // 随机初始化基因 - 使用Apache Commons Math PRNG
        public void initializeRandomly(int nodeCount, RandomGenerator random) {
            genes.clear();
            for (int i = 0; i < nodeCount; i++) {
                genes.add(i);
            }
            // 使用高质量随机数生成器进行洗牌
            for (int i = genes.size() - 1; i > 0; i--) {
                int j = random.nextInt(i + 1);
                Collections.swap(genes, i, j);
            }
        }
        
        // 贪心初始化基因
        public void initializeGreedily(double[][] costMatrix) {
            genes.clear();
            int nodeCount = costMatrix.length;
            boolean[] visited = new boolean[nodeCount];
            
            int current = 0;
            genes.add(current);
            visited[current] = true;
            
            for (int step = 1; step < nodeCount; step++) {
                int nearest = -1;
                double minCost = Double.MAX_VALUE;
                
                for (int next = 0; next < nodeCount; next++) {
                    if (!visited[next] && costMatrix[current][next] < minCost) {
                        minCost = costMatrix[current][next];
                        nearest = next;
                    }
                }
                
                if (nearest != -1) {
                    genes.add(nearest);
                    visited[nearest] = true;
                    current = nearest;
                }
            }
        }
        
        // 计算适应度
        public void calculateFitness(double[][] costMatrix) {
            if (genes.isEmpty()) {
                fitness = 0.0;
                distance = Double.MAX_VALUE;
                return;
            }
            
            double totalDistance = 0.0;
            int nodeCount = genes.size();
            
            for (int i = 0; i < nodeCount; i++) {
                int current = genes.get(i);
                int next = genes.get((i + 1) % nodeCount);
                totalDistance += costMatrix[current][next];
            }
            
            this.distance = totalDistance;
            this.fitness = totalDistance > 0 ? 1.0 / totalDistance : 0.0;
        }
    }
    
    // 种群类
    private static class Population {
        List<Individual> individuals;
        int populationSize;
        Individual best;
        
        public Population(int size) {
            this.populationSize = size;
            this.individuals = new ArrayList<>();
            this.best = null;
        }
        
        public void add(Individual individual) {
            individuals.add(individual);
            updateBest(individual);
        }
        
        private void updateBest(Individual individual) {
            if (best == null || individual.fitness > best.fitness) {
                best = new Individual(individual);
            }
        }
        
        public void updateBest() {
            best = individuals.stream()
                    .max(Comparator.comparingDouble(ind -> ind.fitness))
                    .orElse(null);
        }
        
        public Individual getBest() {
            return best;
        }
        
        public Individual getIndividual(int index) {
            return individuals.get(index);
        }
        
        public int size() {
            return individuals.size();
        }
    }
    
    // 遗传算法参数
    private double crossoverRate = 0.8;
    private double mutationRate = 0.1;
    private int populationSize = 100;
    private int maxGenerations = 500;
    private int eliteSize = 10;
    
    /**
     * 遗传算法求解TSP - 第三方库优先版本
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix) {
        
        log.info("🧬 [第三方库调用] 开始使用Apache Commons Math增强遗传算法求解TSP - 节点数: {}", cluster.size());
        long startTime = System.currentTimeMillis();
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        // 第三方库初始化日志
        log.info("📊 [Apache Commons Math] 初始化高质量随机数生成器 - Well19937c PRNG, 库版本: 3.6.1");
        log.info("📈 [Apache Commons Math] 启用统计分析模块 - DescriptiveStatistics");
        
        // 自适应参数调整
        adaptParameters(cluster.size());
        
        // 构建成本矩阵
        double[][] costMatrix = buildCostMatrix(depot, cluster, timeMatrix);
        
        // 初始化种群
        Population population = initializePopulation(costMatrix);
        
        log.debug("初始种群创建完成，种群大小: {}, 初始最优距离: {}", 
                 populationSize, population.getBest().distance);
        
        // 使用Apache Commons Math进行统计分析
        DescriptiveStatistics fitnessStats = new DescriptiveStatistics();
        DescriptiveStatistics improvementStats = new DescriptiveStatistics();
        
        // 进化迭代
        Individual bestEver = new Individual(population.getBest());
        int stagnationCount = 0;
        
        log.debug("🔬 [Apache Commons Math] 开始进化统计分析，使用DescriptiveStatistics追踪算法收敛");
        
        for (int generation = 0; generation < maxGenerations; generation++) {
            // 选择
            Population parents = select(population);
            
            // 交叉
            Population offspring = crossover(parents, costMatrix);
            
            // 变异
            mutate(offspring, costMatrix);
            
            // 局部搜索增强（对部分个体）
            if (generation % 10 == 0) {
                localSearchEnhancement(offspring, costMatrix);
            }
            
            // 环境选择（精英策略）
            population = environmentalSelection(population, offspring);
            
            // 更新最优解并收集统计数据
            double currentBestFitness = population.getBest().fitness;
            fitnessStats.addValue(currentBestFitness);
            
            if (population.getBest().fitness > bestEver.fitness) {
                double improvement = (population.getBest().fitness - bestEver.fitness) / bestEver.fitness * 100;
                improvementStats.addValue(improvement);
                
                bestEver = new Individual(population.getBest());
                stagnationCount = 0;
                log.debug("第{}代发现更优解，距离: {}, 改进: {:.2f}%", generation, bestEver.distance, improvement);
            } else {
                stagnationCount++;
            }
            
            // 早期停止条件
            if (stagnationCount > 50) {
                log.debug("连续{}代无改进，提前停止", stagnationCount);
                break;
            }
            
            // 种群多样性检查和重启
            if (generation % 100 == 0) {
                double diversity = calculateDiversity(population);
                if (diversity < 0.1) {
                    log.debug("种群多样性过低({})，重新初始化部分个体", diversity);
                    restartPopulation(population, costMatrix, 0.3);
                }
            }
        }
        
        // 第三方库统计分析结果
        long duration = System.currentTimeMillis() - startTime;
        
        log.info("✅ [第三方库成功] Apache Commons Math增强遗传算法求解完成 - 耗时: {}ms, 最优距离: {:.2f}", 
                duration, bestEver.distance);
        
        // 详细统计报告
        if (fitnessStats.getN() > 0) {
            log.info("📊 [Apache Commons Math统计] 适应度分析 - 均值: {:.4f}, 标准差: {:.4f}, 最大值: {:.4f}", 
                    fitnessStats.getMean(), fitnessStats.getStandardDeviation(), fitnessStats.getMax());
        }
        
        if (improvementStats.getN() > 0) {
            log.info("📈 [Apache Commons Math统计] 改进分析 - 平均改进: {:.2f}%, 最大单次改进: {:.2f}%, 改进次数: {}", 
                    improvementStats.getMean(), improvementStats.getMax(), improvementStats.getN());
        }
        
        log.info("🧬 [算法统计] 总代数: {}, 种群大小: {}, 收敛性: {}%", 
                maxGenerations, populationSize, (double)(maxGenerations - stagnationCount) / maxGenerations * 100);
        
        // 转换为聚集区ID序列
        return bestEver.genes.stream()
                .map(i -> cluster.get(i).getAccumulationId())
                .collect(Collectors.toList());
    }
    
    /**
     * 自适应参数调整
     */
    private void adaptParameters(int problemSize) {
        if (problemSize < 30) {
            populationSize = 50;
            maxGenerations = 200;
            mutationRate = 0.15;
        } else if (problemSize < 100) {
            populationSize = 100;
            maxGenerations = 500;
            mutationRate = 0.1;
        } else {
            populationSize = 200;
            maxGenerations = 1000;
            mutationRate = 0.05;
        }
        
        eliteSize = (int) (populationSize * 0.1);
        crossoverRate = 0.8;
        
        log.debug("自适应参数：种群大小={}, 最大代数={}, 变异率={}", 
                 populationSize, maxGenerations, mutationRate);
    }
    
    /**
     * 初始化种群
     */
    private Population initializePopulation(double[][] costMatrix) {
        Population population = new Population(populationSize);
        int nodeCount = costMatrix.length;
        
        // 30%贪心初始化，70%随机初始化
        int greedyCount = (int) (populationSize * 0.3);
        
        for (int i = 0; i < populationSize; i++) {
            Individual individual = new Individual(nodeCount);
            
            if (i < greedyCount) {
                // 贪心初始化（从不同起点开始）
                individual.initializeGreedily(rotateMatrix(costMatrix, i % nodeCount));
            } else {
                // 随机初始化 - 使用Apache Commons Math PRNG
                individual.initializeRandomly(nodeCount, random);
            }
            
            individual.calculateFitness(costMatrix);
            population.add(individual);
        }
        
        return population;
    }
    
    /**
     * 选择操作（锦标赛选择）
     */
    private Population select(Population population) {
        Population parents = new Population(populationSize);
        int tournamentSize = 5;
        
        for (int i = 0; i < populationSize; i++) {
            Individual winner = tournamentSelection(population, tournamentSize);
            parents.add(new Individual(winner));
        }
        
        return parents;
    }
    
    /**
     * 锦标赛选择
     */
    private Individual tournamentSelection(Population population, int tournamentSize) {
        Individual best = null;
        
        for (int i = 0; i < tournamentSize; i++) {
            int randomIndex = random.nextInt(population.size());
            Individual candidate = population.getIndividual(randomIndex);
            
            if (best == null || candidate.fitness > best.fitness) {
                best = candidate;
            }
        }
        
        return best;
    }
    
    /**
     * 交叉操作（顺序交叉OX）
     */
    private Population crossover(Population parents, double[][] costMatrix) {
        Population offspring = new Population(populationSize);
        
        for (int i = 0; i < populationSize; i += 2) {
            Individual parent1 = parents.getIndividual(i);
            Individual parent2 = parents.getIndividual((i + 1) % populationSize);
            
            if (random.nextDouble() < crossoverRate) {
                Individual[] children = orderCrossover(parent1, parent2);
                children[0].calculateFitness(costMatrix);
                children[1].calculateFitness(costMatrix);
                offspring.add(children[0]);
                if (offspring.size() < populationSize) {
                    offspring.add(children[1]);
                }
            } else {
                offspring.add(new Individual(parent1));
                if (offspring.size() < populationSize) {
                    offspring.add(new Individual(parent2));
                }
            }
        }
        
        return offspring;
    }
    
    /**
     * 顺序交叉（OX）
     */
    private Individual[] orderCrossover(Individual parent1, Individual parent2) {
        int length = parent1.genes.size();
        Individual child1 = new Individual(length);
        Individual child2 = new Individual(length);
        
        // 随机选择交叉点 - 使用Apache Commons Math PRNG
        int start = random.nextInt(length);
        int end = random.nextInt(length);
        if (start > end) {
            int temp = start;
            start = end;
            end = temp;
        }
        
        // 初始化子代
        child1.genes = new ArrayList<>(Collections.nCopies(length, -1));
        child2.genes = new ArrayList<>(Collections.nCopies(length, -1));
        
        // 复制中间段
        for (int i = start; i <= end; i++) {
            child1.genes.set(i, parent1.genes.get(i));
            child2.genes.set(i, parent2.genes.get(i));
        }
        
        // 填充剩余位置
        fillOXChild(child1, parent2, end + 1, length);
        fillOXChild(child2, parent1, end + 1, length);
        
        return new Individual[]{child1, child2};
    }
    
    /**
     * 填充OX交叉的剩余位置
     */
    private void fillOXChild(Individual child, Individual parent, int startPos, int length) {
        int childPos = startPos % length;
        int parentPos = startPos % length;
        
        while (child.genes.contains(-1)) {
            int gene = parent.genes.get(parentPos);
            if (!child.genes.contains(gene)) {
                child.genes.set(childPos, gene);
                childPos = (childPos + 1) % length;
            }
            parentPos = (parentPos + 1) % length;
        }
    }
    
    /**
     * 变异操作（多种变异策略）
     */
    private void mutate(Population population, double[][] costMatrix) {
        for (int i = 0; i < population.size(); i++) {
            Individual individual = population.getIndividual(i);
            
            if (random.nextDouble() < mutationRate) {
                // 随机选择变异类型 - 使用Apache Commons Math PRNG
                double mutationType = random.nextDouble();
                
                if (mutationType < 0.4) {
                    // 交换变异
                    swapMutate(individual);
                } else if (mutationType < 0.7) {
                    // 逆转变异
                    inversionMutate(individual);
                } else {
                    // 插入变异
                    insertionMutate(individual);
                }
                
                individual.calculateFitness(costMatrix);
            }
        }
    }
    
    /**
     * 交换变异
     */
    private void swapMutate(Individual individual) {
        int length = individual.genes.size();
        int pos1 = random.nextInt(length);
        int pos2 = random.nextInt(length);
        
        Collections.swap(individual.genes, pos1, pos2);
    }
    
    /**
     * 逆转变异
     */
    private void inversionMutate(Individual individual) {
        int length = individual.genes.size();
        int start = random.nextInt(length);
        int end = random.nextInt(length);
        
        if (start > end) {
            int temp = start;
            start = end;
            end = temp;
        }
        
        while (start < end) {
            Collections.swap(individual.genes, start, end);
            start++;
            end--;
        }
    }
    
    /**
     * 插入变异
     */
    private void insertionMutate(Individual individual) {
        int length = individual.genes.size();
        int from = random.nextInt(length);
        int to = random.nextInt(length);
        
        int gene = individual.genes.remove(from);
        individual.genes.add(to, gene);
    }
    
    /**
     * 局部搜索增强（2-opt）
     */
    private void localSearchEnhancement(Population population, double[][] costMatrix) {
        int enhanceCount = Math.min(10, population.size() / 10);
        List<Individual> candidates = population.individuals.stream()
                .sorted((a, b) -> Double.compare(b.fitness, a.fitness))
                .limit(enhanceCount)
                .collect(Collectors.toList());
        
        for (Individual individual : candidates) {
            twoOptImprove(individual, costMatrix);
        }
    }
    
    /**
     * 2-opt局部搜索
     */
    private void twoOptImprove(Individual individual, double[][] costMatrix) {
        boolean improved = true;
        int maxIterations = 20;
        int iteration = 0;
        
        while (improved && iteration < maxIterations) {
            improved = false;
            iteration++;
            
            for (int i = 0; i < individual.genes.size() - 1; i++) {
                for (int j = i + 2; j < individual.genes.size(); j++) {
                    if (twoOptSwap(individual, i, j, costMatrix)) {
                        improved = true;
                    }
                }
            }
        }
    }
    
    /**
     * 2-opt交换
     */
    private boolean twoOptSwap(Individual individual, int i, int j, double[][] costMatrix) {
        double oldDistance = individual.distance;
        
        // 执行2-opt交换
        Collections.reverse(individual.genes.subList(i + 1, j + 1));
        individual.calculateFitness(costMatrix);
        
        if (individual.distance < oldDistance) {
            return true; // 改进了
        } else {
            // 撤销交换
            Collections.reverse(individual.genes.subList(i + 1, j + 1));
            individual.distance = oldDistance;
            individual.fitness = 1.0 / oldDistance;
            return false;
        }
    }
    
    /**
     * 环境选择（精英策略 + 多样性保持）
     */
    private Population environmentalSelection(Population parents, Population offspring) {
        List<Individual> combined = new ArrayList<>();
        combined.addAll(parents.individuals);
        combined.addAll(offspring.individuals);
        
        // 按适应度排序
        combined.sort((a, b) -> Double.compare(b.fitness, a.fitness));
        
        Population newPopulation = new Population(populationSize);
        
        // 精英选择
        for (int i = 0; i < eliteSize && i < combined.size(); i++) {
            newPopulation.add(new Individual(combined.get(i)));
        }
        
        // 多样性选择填充剩余位置 - 使用Apache Commons Math PRNG
        while (newPopulation.size() < populationSize && combined.size() > eliteSize) {
            Individual candidate = combined.get(eliteSize + random.nextInt(combined.size() - eliteSize));
            if (!isDuplicate(newPopulation, candidate)) {
                newPopulation.add(new Individual(candidate));
            }
            combined.remove(candidate);
        }
        
        // 如果还不够，随机填充 - 使用Apache Commons Math PRNG
        while (newPopulation.size() < populationSize && !combined.isEmpty()) {
            Individual candidate = combined.remove(random.nextInt(combined.size()));
            newPopulation.add(new Individual(candidate));
        }
        
        return newPopulation;
    }
    
    /**
     * 检查个体是否重复
     */
    private boolean isDuplicate(Population population, Individual candidate) {
        return population.individuals.stream()
                .anyMatch(ind -> ind.genes.equals(candidate.genes));
    }
    
    /**
     * 计算种群多样性
     */
    private double calculateDiversity(Population population) {
        double totalDistance = 0.0;
        int comparisons = 0;
        
        for (int i = 0; i < population.size(); i++) {
            for (int j = i + 1; j < population.size(); j++) {
                totalDistance += hammingDistance(
                    population.getIndividual(i), 
                    population.getIndividual(j)
                );
                comparisons++;
            }
        }
        
        return comparisons > 0 ? totalDistance / comparisons / population.getIndividual(0).genes.size() : 0.0;
    }
    
    /**
     * 计算汉明距离
     */
    private double hammingDistance(Individual ind1, Individual ind2) {
        int differences = 0;
        for (int i = 0; i < ind1.genes.size(); i++) {
            if (!ind1.genes.get(i).equals(ind2.genes.get(i))) {
                differences++;
            }
        }
        return (double) differences;
    }
    
    /**
     * 重启部分种群
     */
    private void restartPopulation(Population population, double[][] costMatrix, double restartRatio) {
        int restartCount = (int) (population.size() * restartRatio);
        
        // 保留最优个体
        population.individuals.sort((a, b) -> Double.compare(b.fitness, a.fitness));
        
        // 重新初始化较差的个体 - 使用Apache Commons Math PRNG
        for (int i = population.size() - restartCount; i < population.size(); i++) {
            Individual individual = population.getIndividual(i);
            individual.initializeRandomly(costMatrix.length, random);
            individual.calculateFitness(costMatrix);
        }
    }
    
    /**
     * 构建成本矩阵
     */
    private double[][] buildCostMatrix(TransitDepot depot, List<Accumulation> cluster, 
                                     Map<String, TimeInfo> timeMatrix) {
        int n = cluster.size();
        double[][] matrix = new double[n][n];
        
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    double travelTime = getTravelTime(
                        cluster.get(i).getCoordinate(), 
                        cluster.get(j).getCoordinate(), 
                        timeMatrix
                    );
                    matrix[i][j] = travelTime + cluster.get(j).getDeliveryTime();
                } else {
                    matrix[i][j] = 0.0;
                }
            }
        }
        
        return matrix;
    }
    
    /**
     * 旋转矩阵（用于多起点贪心初始化）
     */
    private double[][] rotateMatrix(double[][] original, int offset) {
        if (offset == 0) return original;
        
        int n = original.length;
        double[][] rotated = new double[n][n];
        
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                int newI = (i + offset) % n;
                int newJ = (j + offset) % n;
                rotated[i][j] = original[newI][newJ];
            }
        }
        
        return rotated;
    }
    
    /**
     * 获取两点间行驶时间
     */
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 0.0;
    }
}