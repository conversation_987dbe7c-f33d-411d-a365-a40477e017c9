package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.domain;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * JSPRIT VRP位置模型
 * 
 * 表示VRP问题中的地理位置，包含坐标信息和位置标识
 * 支持从中转站和聚集区创建位置对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VRPLocation {
    
    /**
     * 位置唯一标识
     */
    private String locationId;
    
    /**
     * 位置名称
     */
    private String locationName;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 位置类型（DEPOT-中转站, ACCUMULATION-聚集区）
     */
    private LocationType locationType;
    
    /**
     * 原始业务对象引用（可选）
     */
    private Object originalObject;
    
    /**
     * 位置类型枚举
     */
    public enum LocationType {
        DEPOT("中转站"),
        ACCUMULATION("聚集区");
        
        private final String description;
        
        LocationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 从中转站创建VRP位置
     * 
     * @param depot 中转站
     * @return VRP位置
     */
    public static VRPLocation fromTransitDepot(TransitDepot depot) {
        if (depot == null) {
            throw new IllegalArgumentException("中转站不能为空");
        }
        
        return VRPLocation.builder()
            .locationId("depot_" + depot.getTransitDepotId())
            .locationName(depot.getTransitDepotName())
            .longitude(depot.getLongitude())
            .latitude(depot.getLatitude())
            .locationType(LocationType.DEPOT)
            .originalObject(depot)
            .build();
    }
    
    /**
     * 从聚集区创建VRP位置
     * 
     * @param accumulation 聚集区
     * @return VRP位置
     */
    public static VRPLocation fromAccumulation(Accumulation accumulation) {
        if (accumulation == null) {
            throw new IllegalArgumentException("聚集区不能为空");
        }
        
        return VRPLocation.builder()
            .locationId("acc_" + accumulation.getAccumulationId())
            .locationName(accumulation.getAccumulationName())
            .longitude(accumulation.getLongitude())
            .latitude(accumulation.getLatitude())
            .locationType(LocationType.ACCUMULATION)
            .originalObject(accumulation)
            .build();
    }
    
    /**
     * 计算到另一个位置的直线距离（公里）
     * 使用Haversine公式计算球面距离
     * 
     * @param otherLocation 目标位置
     * @return 距离（公里）
     */
    public double calculateDistanceTo(VRPLocation otherLocation) {
        if (otherLocation == null || !isValidCoordinates() || !otherLocation.isValidCoordinates()) {
            return 0.0;
        }
        
        return haversineDistance(this.latitude, this.longitude, 
                               otherLocation.latitude, otherLocation.longitude);
    }
    
    /**
     * Haversine公式计算球面距离
     * 
     * @param lat1 起点纬度
     * @param lon1 起点经度
     * @param lat2 终点纬度
     * @param lon2 终点经度
     * @return 距离（公里）
     */
    private double haversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 验证坐标是否有效
     * 
     * @return 坐标是否有效
     */
    public boolean isValidCoordinates() {
        return longitude != null && latitude != null
            && longitude >= -180.0 && longitude <= 180.0
            && latitude >= -90.0 && latitude <= 90.0;
    }
    
    /**
     * 验证位置对象是否有效
     * 
     * @return 位置对象是否有效
     */
    public boolean isValid() {
        return locationId != null && !locationId.trim().isEmpty()
            && locationType != null
            && isValidCoordinates();
    }
    
    /**
     * 检查是否为中转站位置
     * 
     * @return 是否为中转站
     */
    public boolean isDepot() {
        return LocationType.DEPOT.equals(locationType);
    }
    
    /**
     * 检查是否为聚集区位置
     * 
     * @return 是否为聚集区
     */
    public boolean isAccumulation() {
        return LocationType.ACCUMULATION.equals(locationType);
    }
    
    /**
     * 获取原始中转站对象（如果位置类型为DEPOT）
     * 
     * @return 中转站对象，如果不是中转站位置则返回null
     */
    public TransitDepot getOriginalDepot() {
        if (isDepot() && originalObject instanceof TransitDepot) {
            return (TransitDepot) originalObject;
        }
        return null;
    }
    
    /**
     * 获取原始聚集区对象（如果位置类型为ACCUMULATION）
     * 
     * @return 聚集区对象，如果不是聚集区位置则返回null
     */
    public Accumulation getOriginalAccumulation() {
        if (isAccumulation() && originalObject instanceof Accumulation) {
            return (Accumulation) originalObject;
        }
        return null;
    }
    
    /**
     * 创建位置的地理中心点
     * 
     * @param locations 位置列表
     * @return 地理中心点位置
     */
    public static VRPLocation calculateGeographicCenter(VRPLocation... locations) {
        if (locations == null || locations.length == 0) {
            return null;
        }
        
        double avgLat = 0.0;
        double avgLon = 0.0;
        int validCount = 0;
        
        for (VRPLocation location : locations) {
            if (location != null && location.isValidCoordinates()) {
                avgLat += location.latitude;
                avgLon += location.longitude;
                validCount++;
            }
        }
        
        if (validCount == 0) {
            return null;
        }
        
        avgLat /= validCount;
        avgLon /= validCount;
        
        return VRPLocation.builder()
            .locationId("center_" + System.currentTimeMillis())
            .locationName("地理中心点")
            .longitude(avgLon)
            .latitude(avgLat)
            .locationType(LocationType.ACCUMULATION) // 默认为聚集区类型
            .build();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        VRPLocation that = (VRPLocation) obj;
        return locationId != null ? locationId.equals(that.locationId) : that.locationId == null;
    }
    
    @Override
    public int hashCode() {
        return locationId != null ? locationId.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return String.format("VRPLocation{id='%s', name='%s', type=%s, coord=[%.6f,%.6f]}", 
            locationId, locationName, locationType, longitude, latitude);
    }
}