package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.solver;

import com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem;

import java.util.List;

/**
 * 求解器选择策略接口
 * 
 * 定义如何从候选求解器中选择最佳求解器的策略
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
public interface SolverSelectionStrategy {
    
    /**
     * 从候选求解器中选择最佳求解器
     * 
     * @param candidateSolvers 候选求解器列表
     * @param problem MILP问题
     * @param parameters 求解器参数
     * @return 选中的求解器，如果没有合适的则返回null
     */
    MILPSolver selectSolver(List<MILPSolver> candidateSolvers, 
                           MILPProblem problem, 
                           SolverParameters parameters);
    
    /**
     * 对候选求解器按优先级排序
     * 
     * @param candidateSolvers 候选求解器列表
     * @param problem MILP问题
     * @param parameters 求解器参数
     * @return 按优先级排序的求解器列表（高优先级在前）
     */
    List<MILPSolver> rankSolvers(List<MILPSolver> candidateSolvers, 
                                MILPProblem problem, 
                                SolverParameters parameters);
}