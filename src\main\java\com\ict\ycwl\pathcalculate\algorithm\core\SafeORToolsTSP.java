package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 安全的OR-Tools TSP求解器
 * 使用JNIFixService进行预修复，避免类初始化失败问题
 */
@Slf4j
@Component
public class SafeORToolsTSP implements TSPSolver {
    
    private final ManualORToolsTSP manualORToolsSolver;
    private final EnhancedGeneticTSP fallbackSolver;
    
    /**
     * 构造器 - 委托给ManualORToolsTSP
     */
    public SafeORToolsTSP() {
        this.fallbackSolver = new EnhancedGeneticTSP();
        this.manualORToolsSolver = new ManualORToolsTSP();
        
        log.info("🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现");
        log.info("🎯 [SafeORToolsTSP] OR-Tools状态: {}", manualORToolsSolver.isORToolsAvailable() ? "可用" : "不可用");
    }
    
    /**
     * 带降级算法的构造器
     */
    public SafeORToolsTSP(EnhancedGeneticTSP fallbackSolver) {
        this.fallbackSolver = fallbackSolver;
        this.manualORToolsSolver = new ManualORToolsTSP();
        
        log.info("🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）");
    }
    
    /**
     * OR-Tools TSP求解 - 委托给ManualORToolsTSP
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        log.debug("开始SafeORTools TSP求解（委托给ManualORToolsTSP），节点数: {}, 时间限制: {}ms", cluster.size(), timeLimitMs);
        
        // 直接委托给ManualORToolsTSP，它有自己的降级机制
        return manualORToolsSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
    }
    
    
    /**
     * 检查OR-Tools是否可用 - 委托给ManualORToolsTSP
     */
    public boolean isORToolsAvailable() {
        return manualORToolsSolver.isORToolsAvailable();
    }
    
    /**
     * 强制重新检查OR-Tools可用性 - 委托给ManualORToolsTSP
     */
    public void recheckAvailability() {
        log.info("强制重新检查OR-Tools可用性（委托给ManualORToolsTSP）...");
        
        // 重新创建ManualORToolsTSP实例来强制重新检查
        // 注意：由于JNI库无法从JVM中卸载，重新检查可能有限
        log.info("当前OR-Tools状态: {}", manualORToolsSolver.isORToolsAvailable() ? "可用" : "不可用");
        log.info("ManualORToolsTSP状态: {}", manualORToolsSolver.getLoadStatus());
    }
}