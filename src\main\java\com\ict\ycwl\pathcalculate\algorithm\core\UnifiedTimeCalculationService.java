package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 统一时间计算服务
 * 解决聚类阶段与TSP阶段时间计算不一致问题
 * 
 * 统一原则：
 * 1. 所有阶段使用相同的时间计算逻辑
 * 2. 优先使用精确的时间矩阵数据
 * 3. 降级到地理距离估算时使用一致的系数
 * 4. 统一的点权（配送时间）和边权（行驶时间）处理
 */
@Slf4j
@Component
public class UnifiedTimeCalculationService {
    
    // 统一时间计算参数
    private static final double LOADING_TIME_MINUTES = AlgorithmParameters.LOADING_TIME_MINUTES;  // 装车时间
    private static final double DISTANCE_TO_TIME_FACTOR = 2.0;      // 每公里转换时间系数（分钟）
    private static final double EARTH_RADIUS_KM = 6371.0;           // 地球半径
    private static final double INTRA_CLUSTER_FACTOR = 1.2;         // 聚类内部行驶系数
    
    // 时间矩阵缓存
    private Map<String, Double> timeMatrixCache = new HashMap<>();
    
    /**
     * 计算路线总工作时间（聚类和TSP阶段统一使用）
     * @param accumulations 聚集区列表
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 总工作时间（分钟）
     */
    public double calculateRouteWorkTime(List<Accumulation> accumulations, TransitDepot depot, 
                                       Map<String, Map<String, TimeInfo>> timeMatrix) {
        
        if (accumulations == null || accumulations.isEmpty()) {
            return LOADING_TIME_MINUTES;
        }
        
        double totalTime = LOADING_TIME_MINUTES;  // 装车时间
        CoordinatePoint currentPos = depot.getCoordinate();
        
        // 累计每个聚集区的配送时间和行驶时间
        for (Accumulation acc : accumulations) {
            // 1. 行驶时间：当前位置到聚集区
            double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
            totalTime += travelTime;
            
            // 2. 配送时间（点权）
            totalTime += acc.getDeliveryTime();
            
            // 更新当前位置
            currentPos = acc.getCoordinate();
        }
        
        // 3. 返回中转站的行驶时间
        double returnTime = getTravelTime(currentPos, depot.getCoordinate(), timeMatrix);
        totalTime += returnTime;
        
        return totalTime;
    }
    
    /**
     * 获取两点间行驶时间（优先使用时间矩阵，降级到地理距离估算）
     * @param from 起点坐标
     * @param to 终点坐标
     * @param timeMatrix 时间矩阵
     * @return 行驶时间（分钟）
     */
    public double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, Map<String, TimeInfo>> timeMatrix) {
        
        // 生成缓存键
        String cacheKey = generateCacheKey(from, to);
        
        // 检查缓存
        if (timeMatrixCache.containsKey(cacheKey)) {
            return timeMatrixCache.get(cacheKey);
        }
        
        double travelTime = 0.0;
        
        // 1. 优先尝试使用时间矩阵
        if (timeMatrix != null) {
            TimeInfo timeInfo = getTimeInfo(from, to, timeMatrix);
            if (timeInfo != null) {
                travelTime = timeInfo.getTravelTime();
                timeMatrixCache.put(cacheKey, travelTime);
                return travelTime;
            }
        }
        
        // 2. 降级到地理距离估算
        double distance = calculateEuclideanDistance(from, to);
        travelTime = distance * DISTANCE_TO_TIME_FACTOR;
        
        // 缓存结果
        timeMatrixCache.put(cacheKey, travelTime);
        
        return travelTime;
    }
    
    /**
     * 从时间矩阵获取时间信息
     */
    private TimeInfo getTimeInfo(CoordinatePoint from, CoordinatePoint to, 
                                Map<String, Map<String, TimeInfo>> timeMatrix) {
        
        // 生成坐标键（保留6位小数精度）
        String fromKey = String.format("%.6f,%.6f", from.getLongitude(), from.getLatitude());
        String toKey = String.format("%.6f,%.6f", to.getLongitude(), to.getLatitude());
        
        // 查询时间矩阵
        Map<String, TimeInfo> fromMap = timeMatrix.get(fromKey);
        if (fromMap != null) {
            TimeInfo timeInfo = fromMap.get(toKey);
            if (timeInfo != null) {
                return timeInfo;
            }
        }
        
        // 尝试反向查询
        Map<String, TimeInfo> toMap = timeMatrix.get(toKey);
        if (toMap != null) {
            TimeInfo timeInfo = toMap.get(fromKey);
            if (timeInfo != null) {
                return timeInfo;
            }
        }
        
        return null;
    }
    
    /**
     * 计算聚类工作时间（替代原有的简化估算）
     * @param cluster 聚类
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @return 聚类总工作时间（分钟）
     */
    public double calculateClusterWorkTime(List<Accumulation> cluster, TransitDepot depot, 
                                         Map<String, Map<String, TimeInfo>> timeMatrix) {
        
        if (cluster == null || cluster.isEmpty()) {
            return LOADING_TIME_MINUTES;
        }
        
        // 使用统一的路线时间计算逻辑
        // 注意：这里不进行TSP优化，而是使用简单的聚类内部连接估算
        double totalTime = LOADING_TIME_MINUTES;
        
        // 1. 计算所有聚集区的配送时间（点权）
        double deliveryTime = cluster.stream()
            .mapToDouble(Accumulation::getDeliveryTime)
            .sum();
        totalTime += deliveryTime;
        
        // 2. 估算行驶时间
        // 从中转站到聚类中心的往返时间
        CoordinatePoint clusterCenter = calculateClusterCenter(cluster);
        double depotToClusterTime = getTravelTime(depot.getCoordinate(), clusterCenter, timeMatrix) * 2; // 往返
        totalTime += depotToClusterTime;
        
        // 聚类内部行驶时间估算
        double intraClusterTime = estimateIntraClusterTravelTime(cluster, timeMatrix);
        totalTime += intraClusterTime;
        
        return totalTime;
    }
    
    /**
     * 计算聚类中心坐标
     */
    private CoordinatePoint calculateClusterCenter(List<Accumulation> cluster) {
        double avgLat = cluster.stream().mapToDouble(acc -> acc.getLatitude()).average().orElse(0.0);
        double avgLon = cluster.stream().mapToDouble(acc -> acc.getLongitude()).average().orElse(0.0);
        
        CoordinatePoint center = new CoordinatePoint();
        center.setLatitude(avgLat);
        center.setLongitude(avgLon);
        return center;
    }
    
    /**
     * 估算聚类内部行驶时间
     */
    private double estimateIntraClusterTravelTime(List<Accumulation> cluster, 
                                                Map<String, Map<String, TimeInfo>> timeMatrix) {
        
        if (cluster.size() <= 1) {
            return 0.0;
        }
        
        // 计算聚类内平均距离
        double totalDistance = 0.0;
        int pairCount = 0;
        
        for (int i = 0; i < cluster.size(); i++) {
            for (int j = i + 1; j < cluster.size(); j++) {
                double distance = calculateEuclideanDistance(
                    cluster.get(i).getCoordinate(), 
                    cluster.get(j).getCoordinate()
                );
                totalDistance += distance;
                pairCount++;
            }
        }
        
        double avgDistance = pairCount > 0 ? totalDistance / pairCount : 0.0;
        
        // 聚类内部行驶时间 = 平均距离 * 节点数 * 内部系数
        return avgDistance * cluster.size() * INTRA_CLUSTER_FACTOR * DISTANCE_TO_TIME_FACTOR;
    }
    
    /**
     * 计算欧几里得距离（公里）
     */
    private double calculateEuclideanDistance(CoordinatePoint p1, CoordinatePoint p2) {
        double dLat = Math.toRadians(p2.getLatitude() - p1.getLatitude());
        double dLon = Math.toRadians(p2.getLongitude() - p1.getLongitude());
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(Math.toRadians(p1.getLatitude())) * Math.cos(Math.toRadians(p2.getLatitude())) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return EARTH_RADIUS_KM * c;
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(CoordinatePoint from, CoordinatePoint to) {
        // 确保键的一致性：较小的坐标在前
        double lat1 = from.getLatitude();
        double lon1 = from.getLongitude();
        double lat2 = to.getLatitude();
        double lon2 = to.getLongitude();
        
        if (lat1 > lat2 || (lat1 == lat2 && lon1 > lon2)) {
            // 交换顺序
            double tempLat = lat1;
            double tempLon = lon1;
            lat1 = lat2;
            lon1 = lon2;
            lat2 = tempLat;
            lon2 = tempLon;
        }
        
        return String.format("%.6f,%.6f->%.6f,%.6f", lon1, lat1, lon2, lat2);
    }
    
    /**
     * 验证路线时间计算的一致性
     * @param routes 路线列表
     * @param depot 中转站
     * @param timeMatrix 时间矩阵
     * @param context 算法上下文
     */
    public void validateTimeCalculationConsistency(List<RouteResult> routes, TransitDepot depot,
                                                 Map<String, Map<String, TimeInfo>> timeMatrix,
                                                 AlgorithmContext context) {
        
        log.info("🔍 [时间计算验证] 检查中转站{}的时间计算一致性", depot.getTransitDepotName());
        
        for (RouteResult route : routes) {
            List<Long> sequence = route.getAccumulationSequence();
            
            if (sequence.isEmpty()) {
                continue;
            }
            
            // 重新计算时间
            List<Accumulation> accumulations = sequence.stream()
                .map(context::getAccumulationById)
                .filter(Objects::nonNull)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            
            double recalculatedTime = calculateRouteWorkTime(accumulations, depot, timeMatrix);
            double currentTime = route.getTotalWorkTime();
            double timeDifference = Math.abs(recalculatedTime - currentTime);
            
            if (timeDifference > 5.0) { // 5分钟容忍度
                log.warn("⚠️ [时间不一致] 路线{}: 当前时间{:.1f}分钟, 重算时间{:.1f}分钟, 差异{:.1f}分钟",
                    route.getRouteName(), currentTime, recalculatedTime, timeDifference);
                
                // 修正时间
                route.setTotalWorkTime(recalculatedTime);
                log.info("🔧 [时间修正] 路线{}时间已修正为{:.1f}分钟", route.getRouteName(), recalculatedTime);
            } else {
                log.debug("✅ [时间一致] 路线{}: {:.1f}分钟", route.getRouteName(), currentTime);
            }
        }
    }
    
    /**
     * 批量验证所有中转站的时间计算一致性
     */
    public void validateAllTimeCalculations(AlgorithmContext context) {
        log.info("🔍 [全局时间验证] 开始验证所有中转站的时间计算一致性");
        
        int totalInconsistencies = 0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            int routesBefore = (int) routes.stream()
                .filter(route -> route.getTotalWorkTime() > AlgorithmParameters.TARGET_ROUTE_TIME)
                .count();
            
            validateTimeCalculationConsistency(routes, depot, null, context); // 暂时传null，使用地理距离估算
            
            int routesAfter = (int) routes.stream()
                .filter(route -> route.getTotalWorkTime() > AlgorithmParameters.TARGET_ROUTE_TIME)
                .count();
            
            if (routesBefore != routesAfter) {
                totalInconsistencies++;
                log.info("🔧 [时间修正影响] 中转站{}: 超时路线数量 {} -> {}", 
                    depot.getTransitDepotName(), routesBefore, routesAfter);
            }
        }
        
        if (totalInconsistencies == 0) {
            log.info("✅ [验证完成] 所有时间计算已保持一致性");
        } else {
            log.info("🔧 [验证完成] 修正了{}个中转站的时间计算不一致问题", totalInconsistencies);
        }
    }
    
    /**
     * 清理缓存（在算法开始时调用）
     */
    public void clearCache() {
        timeMatrixCache.clear();
        log.debug("🗑️ [缓存清理] 时间计算缓存已清理");
    }
    
    /**
     * 获取缓存统计信息
     */
    public void logCacheStats() {
        log.info("📊 [缓存统计] 时间矩阵缓存大小: {}", timeMatrixCache.size());
    }
}