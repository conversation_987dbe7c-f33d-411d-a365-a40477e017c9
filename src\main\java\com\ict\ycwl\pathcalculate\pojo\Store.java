package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("store")
@Builder
public class Store {

    @TableId(type = IdType.ASSIGN_ID)
    private Long storeId;

    private String customerCode;

    private String storeName;

    private String storeAddress;

    private Double longitude;

    private Double latitude;

    private String type;

    private String orderCycle;

    private String district;

    private String areaName;

    private String contactName;

    private String contactPhone;

    private String status;

    private Long customerManagerId;

    private String customerManagerName;

    private Long accumulationId;

    private Long areaId;

    private Long routeId;

    private String routeName;

    private String isDelete;

    private Timestamp createTime;

    private Timestamp updateTime;

    private String gear;


    private String locationType;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 班组Id
     */
    private Long groupId;
}
