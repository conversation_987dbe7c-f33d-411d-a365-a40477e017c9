package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

/**
 * 业内标准降级策略枚举
 * 
 * 定义了当主要算法失效时可用的降级优化策略
 * 基于业界验证的启发式算法，确保系统鲁棒性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
public enum FallbackStrategy {
    
    /**
     * 模拟退火算法
     * 
     * 特点：
     * - 全局优化能力强
     * - 能跳出局部最优
     * - 适合大规模复杂问题
     * - 主要降级方案
     */
    SIMULATED_ANNEALING("模拟退火算法", "SA", 1.0, 
        "基于物理退火过程的全局优化算法，能够接受部分劣解以跳出局部最优"),
    
    /**
     * 遗传算法
     * 
     * 特点：
     * - 群体搜索策略
     * - 并行优化能力
     * - 适合大规模问题
     * - 备用降级方案
     */
    GENETIC_ALGORITHM("遗传算法", "GA", 0.9, 
        "模拟生物进化过程的群体智能优化算法，通过选择、交叉、变异等操作寻找最优解"),
    
    /**
     * 变邻域搜索
     * 
     * 特点：
     * - 多邻域结构
     * - 局部搜索增强
     * - 适合中等规模问题
     * - 局部优化专用
     */
    VARIABLE_NEIGHBORHOOD_SEARCH("变邻域搜索", "VNS", 0.8, 
        "系统性改变邻域结构的局部搜索算法，在多个邻域中寻找改进解"),
    
    /**
     * 局部搜索
     * 
     * 特点：
     * - 计算效率高
     * - 收敛速度快
     * - 适合小规模问题
     * - 快速修复方案
     */
    LOCAL_SEARCH("局部搜索", "LS", 0.7, 
        "在当前解的邻域中寻找改进解的简单高效算法，适用于快速局部优化"),
    
    /**
     * 混合策略
     * 
     * 特点：
     * - 多算法组合
     * - 分阶段优化
     * - 综合效果最优
     * - 推荐策略
     */
    HYBRID("混合策略", "HYBRID", 1.2, 
        "组合多种算法的分阶段优化策略：SA全局搜索 + VNS局部改进 + LS精细调优");
    
    // 枚举属性
    private final String name;
    private final String code;
    private final double effectiveness;
    private final String description;
    
    /**
     * 构造函数
     */
    FallbackStrategy(String name, String code, double effectiveness, String description) {
        this.name = name;
        this.code = code;
        this.effectiveness = effectiveness;
        this.description = description;
    }
    
    /**
     * 获取策略名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取策略代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取策略有效性评分
     * 
     * @return 有效性评分（0.0-2.0，越高越有效）
     */
    public double getEffectiveness() {
        return effectiveness;
    }
    
    /**
     * 获取策略描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为高级策略
     * 
     * @return 有效性评分 >= 1.0为高级策略
     */
    public boolean isAdvancedStrategy() {
        return effectiveness >= 1.0;
    }
    
    /**
     * 获取推荐的时间限制（秒）
     */
    public int getRecommendedTimeLimit() {
        switch (this) {
            case SIMULATED_ANNEALING:
                return 120; // 2分钟
            case GENETIC_ALGORITHM:
                return 180; // 3分钟
            case VARIABLE_NEIGHBORHOOD_SEARCH:
                return 90;  // 1.5分钟
            case LOCAL_SEARCH:
                return 30;  // 30秒
            case HYBRID:
                return 240; // 4分钟
            default:
                return 60;  // 默认1分钟
        }
    }
    
    /**
     * 获取适用的问题规模范围
     */
    public String getApplicableScale() {
        switch (this) {
            case SIMULATED_ANNEALING:
                return "大规模(>50路线)";
            case GENETIC_ALGORITHM:
                return "大规模(>50路线)";
            case VARIABLE_NEIGHBORHOOD_SEARCH:
                return "中等规模(20-50路线)";
            case LOCAL_SEARCH:
                return "小规模(<20路线)";
            case HYBRID:
                return "任意规模";
            default:
                return "未定义";
        }
    }
    
    /**
     * 根据问题特征推荐最佳策略
     */
    public static FallbackStrategy recommendStrategy(int routeCount, double constraintViolationRate, double timeVariance) {
        // 大规模问题：优先遗传算法
        if (routeCount > 50) {
            return GENETIC_ALGORITHM;
        }
        
        // 高约束违反率：优先模拟退火
        if (constraintViolationRate > 0.3) {
            return SIMULATED_ANNEALING;
        }
        
        // 高时间方差：优先变邻域搜索
        if (timeVariance > 10000) {
            return VARIABLE_NEIGHBORHOOD_SEARCH;
        }
        
        // 默认情况：混合策略
        return HYBRID;
    }
    
    /**
     * 生成策略摘要信息
     */
    public String generateSummary() {
        return String.format("%s(%s) | 有效性:%.1f | 适用:%s | 时限:%ds", 
            name, code, effectiveness, getApplicableScale(), getRecommendedTimeLimit());
    }
    
    @Override
    public String toString() {
        return String.format("%s[%s]", name, code);
    }
}