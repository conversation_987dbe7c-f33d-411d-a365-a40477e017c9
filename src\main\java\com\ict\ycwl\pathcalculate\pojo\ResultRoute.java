package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultRoute {

    @TableId(type = IdType.ASSIGN_ID)
    private Long routeId;

    private String routeName;

    private String distance;

    private Long transitDepotId;

    private Long groupId;

    private Long areaId;

    private List<LngAndLat> polyline;

    private Timestamp createTime;

    private Timestamp updateTime;

    private boolean isDelete;

    private String cargoWeight;

    private Long versionId;

    private BigDecimal workTime;

    private List<LngAndLat> convex;

    //装车时长
    private String loadingTime;
    //途中时长
    private String transitTime;
    //卸货时长
    private String deliveryTime;

    //高速公路里程km
    private double freeewatDist;
    //城区公路行驶里程km
    private double urabanRoadsDist;
    //乡镇公路行驶里程km
    private double townshipRoadsDist;
    //二次中转站时长分钟
    private double secondTransitTime;


}
