package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

/**
 * 路线数量调整行动枚举
 * 
 * 定义路线数量评估后的推荐调整行动
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
public enum RouteCountAction {
    
    /**
     * 增加路线数量
     */
    INCREASE("增加路线", 1, "当前路线负载过重，建议增加路线数量"),
    
    /**
     * 减少路线数量
     */
    DECREASE("减少路线", -1, "当前路线利用率不足，建议减少路线数量"),
    
    /**
     * 保持当前数量
     */
    MAINTAIN("保持现状", 0, "当前路线数量接近最优，建议保持不变");
    
    private final String description;
    private final int direction;
    private final String reasoning;
    
    RouteCountAction(String description, int direction, String reasoning) {
        this.description = description;
        this.direction = direction;
        this.reasoning = reasoning;
    }
    
    /**
     * 获取行动描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取调整方向 (-1: 减少, 0: 保持, 1: 增加)
     */
    public int getDirection() {
        return direction;
    }
    
    /**
     * 获取推荐理由
     */
    public String getReasoning() {
        return reasoning;
    }
    
    /**
     * 是否需要调整
     */
    public boolean needsAdjustment() {
        return this != MAINTAIN;
    }
    
    /**
     * 是否是增加操作
     */
    public boolean isIncrease() {
        return this == INCREASE;
    }
    
    /**
     * 是否是减少操作
     */
    public boolean isDecrease() {
        return this == DECREASE;
    }
    
    /**
     * 根据数值方向获取对应的行动
     */
    public static RouteCountAction fromDirection(int direction) {
        if (direction > 0) {
            return INCREASE;
        } else if (direction < 0) {
            return DECREASE;
        } else {
            return MAINTAIN;
        }
    }
    
    /**
     * 根据路线数量差距获取推荐行动
     */
    public static RouteCountAction fromGap(double gap) {
        if (gap > 1.5) {
            return DECREASE; // 当前路线太多
        } else if (gap < -1.5) {
            return INCREASE; // 当前路线太少
        } else {
            return MAINTAIN; // 数量合适
        }
    }
}