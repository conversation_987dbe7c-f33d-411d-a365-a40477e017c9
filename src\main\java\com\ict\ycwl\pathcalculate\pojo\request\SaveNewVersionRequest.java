package com.ict.ycwl.pathcalculate.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("另村为新版本表单")
public class SaveNewVersionRequest {
    //版本名
    @ApiModelProperty(value = "版本名",dataType = "String",required = true)
    private String VersionName;
    //版本备注
    @ApiModelProperty(value = "版本备注",dataType = "String",required = false)
    private String remake;
}
