package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分支定界TSP求解器 - 第三方库优先版本
 * 优先使用OR-Tools约束求解，降级时使用自实现分支定界算法
 * 用于中等规模TSP问题（13-25节点）
 */
@Slf4j
@Component
public class BranchAndBoundTSP {
    
    private final SafeORToolsTSP orToolsSolver;
    
    // 分支定界状态节点
    private static class TSPNode {
        List<Integer> path;           // 当前路径
        boolean[] visited;            // 访问标记
        double currentCost;           // 当前成本
        double lowerBound;            // 下界估计
        int level;                    // 搜索深度
        
        public TSPNode(int size) {
            this.path = new ArrayList<>();
            this.visited = new boolean[size];
            this.currentCost = 0.0;
            this.lowerBound = 0.0;
            this.level = 0;
        }
        
        public TSPNode(TSPNode parent) {
            this.path = new ArrayList<>(parent.path);
            this.visited = parent.visited.clone();
            this.currentCost = parent.currentCost;
            this.lowerBound = parent.lowerBound;
            this.level = parent.level;
        }
    }
    
    private double bestCost = Double.MAX_VALUE;
    private List<Integer> bestPath = new ArrayList<>();
    private long startTime;
    private long timeLimit;
    private double[][] costMatrix;
    private int nodeCount;
    
    /**
     * 无参构造器
     */
    public BranchAndBoundTSP() {
        this.orToolsSolver = new SafeORToolsTSP();
        log.info("🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用");
    }
    
    /**
     * 依赖注入构造器
     */
    public BranchAndBoundTSP(SafeORToolsTSP orToolsSolver) {
        this.orToolsSolver = orToolsSolver;
        log.info("🧮 BranchAndBoundTSP初始化 - 使用注入的OR-Tools求解器");
    }
    
    /**
     * 分支定界求解TSP - 第三方库优先版本
     * 
     * @param depot 中转站
     * @param cluster 聚集区列表
     * @param timeMatrix 时间矩阵
     * @param timeLimitMs 时间限制（毫秒）
     * @return 优化后的聚集区访问顺序
     */
    public List<Long> solve(TransitDepot depot, List<Accumulation> cluster, 
                           Map<String, TimeInfo> timeMatrix, long timeLimitMs) {
        
        log.info("🧮 [第三方库优先] 开始分支定界TSP求解 - 节点数: {}, 时间限制: {}ms", cluster.size(), timeLimitMs);
        long startTime = System.currentTimeMillis();
        
        if (cluster.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (cluster.size() == 1) {
            return Arrays.asList(cluster.get(0).getAccumulationId());
        }
        
        // 第三方库优先策略：OR-Tools可用时优先使用
        if (orToolsSolver.isORToolsAvailable() && cluster.size() <= 50) {
            try {
                log.info("🚀 [第三方库调用] 优先使用OR-Tools约束求解器 - Google OR-Tools 9.8.3296");
                List<Long> orToolsResult = orToolsSolver.solve(depot, cluster, timeMatrix, timeLimitMs);
                
                long duration = System.currentTimeMillis() - startTime;
                log.info("✅ [第三方库成功] OR-Tools分支定界求解完成 - 耗时: {}ms, 解质量: {} 节点", 
                        duration, orToolsResult.size());
                
                return orToolsResult;
            } catch (Exception e) {
                log.warn("❌ [第三方库失败] OR-Tools求解失败，降级到自实现分支定界 - 错误: {}", e.getMessage());
            }
        } else if (!orToolsSolver.isORToolsAvailable()) {
            log.info("⚠️ [算法降级] OR-Tools不可用，使用自实现分支定界算法");
        } else {
            log.info("⚠️ [算法降级] 节点数{}超过OR-Tools推荐上限，使用自实现分支定界算法", cluster.size());
        }
        
        // 降级到自实现分支定界算法
        log.info("🔧 [自实现算法] 开始经典分支定界算法求解");
        
        this.startTime = System.currentTimeMillis();
        this.timeLimit = timeLimitMs;
        this.nodeCount = cluster.size();
        this.bestCost = Double.MAX_VALUE;
        this.bestPath.clear();
        
        // 构建成本矩阵
        this.costMatrix = buildCostMatrix(depot, cluster, timeMatrix);
        
        // 使用贪心算法获得初始上界
        double greedyUpperBound = calculateGreedyUpperBound();
        this.bestCost = greedyUpperBound;
        
        log.debug("初始上界（贪心算法）: {}", greedyUpperBound);
        
        // 创建根节点
        TSPNode root = new TSPNode(nodeCount);
        root.path.add(0); // 从第一个节点开始
        root.visited[0] = true;
        root.level = 1;
        root.lowerBound = calculateLowerBound(root);
        
        // 使用优先队列实现最佳优先搜索
        PriorityQueue<TSPNode> queue = new PriorityQueue<>(
            Comparator.comparingDouble(node -> node.lowerBound)
        );
        queue.offer(root);
        
        int exploredNodes = 0;
        int prunedNodes = 0;
        
        while (!queue.isEmpty() && !isTimeExpired()) {
            TSPNode current = queue.poll();
            exploredNodes++;
            
            // 剪枝：下界超过当前最优解
            if (current.lowerBound >= bestCost) {
                prunedNodes++;
                continue;
            }
            
            // 叶节点：找到完整路径
            if (current.level == nodeCount) {
                double totalCost = current.currentCost + costMatrix[current.path.get(nodeCount - 1)][0];
                if (totalCost < bestCost) {
                    bestCost = totalCost;
                    bestPath = new ArrayList<>(current.path);
                    log.debug("找到更优解，成本: {}", bestCost);
                }
                continue;
            }
            
            // 分支：扩展所有可能的下一个节点
            for (int nextNode = 0; nextNode < nodeCount; nextNode++) {
                if (!current.visited[nextNode]) {
                    TSPNode child = new TSPNode(current);
                    
                    int lastNode = current.path.get(current.level - 1);
                    child.currentCost += costMatrix[lastNode][nextNode];
                    child.path.add(nextNode);
                    child.visited[nextNode] = true;
                    child.level++;
                    
                    // 计算下界
                    child.lowerBound = calculateLowerBound(child);
                    
                    // 剪枝检查
                    if (child.lowerBound < bestCost) {
                        queue.offer(child);
                    } else {
                        prunedNodes++;
                    }
                }
            }
        }
        
        long selfImplementedDuration = System.currentTimeMillis() - this.startTime;
        double pruningEfficiency = prunedNodes > 0 ? (double) prunedNodes / (exploredNodes + prunedNodes) * 100 : 0;
        
        log.info("✅ [自实现算法成功] 分支定界算法求解完成 - 耗时: {}ms, 最优成本: {:.2f}", 
                selfImplementedDuration, bestCost);
        log.info("🔍 [算法统计] 探索节点: {}, 剪枝节点: {}, 剪枝效率: {:.1f}%", 
                exploredNodes, prunedNodes, pruningEfficiency);
        
        // 转换为聚集区ID序列
        if (bestPath.isEmpty()) {
            log.warn("⚠️ [算法失败] 分支定界未找到解，最终降级到贪心算法");
            List<Long> greedyResult = solveWithGreedy(depot, cluster, timeMatrix);
            log.info("🔄 [最终降级] 贪心算法完成 - 解质量: {} 节点", greedyResult.size());
            return greedyResult;
        }
        
        List<Long> result = bestPath.stream()
                .map(i -> cluster.get(i).getAccumulationId())
                .collect(Collectors.toList());
        
        log.info("🎯 [算法结果] 分支定界找到最优解 - 解质量: {} 节点, 路径: {}", 
                result.size(), result.size() <= 10 ? result : "[" + result.size() + " 节点]");
        
        return result;
    }
    
    /**
     * 构建成本矩阵
     */
    private double[][] buildCostMatrix(TransitDepot depot, List<Accumulation> cluster, 
                                     Map<String, TimeInfo> timeMatrix) {
        
        int n = cluster.size();
        double[][] matrix = new double[n][n];
        
        // 聚集区之间的成本
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i != j) {
                    double travelTime = getTravelTime(
                        cluster.get(i).getCoordinate(), 
                        cluster.get(j).getCoordinate(), 
                        timeMatrix
                    );
                    matrix[i][j] = travelTime + cluster.get(j).getDeliveryTime();
                } else {
                    matrix[i][j] = Double.MAX_VALUE; // 禁止自环
                }
            }
        }
        
        return matrix;
    }
    
    /**
     * 计算下界估计
     * 使用最小生成树下界 + 最小出度下界
     */
    private double calculateLowerBound(TSPNode node) {
        double lowerBound = node.currentCost;
        
        // 方法1：最小出度下界
        // 对每个未访问节点，加上其最小出边成本
        for (int i = 0; i < nodeCount; i++) {
            if (!node.visited[i]) {
                double minOutEdge = Double.MAX_VALUE;
                for (int j = 0; j < nodeCount; j++) {
                    if (i != j && costMatrix[i][j] < minOutEdge) {
                        minOutEdge = costMatrix[i][j];
                    }
                }
                if (minOutEdge != Double.MAX_VALUE) {
                    lowerBound += minOutEdge;
                }
            }
        }
        
        // 方法2：回到起点的最小成本
        if (node.level > 1) {
            int lastNode = node.path.get(node.level - 1);
            lowerBound += costMatrix[lastNode][0];
        }
        
        return lowerBound * 0.5; // 调整系数，避免下界过松
    }
    
    /**
     * 使用贪心算法计算初始上界
     */
    private double calculateGreedyUpperBound() {
        boolean[] visited = new boolean[nodeCount];
        List<Integer> greedyPath = new ArrayList<>();
        
        int current = 0;
        greedyPath.add(current);
        visited[current] = true;
        
        double totalCost = 0.0;
        
        for (int step = 1; step < nodeCount; step++) {
            int nearest = -1;
            double minCost = Double.MAX_VALUE;
            
            for (int next = 0; next < nodeCount; next++) {
                if (!visited[next] && costMatrix[current][next] < minCost) {
                    minCost = costMatrix[current][next];
                    nearest = next;
                }
            }
            
            if (nearest != -1) {
                totalCost += minCost;
                greedyPath.add(nearest);
                visited[nearest] = true;
                current = nearest;
            }
        }
        
        // 回到起点
        totalCost += costMatrix[current][0];
        
        // 保存贪心解作为初始最优解
        if (bestPath.isEmpty()) {
            bestPath = new ArrayList<>(greedyPath);
        }
        
        return totalCost;
    }
    
    /**
     * 检查是否超时
     */
    private boolean isTimeExpired() {
        return System.currentTimeMillis() - startTime > timeLimit;
    }
    
    /**
     * 降级到贪心算法
     */
    private List<Long> solveWithGreedy(TransitDepot depot, List<Accumulation> cluster, 
                                     Map<String, TimeInfo> timeMatrix) {
        
        List<Long> sequence = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        CoordinatePoint currentPos = depot.getCoordinate();
        
        while (visited.size() < cluster.size()) {
            Accumulation nearest = null;
            double minCost = Double.MAX_VALUE;
            
            for (Accumulation acc : cluster) {
                if (visited.contains(acc.getAccumulationId())) continue;
                
                double travelTime = getTravelTime(currentPos, acc.getCoordinate(), timeMatrix);
                double totalCost = travelTime + acc.getDeliveryTime();
                
                if (totalCost < minCost) {
                    minCost = totalCost;
                    nearest = acc;
                }
            }
            
            if (nearest != null) {
                sequence.add(nearest.getAccumulationId());
                visited.add(nearest.getAccumulationId());
                currentPos = nearest.getCoordinate();
            } else {
                break;
            }
        }
        
        return sequence;
    }
    
    /**
     * 获取两点间行驶时间
     */
    private double getTravelTime(CoordinatePoint from, CoordinatePoint to, 
                               Map<String, TimeInfo> timeMatrix) {
        String key = String.format("%.6f,%.6f->%.6f,%.6f", 
                from.getLongitude(), from.getLatitude(),
                to.getLongitude(), to.getLatitude());
        
        TimeInfo timeInfo = timeMatrix.get(key);
        return timeInfo != null ? timeInfo.getTravelTime() : 0.0;
    }
}