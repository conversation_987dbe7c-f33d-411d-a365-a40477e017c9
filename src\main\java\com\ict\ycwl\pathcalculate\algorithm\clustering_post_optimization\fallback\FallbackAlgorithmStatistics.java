package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 降级算法统计信息
 * 
 * 跟踪降级算法的执行统计，包括成功率、平均执行时间、
 * 最常用策略、平均改进效果等关键指标
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class FallbackAlgorithmStatistics {
    
    /**
     * 总执行次数
     */
    private int totalExecutions;
    
    /**
     * 成功执行次数
     */
    private int successfulExecutions;
    
    /**
     * 平均执行时间（毫秒）
     */
    private double averageExecutionTime;
    
    /**
     * 最常使用的策略
     */
    private FallbackStrategy mostUsedStrategy;
    
    /**
     * 平均改进百分比
     */
    private double averageImprovement;
    
    /**
     * 最大执行时间（毫秒）
     */
    @Builder.Default
    private long maxExecutionTime = 0L;
    
    /**
     * 最小执行时间（毫秒）
     */
    @Builder.Default
    private long minExecutionTime = Long.MAX_VALUE;
    
    /**
     * 各策略执行次数统计
     */
    @Builder.Default
    private Map<FallbackStrategy, Integer> strategyUsageCount = new ConcurrentHashMap<>();
    
    /**
     * 各策略成功次数统计
     */
    @Builder.Default
    private Map<FallbackStrategy, Integer> strategySuccessCount = new ConcurrentHashMap<>();
    
    /**
     * 各策略平均执行时间统计
     */
    @Builder.Default
    private Map<FallbackStrategy, Double> strategyAverageTime = new ConcurrentHashMap<>();
    
    /**
     * 各策略平均改进效果统计
     */
    @Builder.Default
    private Map<FallbackStrategy, Double> strategyAverageImprovement = new ConcurrentHashMap<>();
    
    /**
     * 最近一次执行时间
     */
    private LocalDateTime lastExecutionTime;
    
    /**
     * 总执行时间（毫秒）
     */
    @Builder.Default
    private long totalExecutionTime = 0L;
    
    /**
     * 计算总体成功率
     */
    public double getOverallSuccessRate() {
        if (totalExecutions == 0) return 0.0;
        return (double) successfulExecutions / totalExecutions * 100.0;
    }
    
    /**
     * 计算策略成功率
     */
    public double getStrategySuccessRate(FallbackStrategy strategy) {
        int usage = strategyUsageCount.getOrDefault(strategy, 0);
        if (usage == 0) return 0.0;
        
        int success = strategySuccessCount.getOrDefault(strategy, 0);
        return (double) success / usage * 100.0;
    }
    
    /**
     * 获取最高成功率的策略
     */
    public FallbackStrategy getMostSuccessfulStrategy() {
        FallbackStrategy best = null;
        double bestRate = -1.0;
        
        for (FallbackStrategy strategy : strategyUsageCount.keySet()) {
            double rate = getStrategySuccessRate(strategy);
            if (rate > bestRate) {
                bestRate = rate;
                best = strategy;
            }
        }
        
        return best;
    }
    
    /**
     * 获取最快执行的策略
     */
    public FallbackStrategy getFastestStrategy() {
        FallbackStrategy fastest = null;
        double fastestTime = Double.MAX_VALUE;
        
        for (Map.Entry<FallbackStrategy, Double> entry : strategyAverageTime.entrySet()) {
            if (entry.getValue() < fastestTime) {
                fastestTime = entry.getValue();
                fastest = entry.getKey();
            }
        }
        
        return fastest;
    }
    
    /**
     * 获取最有效的策略（改进效果最好）
     */
    public FallbackStrategy getMostEffectiveStrategy() {
        FallbackStrategy mostEffective = null;
        double bestImprovement = -1.0;
        
        for (Map.Entry<FallbackStrategy, Double> entry : strategyAverageImprovement.entrySet()) {
            if (entry.getValue() > bestImprovement) {
                bestImprovement = entry.getValue();
                mostEffective = entry.getKey();
            }
        }
        
        return mostEffective;
    }
    
    /**
     * 计算执行频率（次/天）
     */
    public double getExecutionFrequency() {
        if (lastExecutionTime == null) return 0.0;
        
        LocalDateTime now = LocalDateTime.now();
        long daysBetween = java.time.Duration.between(lastExecutionTime.minusDays(30), now).toDays();
        
        if (daysBetween == 0) return totalExecutions;
        return (double) totalExecutions / daysBetween;
    }
    
    /**
     * 计算平均改进效率（改进效果/执行时间）
     */
    public double getImprovementEfficiency() {
        if (averageExecutionTime == 0) return 0.0;
        return averageImprovement / (averageExecutionTime / 1000.0); // 改进%/秒
    }
    
    /**
     * 更新统计信息
     */
    public void updateStatistics(FallbackOptimizationResult result) {
        if (result == null) return;
        
        // 更新总体统计
        totalExecutions++;
        if (result.isSuccess()) {
            successfulExecutions++;
        }
        
        // 更新执行时间统计
        long executionTime = result.getExecutionTimeMs();
        totalExecutionTime += executionTime;
        averageExecutionTime = (double) totalExecutionTime / totalExecutions;
        
        maxExecutionTime = Math.max(maxExecutionTime, executionTime);
        if (minExecutionTime == Long.MAX_VALUE) {
            minExecutionTime = executionTime;
        } else {
            minExecutionTime = Math.min(minExecutionTime, executionTime);
        }
        
        // 更新策略统计
        FallbackStrategy strategy = result.getStrategy();
        if (strategy != null) {
            // 使用次数
            strategyUsageCount.merge(strategy, 1, Integer::sum);
            
            // 成功次数
            if (result.isSuccess()) {
                strategySuccessCount.merge(strategy, 1, Integer::sum);
            }
            
            // 平均执行时间
            updateStrategyAverageTime(strategy, executionTime);
            
            // 平均改进效果
            if (result.getOptimizationMetrics() != null) {
                double improvement = result.getOptimizationMetrics().getTimeImprovement();
                updateStrategyAverageImprovement(strategy, improvement);
                
                // 更新总体平均改进
                updateOverallAverageImprovement(improvement);
            }
        }
        
        // 更新最近执行时间
        lastExecutionTime = LocalDateTime.now();
        
        // 更新最常用策略
        updateMostUsedStrategy();
    }
    
    /**
     * 更新策略平均执行时间
     */
    private void updateStrategyAverageTime(FallbackStrategy strategy, long executionTime) {
        int count = strategyUsageCount.get(strategy);
        double currentAverage = strategyAverageTime.getOrDefault(strategy, 0.0);
        double newAverage = (currentAverage * (count - 1) + executionTime) / count;
        strategyAverageTime.put(strategy, newAverage);
    }
    
    /**
     * 更新策略平均改进效果
     */
    private void updateStrategyAverageImprovement(FallbackStrategy strategy, double improvement) {
        int successCount = strategySuccessCount.getOrDefault(strategy, 0);
        if (successCount == 0) return;
        
        double currentAverage = strategyAverageImprovement.getOrDefault(strategy, 0.0);
        double newAverage = (currentAverage * (successCount - 1) + improvement) / successCount;
        strategyAverageImprovement.put(strategy, newAverage);
    }
    
    /**
     * 更新总体平均改进
     */
    private void updateOverallAverageImprovement(double improvement) {
        if (successfulExecutions == 0) {
            averageImprovement = 0.0;
        } else {
            averageImprovement = (averageImprovement * (successfulExecutions - 1) + improvement) / successfulExecutions;
        }
    }
    
    /**
     * 更新最常用策略
     */
    private void updateMostUsedStrategy() {
        int maxCount = 0;
        FallbackStrategy mostUsed = null;
        
        for (Map.Entry<FallbackStrategy, Integer> entry : strategyUsageCount.entrySet()) {
            if (entry.getValue() > maxCount) {
                maxCount = entry.getValue();
                mostUsed = entry.getKey();
            }
        }
        
        mostUsedStrategy = mostUsed;
    }
    
    /**
     * 生成统计摘要
     */
    public String generateSummary() {
        return String.format(
            "执行%d次 | 成功率%.1f%% | 平均耗时%.0fms | 平均改进%.1f%% | 最常用:%s | 效率%.2f改进/秒",
            totalExecutions,
            getOverallSuccessRate(),
            averageExecutionTime,
            averageImprovement,
            mostUsedStrategy != null ? mostUsedStrategy.getCode() : "无",
            getImprovementEfficiency()
        );
    }
    
    /**
     * 生成详细统计报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("📊 降级算法统计报告\n");
        report.append("════════════════════════════════════════════════════════════════\n");
        
        // 总体统计
        report.append("📈 总体执行统计:\n");
        report.append(String.format("   总执行次数: %d次\n", totalExecutions));
        report.append(String.format("   成功执行: %d次 (成功率: %.1f%%)\n", 
            successfulExecutions, getOverallSuccessRate()));
        report.append(String.format("   失败执行: %d次 (失败率: %.1f%%)\n", 
            totalExecutions - successfulExecutions, 100.0 - getOverallSuccessRate()));
        
        // 时间统计
        report.append(String.format("\n⏱️ 执行时间统计:\n"));
        report.append(String.format("   平均执行时间: %.0fms (%.2fs)\n", 
            averageExecutionTime, averageExecutionTime / 1000.0));
        report.append(String.format("   最长执行时间: %dms (%.2fs)\n", 
            maxExecutionTime, maxExecutionTime / 1000.0));
        report.append(String.format("   最短执行时间: %dms (%.2fs)\n", 
            minExecutionTime == Long.MAX_VALUE ? 0 : minExecutionTime, 
            (minExecutionTime == Long.MAX_VALUE ? 0 : minExecutionTime) / 1000.0));
        report.append(String.format("   总执行时间: %.2fs\n", totalExecutionTime / 1000.0));
        
        // 效果统计
        report.append(String.format("\n🎯 优化效果统计:\n"));
        report.append(String.format("   平均改进效果: %.1f%%\n", averageImprovement));
        report.append(String.format("   改进效率: %.2f 改进%%/秒\n", getImprovementEfficiency()));
        
        // 策略统计
        report.append(String.format("\n🔧 策略使用统计:\n"));
        if (mostUsedStrategy != null) {
            report.append(String.format("   最常用策略: %s (使用%d次)\n", 
                mostUsedStrategy.getName(), strategyUsageCount.getOrDefault(mostUsedStrategy, 0)));
        }
        
        FallbackStrategy mostSuccessful = getMostSuccessfulStrategy();
        if (mostSuccessful != null) {
            report.append(String.format("   成功率最高: %s (%.1f%%)\n", 
                mostSuccessful.getName(), getStrategySuccessRate(mostSuccessful)));
        }
        
        FallbackStrategy fastest = getFastestStrategy();
        if (fastest != null) {
            report.append(String.format("   执行最快: %s (%.0fms)\n", 
                fastest.getName(), strategyAverageTime.getOrDefault(fastest, 0.0)));
        }
        
        FallbackStrategy mostEffective = getMostEffectiveStrategy();
        if (mostEffective != null) {
            report.append(String.format("   效果最好: %s (%.1f%%改进)\n", 
                mostEffective.getName(), strategyAverageImprovement.getOrDefault(mostEffective, 0.0)));
        }
        
        // 各策略详细统计
        if (!strategyUsageCount.isEmpty()) {
            report.append(String.format("\n📋 各策略详细统计:\n"));
            for (FallbackStrategy strategy : FallbackStrategy.values()) {
                int usage = strategyUsageCount.getOrDefault(strategy, 0);
                if (usage > 0) {
                    int success = strategySuccessCount.getOrDefault(strategy, 0);
                    double avgTime = strategyAverageTime.getOrDefault(strategy, 0.0);
                    double avgImprovement = strategyAverageImprovement.getOrDefault(strategy, 0.0);
                    
                    report.append(String.format("   %s: 使用%d次 | 成功%d次(%.1f%%) | 平均%.0fms | 改进%.1f%%\n",
                        strategy.getCode(), usage, success, getStrategySuccessRate(strategy),
                        avgTime, avgImprovement));
                }
            }
        }
        
        // 使用频率
        report.append(String.format("\n📅 使用频率统计:\n"));
        report.append(String.format("   执行频率: %.2f次/天\n", getExecutionFrequency()));
        if (lastExecutionTime != null) {
            report.append(String.format("   最近执行: %s\n", lastExecutionTime.toString()));
        }
        
        report.append("════════════════════════════════════════════════════════════════\n");
        
        return report.toString();
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        totalExecutions = 0;
        successfulExecutions = 0;
        averageExecutionTime = 0.0;
        averageImprovement = 0.0;
        mostUsedStrategy = null;
        maxExecutionTime = 0L;
        minExecutionTime = Long.MAX_VALUE;
        totalExecutionTime = 0L;
        lastExecutionTime = null;
        
        strategyUsageCount.clear();
        strategySuccessCount.clear();
        strategyAverageTime.clear();
        strategyAverageImprovement.clear();
    }
}