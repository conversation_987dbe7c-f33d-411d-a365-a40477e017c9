package com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.evaluation;

import lombok.Builder;
import lombok.Data;

/**
 * 工作量理论分析结果
 * 
 * 基于队列理论和负载均衡理论的工作量分析
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-08-03
 */
@Data
@Builder
public class WorkloadAnalysis {
    
    /**
     * 总工作量（分钟）
     */
    private double totalWorkload;
    
    /**
     * 当前路线数量
     */
    private int currentRouteCount;
    
    /**
     * 理想路线数量（基于350分钟理想工作时间）
     */
    private double idealRouteCount;
    
    /**
     * 平均工作时间（分钟）
     */
    private double averageWorkTime;
    
    /**
     * 工作时间方差
     */
    private double workTimeVariance;
    
    /**
     * 工作时间标准差
     */
    private double standardDeviation;
    
    /**
     * 变异系数（标准差/均值）
     */
    private double coefficientOfVariation;
    
    /**
     * 负载均衡指数 (0.0-1.0，越接近1越均衡)
     */
    private double loadBalanceIndex;
    
    /**
     * 路线数量差距（当前数量 - 理想数量）
     */
    private double routeCountGap;
    
    /**
     * 工作量密度（工作量/路线数）
     */
    private double workloadDensity;
    
    /**
     * 获取工作量分布评级
     */
    public WorkloadDistributionGrade getDistributionGrade() {
        if (loadBalanceIndex >= 0.9) {
            return WorkloadDistributionGrade.EXCELLENT;
        } else if (loadBalanceIndex >= 0.8) {
            return WorkloadDistributionGrade.GOOD;
        } else if (loadBalanceIndex >= 0.7) {
            return WorkloadDistributionGrade.FAIR;
        } else if (loadBalanceIndex >= 0.6) {
            return WorkloadDistributionGrade.POOR;
        } else {
            return WorkloadDistributionGrade.VERY_POOR;
        }
    }
    
    /**
     * 是否需要基于工作量调整路线数量
     */
    public boolean needsWorkloadBasedAdjustment() {
        return Math.abs(routeCountGap) > 1.5; // 差距超过1.5条路线
    }
    
    /**
     * 获取基于工作量的推荐调整方向
     */
    public RouteCountAction getWorkloadBasedRecommendation() {
        if (routeCountGap > 1.5) {
            return RouteCountAction.DECREASE; // 路线太多
        } else if (routeCountGap < -1.5) {
            return RouteCountAction.INCREASE; // 路线太少
        } else {
            return RouteCountAction.MAINTAIN; // 数量合理
        }
    }
    
    /**
     * 获取工作量均衡性评分 (0.0-1.0)
     */
    public double getWorkloadBalanceScore() {
        return loadBalanceIndex;
    }
    
    /**
     * 工作量分布等级枚举
     */
    public enum WorkloadDistributionGrade {
        EXCELLENT("优秀", 5),
        GOOD("良好", 4),
        FAIR("一般", 3),
        POOR("较差", 2),
        VERY_POOR("很差", 1);
        
        private final String description;
        private final int score;
        
        WorkloadDistributionGrade(String description, int score) {
            this.description = description;
            this.score = score;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getScore() {
            return score;
        }
    }
}