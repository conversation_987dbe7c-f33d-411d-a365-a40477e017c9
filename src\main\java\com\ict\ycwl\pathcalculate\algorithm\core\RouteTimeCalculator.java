package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation;
import com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 路线时间计算器
 * 
 * 实现您要求的完整时间评估算法：
 * 1. 地块内距离：所有点两两距离的平均值 × (点数-1)
 * 2. 地块间距离：两个地块最近两点距离
 * 3. 中转站距离：组内距离中转站最近的两个点到中转站距离之和
 * 4. 服务时间：直接使用每个点的deliveryTime（配送/卸货时间）
 * 5. 综合评估：总距离/行驶速度 + 服务时间
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-08-05
 */
@Slf4j
public class RouteTimeCalculator {
    
    private final TimeEvaluationConfig config;
    
    public RouteTimeCalculator(TimeEvaluationConfig config) {
        this.config = config;
    }
    
    // ===================== 主要评估接口 =====================
    
    /**
     * 评估添加新地块后的总时间
     * 
     * @param currentRouteBlocks 当前路线的地块列表
     * @param candidateBlock 候选要添加的新地块
     * @param depot 中转站信息
     * @return 时间评估结果
     */
    public TimeEvaluationResult evaluateAddBlock(
            List<List<Accumulation>> currentRouteBlocks,
            List<Accumulation> candidateBlock,
            TransitDepot depot) {
        
        log.debug("🔍 开始评估添加地块: {}个点", candidateBlock.size());
        
        // 1. 构建完整路线（当前路线 + 新地块）
        List<List<Accumulation>> fullRoute = new ArrayList<>(currentRouteBlocks);
        fullRoute.add(candidateBlock);
        
        // 2. 计算各项时间成本
        double serviceTime = calculateTotalServiceTime(fullRoute);
        double intraBlockTime = calculateIntraBlockTravelTime(fullRoute);
        double interBlockTime = calculateInterBlockConnectionTime(fullRoute);
        double depotTime = calculateDepotTravelTime(fullRoute, depot);
        
        // 3. 汇总时间
        double totalTravelTime = intraBlockTime + interBlockTime + depotTime;
        double totalTimeHours = (serviceTime + totalTravelTime) / 60.0; // 转换为小时
        
        log.debug("⏱️ 时间计算结果: 配送{:.1f}min + 地块内{:.1f}min + 地块间{:.1f}min + 往返{:.1f}min = 总计{:.1f}min", 
            serviceTime, intraBlockTime, interBlockTime, depotTime, serviceTime + totalTravelTime);
        
        // 4. 生成评估结果
        return TimeEvaluationResult.createBasic(
            totalTimeHours,
            serviceTime / 60.0,          // 配送时间（小时）
            totalTravelTime / 60.0,      // 行驶时间（小时）
            depotTime / 60.0,            // 往返时间（小时）
            config
        );
    }
    
    /**
     * 评估当前路线的总时间
     * 
     * @param routeBlocks 路线地块列表
     * @param depot 中转站
     * @return 时间评估结果
     */
    public TimeEvaluationResult evaluateCurrentRoute(
            List<List<Accumulation>> routeBlocks,
            TransitDepot depot) {
        
        return evaluateAddBlock(routeBlocks, Collections.emptyList(), depot);
    }
    
    // ===================== 服务时间计算 =====================
    
    /**
     * 计算总配送服务时间
     * 直接使用每个点的deliveryTime（配送/卸货时间）
     */
    public double calculateTotalServiceTime(List<List<Accumulation>> routeBlocks) {
        double totalServiceTime = routeBlocks.stream()
            .flatMap(List::stream)
            .mapToDouble(acc -> {
                Double deliveryTime = acc.getDeliveryTime();
                return deliveryTime != null ? deliveryTime : 0.0;
            })
            .sum();
        
        log.debug("📦 总配送时间: {:.1f}分钟 ({}个点的deliveryTime之和)", 
            totalServiceTime, routeBlocks.stream().mapToInt(List::size).sum());
        
        return totalServiceTime;
    }
    
    // ===================== 地块内距离计算 =====================
    
    /**
     * 计算所有地块内的行驶时间
     */
    public double calculateIntraBlockTravelTime(List<List<Accumulation>> routeBlocks) {
        double totalIntraTime = routeBlocks.stream()
            .mapToDouble(this::calculateSingleBlockInternalTime)
            .sum();
        
        log.debug("🏘️ 总地块内行驶时间: {:.1f}分钟", totalIntraTime);
        return totalIntraTime;
    }
    
    /**
     * 计算单个地块内的行驶时间
     * 算法：平均距离 × (点数-1) / 行驶速度
     */
    public double calculateSingleBlockInternalTime(List<Accumulation> block) {
        if (block.size() <= 1) {
            return 0.0;
        }
        
        // 1. 计算地块内所有点两两距离的平均值
        double avgDistance = calculateIntraBlockAverageDistance(block);
        
        // 2. 估算地块内总行走距离：平均距离 × (点数-1)
        double totalDistance = avgDistance * (block.size() - 1);
        
        // 3. 转换为时间：距离 / 速度 * 60 (转换为分钟)
        double travelTimeMinutes = (totalDistance / config.getDrivingSpeedKmh()) * 60.0;
        
        log.debug("📍 地块内时间计算: {}个点 平均距离{:.3f}km 总距离{:.3f}km 时间{:.1f}min", 
            block.size(), avgDistance, totalDistance, travelTimeMinutes);
        
        return travelTimeMinutes;
    }
    
    /**
     * 计算地块内平均距离
     * 算法：所有点两两距离的平均值
     */
    public double calculateIntraBlockAverageDistance(List<Accumulation> block) {
        if (block.size() <= 1) {
            return 0.0;
        }
        
        double totalDistance = 0.0;
        int pairCount = 0;
        
        for (int i = 0; i < block.size(); i++) {
            for (int j = i + 1; j < block.size(); j++) {
                double distance = calculateDistance(
                    block.get(i).getLatitude(), block.get(i).getLongitude(),
                    block.get(j).getLatitude(), block.get(j).getLongitude()
                );
                totalDistance += distance;
                pairCount++;
            }
        }
        
        double avgDistance = totalDistance / pairCount;
        log.debug("📏 地块内平均距离: {:.3f}km ({}个点 {}对距离)", avgDistance, block.size(), pairCount);
        
        return avgDistance;
    }
    
    // ===================== 地块间距离计算 =====================
    
    /**
     * 计算地块间连接时间
     */
    public double calculateInterBlockConnectionTime(List<List<Accumulation>> routeBlocks) {
        if (routeBlocks.size() <= 1) {
            return 0.0;
        }
        
        double totalConnectionTime = 0.0;
        
        for (int i = 0; i < routeBlocks.size() - 1; i++) {
            List<Accumulation> currentBlock = routeBlocks.get(i);
            List<Accumulation> nextBlock = routeBlocks.get(i + 1);
            
            double connectionDistance = calculateInterBlockDistance(currentBlock, nextBlock);
            double connectionTime = (connectionDistance / config.getDrivingSpeedKmh()) * 60.0;
            
            totalConnectionTime += connectionTime;
            
            log.debug("🔗 地块间连接: 地块{}→地块{} 距离{:.3f}km 时间{:.1f}min", 
                i+1, i+2, connectionDistance, connectionTime);
        }
        
        log.debug("🌉 总地块间连接时间: {:.1f}分钟", totalConnectionTime);
        return totalConnectionTime;
    }
    
    /**
     * 计算两个地块间的最短距离
     * 算法：找到两个地块之间最近的两个点
     */
    public double calculateInterBlockDistance(List<Accumulation> block1, List<Accumulation> block2) {
        if (block1.isEmpty() || block2.isEmpty()) {
            return 0.0;
        }
        
        double minDistance = Double.MAX_VALUE;
        Accumulation closestPoint1 = null;
        Accumulation closestPoint2 = null;
        
        for (Accumulation point1 : block1) {
            for (Accumulation point2 : block2) {
                double distance = calculateDistance(
                    point1.getLatitude(), point1.getLongitude(),
                    point2.getLatitude(), point2.getLongitude()
                );
                
                if (distance < minDistance) {
                    minDistance = distance;
                    closestPoint1 = point1;
                    closestPoint2 = point2;
                }
            }
        }
        
        log.debug("🎯 地块间最短距离: {:.3f}km (点{}→点{})", 
            minDistance, 
            closestPoint1 != null ? closestPoint1.getAccumulationId() : "null",
            closestPoint2 != null ? closestPoint2.getAccumulationId() : "null");
        
        return minDistance;
    }
    
    // ===================== 中转站距离计算 =====================
    
    /**
     * 计算到中转站的往返时间
     */
    public double calculateDepotTravelTime(List<List<Accumulation>> routeBlocks, TransitDepot depot) {
        List<Accumulation> allPoints = routeBlocks.stream()
            .flatMap(List::stream)
            .collect(Collectors.toList());
        
        if (allPoints.isEmpty()) {
            return 0.0;
        }
        
        double depotDistance = calculateDepotDistance(allPoints, depot);
        double depotTime = (depotDistance / config.getDrivingSpeedKmh()) * 60.0;
        
        log.debug("🏪 中转站往返时间: 距离{:.3f}km 时间{:.1f}min", depotDistance, depotTime);
        
        return depotTime;
    }
    
    /**
     * 计算组内距离中转站最近的两个点到中转站的距离之和
     * 算法：找到距离中转站最近的两个点，计算距离和
     */
    public double calculateDepotDistance(List<Accumulation> routePoints, TransitDepot depot) {
        if (routePoints.isEmpty()) {
            return 0.0;
        }
        
        // 计算所有点到中转站的距离
        List<DepotDistanceInfo> depotDistances = routePoints.stream()
            .map(point -> {
                double distance = calculateDistance(
                    depot.getLatitude(), depot.getLongitude(),
                    point.getLatitude(), point.getLongitude()
                );
                return new DepotDistanceInfo(point, distance);
            })
            .sorted(Comparator.comparingDouble(DepotDistanceInfo::getDistance))
            .collect(Collectors.toList());
        
        // 取最近的两个点到中转站的距离之和
        double totalDepotDistance;
        if (depotDistances.size() >= 2) {
            totalDepotDistance = depotDistances.get(0).getDistance() + depotDistances.get(1).getDistance();
            log.debug("🎯 中转站最近两点: 点{}({:.3f}km) + 点{}({:.3f}km) = {:.3f}km", 
                depotDistances.get(0).getPoint().getAccumulationId(), depotDistances.get(0).getDistance(),
                depotDistances.get(1).getPoint().getAccumulationId(), depotDistances.get(1).getDistance(),
                totalDepotDistance);
        } else {
            // 只有一个点时，往返距离 = 距离 × 2
            totalDepotDistance = depotDistances.get(0).getDistance() * 2;
            log.debug("🎯 中转站单点往返: 点{}({:.3f}km) × 2 = {:.3f}km", 
                depotDistances.get(0).getPoint().getAccumulationId(), 
                depotDistances.get(0).getDistance(), totalDepotDistance);
        }
        
        return totalDepotDistance;
    }
    
    // ===================== 工具方法 =====================
    
    /**
     * 计算两点间距离（Haversine公式）
     */
    public double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 中转站距离信息内部类
     */
    private static class DepotDistanceInfo {
        private final Accumulation point;
        private final double distance;
        
        public DepotDistanceInfo(Accumulation point, double distance) {
            this.point = point;
            this.distance = distance;
        }
        
        public Accumulation getPoint() { return point; }
        public double getDistance() { return distance; }
    }
}