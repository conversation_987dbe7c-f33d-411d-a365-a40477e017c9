package com.ict.ycwl.pathcalculate.algorithm.core;

import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 高级约束优化器管理器
 * 智能协调可用的高性能库，根据问题特征自动选择最优求解器
 * 
 * 集成的高性能算法：
 * 1. Google OR-Tools CP-SAT - 工业级约束求解器
 * 2. 自研TSPConstraintEnforcer - 轻量级高效方案
 * 3. 基础约束执行器 - 保障性解决方案
 */
@Slf4j
@Component
public class AdvancedConstraintOptimizerManager {
    
    // 约束参数
    private static final double MAX_ROUTE_TIME_MINUTES = 450.0;
    private static final double TIME_GAP_LIMIT_MINUTES = 30.0;
    
    // 问题规模阈值
    private static final int SMALL_PROBLEM_THRESHOLD = 50;   // 小规模：<50个聚集区
    private static final int MEDIUM_PROBLEM_THRESHOLD = 200; // 中规模：<200个聚集区
    // 大规模：>=200个聚集区
    
    // 优化器实例
    private final TSPConstraintEnforcer tspConstraintEnforcer;
    
    public AdvancedConstraintOptimizerManager() {
        this.tspConstraintEnforcer = new TSPConstraintEnforcer();
    }
    
    /**
     * 智能约束优化入口
     * 根据问题特征自动选择最优求解策略
     */
    public boolean performSmartConstraintOptimization(AlgorithmContext context) {
        log.info("🎯 [智能优化器] 开始分析问题特征并选择最优求解策略");
        
        // 分析问题特征
        ProblemCharacteristics characteristics = analyzeProblemCharacteristics(context);
        
        log.info("📊 [问题分析] 规模: {}, 复杂度: {}, 约束违反: {}, 推荐策略: {}", 
            characteristics.getScale(), 
            characteristics.getComplexity(),
            characteristics.getConstraintViolations(),
            characteristics.getRecommendedStrategy());
        
        // 根据特征选择优化策略
        return executeOptimizationStrategy(context, characteristics);
    }
    
    /**
     * 分析问题特征
     */
    private ProblemCharacteristics analyzeProblemCharacteristics(AlgorithmContext context) {
        ProblemCharacteristics characteristics = new ProblemCharacteristics();
        
        // 计算问题规模
        int totalAccumulations = context.getOptimizedRoutes().values().stream()
            .mapToInt(routes -> routes.stream()
                .mapToInt(route -> route.getAccumulationSequence().size())
                .sum())
            .sum();
        
        int totalRoutes = context.getOptimizedRoutes().values().stream()
            .mapToInt(List::size)
            .sum();
        
        // 确定规模等级
        ProblemScale scale;
        if (totalAccumulations < SMALL_PROBLEM_THRESHOLD) {
            scale = ProblemScale.SMALL;
        } else if (totalAccumulations < MEDIUM_PROBLEM_THRESHOLD) {
            scale = ProblemScale.MEDIUM;
        } else {
            scale = ProblemScale.LARGE;
        }
        characteristics.setScale(scale);
        
        // 分析复杂度
        double avgRouteSize = (double) totalAccumulations / totalRoutes;
        int numDepots = context.getOptimizedRoutes().size();
        
        ProblemComplexity complexity;
        if (numDepots <= 3 && avgRouteSize <= 10) {
            complexity = ProblemComplexity.LOW;
        } else if (numDepots <= 8 && avgRouteSize <= 25) {
            complexity = ProblemComplexity.MEDIUM;
        } else {
            complexity = ProblemComplexity.HIGH;
        }
        characteristics.setComplexity(complexity);
        
        // 分析约束违反情况
        int violatingRoutes = 0;
        int excessiveGapDepots = 0;
        double maxTimeViolation = 0.0;
        double maxGapViolation = 0.0;
        
        for (List<RouteResult> routes : context.getOptimizedRoutes().values()) {
            // 检查450分钟约束违反
            for (RouteResult route : routes) {
                if (route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES) {
                    violatingRoutes++;
                    maxTimeViolation = Math.max(maxTimeViolation, 
                        route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES);
                }
            }
            
            // 检查时间差距约束违反
            if (routes.size() > 1) {
                double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                double timeGap = maxTime - minTime;
                
                if (timeGap > TIME_GAP_LIMIT_MINUTES) {
                    excessiveGapDepots++;
                    maxGapViolation = Math.max(maxGapViolation, timeGap - TIME_GAP_LIMIT_MINUTES);
                }
            }
        }
        
        ConstraintViolationLevel violationLevel;
        if (violatingRoutes == 0 && excessiveGapDepots == 0) {
            violationLevel = ConstraintViolationLevel.NONE;
        } else if (violatingRoutes <= 2 && excessiveGapDepots <= 1 && maxTimeViolation <= 60) {
            violationLevel = ConstraintViolationLevel.MINOR;
        } else if (violatingRoutes <= 5 && excessiveGapDepots <= 3 && maxTimeViolation <= 120) {
            violationLevel = ConstraintViolationLevel.MODERATE;
        } else {
            violationLevel = ConstraintViolationLevel.SEVERE;
        }
        characteristics.setConstraintViolations(violationLevel);
        
        // 根据特征推荐策略
        OptimizationStrategy recommendedStrategy = recommendStrategy(scale, complexity, violationLevel);
        characteristics.setRecommendedStrategy(recommendedStrategy);
        
        // 记录详细统计
        characteristics.setTotalAccumulations(totalAccumulations);
        characteristics.setTotalRoutes(totalRoutes);
        characteristics.setNumDepots(numDepots);
        characteristics.setViolatingRoutes(violatingRoutes);
        characteristics.setExcessiveGapDepots(excessiveGapDepots);
        characteristics.setMaxTimeViolation(maxTimeViolation);
        characteristics.setMaxGapViolation(maxGapViolation);
        
        return characteristics;
    }
    
    /**
     * 根据问题特征推荐优化策略
     */
    private OptimizationStrategy recommendStrategy(ProblemScale scale, ProblemComplexity complexity, 
                                                 ConstraintViolationLevel violations) {
        
        // 简化策略选择：只使用轻量级优化器（已经过优化的TSP约束执行器）
        return OptimizationStrategy.LIGHTWEIGHT;
    }
    
    /**
     * 执行优化策略
     */
    private boolean executeOptimizationStrategy(AlgorithmContext context, ProblemCharacteristics characteristics) {
        OptimizationStrategy strategy = characteristics.getRecommendedStrategy();
        
        log.info("🚀 [策略执行] 使用{}策略进行约束优化", strategy);
        
        boolean success = false;
        
        try {
            switch (strategy) {
                case LIGHTWEIGHT:
                    success = tspConstraintEnforcer.enforceTimeConstraints(context);
                    break;
                    
                default:
                    log.warn("⚠️ [未知策略] 使用轻量级优化器");
                    success = tspConstraintEnforcer.enforceTimeConstraints(context);
                    break;
            }
            
            // 如果所有策略都失败，尝试最后的保障措施
            if (!success) {
                log.warn("⚠️ [最后尝试] 所有高级优化器失败，尝试基础约束执行");
                success = performBasicConstraintEnforcement(context);
            }
            
            // 验证最终结果
            if (success) {
                validateFinalResults(context);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("❌ [策略执行失败] {}: {}", strategy, e.getMessage());
            
            // 异常情况下的应急处理
            try {
                log.info("🔧 [应急处理] 尝试基础约束执行");
                return performBasicConstraintEnforcement(context);
            } catch (Exception fallbackException) {
                log.error("❌ [应急处理失败] {}", fallbackException.getMessage());
                return false;
            }
        }
    }
    
    /**
     * 基础约束执行（保障措施）
     */
    private boolean performBasicConstraintEnforcement(AlgorithmContext context) {
        log.info("🔧 [基础约束执行] 使用简单启发式方法强制满足约束");
        
        boolean hasAnyImprovement = false;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            
            if (routes.size() <= 1) continue;
            
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            // 简单的负载均衡：从最重的路线转移聚集区到最轻的路线
            for (int iteration = 0; iteration < 10; iteration++) {
                RouteResult heaviestRoute = routes.stream()
                    .max(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                    .orElse(null);
                RouteResult lightestRoute = routes.stream()
                    .min(Comparator.comparingDouble(RouteResult::getTotalWorkTime))
                    .orElse(null);
                
                if (heaviestRoute == null || lightestRoute == null || 
                    heaviestRoute == lightestRoute ||
                    heaviestRoute.getTotalWorkTime() <= MAX_ROUTE_TIME_MINUTES) {
                    break;
                }
                
                // 尝试转移一个聚集区
                if (trySimpleTransfer(heaviestRoute, lightestRoute)) {
                    hasAnyImprovement = true;
                    log.debug("🔄 [简单转移] 中转站{}进行了负载均衡调整", depot.getTransitDepotName());
                } else {
                    break;
                }
            }
        }
        
        return hasAnyImprovement;
    }
    
    /**
     * 简单的聚集区转移
     */
    private boolean trySimpleTransfer(RouteResult fromRoute, RouteResult toRoute) {
        List<Long> fromSequence = new ArrayList<>(fromRoute.getAccumulationSequence());
        
        if (fromSequence.isEmpty()) return false;
        
        // 转移最后一个聚集区（简单策略）
        Long lastAcc = fromSequence.get(fromSequence.size() - 1);
        fromSequence.remove(fromSequence.size() - 1);
        
        List<Long> toSequence = new ArrayList<>(toRoute.getAccumulationSequence());
        toSequence.add(lastAcc);
        
        // 简单更新（不重新运行TSP）
        fromRoute.setAccumulationSequence(fromSequence);
        toRoute.setAccumulationSequence(toSequence);
        
        // 简单估算时间调整
        double transferTime = 30.0; // 假设每个聚集区30分钟
        fromRoute.setTotalWorkTime(fromRoute.getTotalWorkTime() - transferTime);
        toRoute.setTotalWorkTime(toRoute.getTotalWorkTime() + transferTime);
        
        return true;
    }
    
    /**
     * 验证最终结果
     */
    private void validateFinalResults(AlgorithmContext context) {
        log.info("🔍 [结果验证] 检查最终约束满足情况");
        
        int totalViolatingRoutes = 0;
        int totalExcessiveGapDepots = 0;
        double maxTimeViolation = 0.0;
        double maxGapViolation = 0.0;
        
        for (Map.Entry<Long, List<RouteResult>> entry : context.getOptimizedRoutes().entrySet()) {
            Long transitDepotId = entry.getKey();
            List<RouteResult> routes = entry.getValue();
            TransitDepot depot = context.getTransitDepotById(transitDepotId);
            
            // 检查时间约束
            for (RouteResult route : routes) {
                if (route.getTotalWorkTime() > MAX_ROUTE_TIME_MINUTES) {
                    totalViolatingRoutes++;
                    maxTimeViolation = Math.max(maxTimeViolation, 
                        route.getTotalWorkTime() - MAX_ROUTE_TIME_MINUTES);
                }
            }
            
            // 检查时间差距
            if (routes.size() > 1) {
                double maxTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).max().orElse(0.0);
                double minTime = routes.stream().mapToDouble(RouteResult::getTotalWorkTime).min().orElse(0.0);
                double timeGap = maxTime - minTime;
                
                if (timeGap > TIME_GAP_LIMIT_MINUTES) {
                    totalExcessiveGapDepots++;
                    maxGapViolation = Math.max(maxGapViolation, timeGap - TIME_GAP_LIMIT_MINUTES);
                }
                
                log.info("✅ [中转站验证] {}: 最长{:.1f}分钟, 最短{:.1f}分钟, 差距{:.1f}分钟", 
                    depot.getTransitDepotName(), maxTime, minTime, timeGap);
            }
        }
        
        if (totalViolatingRoutes == 0 && totalExcessiveGapDepots == 0) {
            log.info("🎉 [完美约束] 所有约束均已满足！");
        } else {
            log.warn("⚠️ [约束摘要] 违反450分钟的路线: {}, 违反30分钟差距的中转站: {}", 
                totalViolatingRoutes, totalExcessiveGapDepots);
            log.warn("   最大时间违反: {:.1f}分钟, 最大差距违反: {:.1f}分钟", 
                maxTimeViolation, maxGapViolation);
        }
    }
    
    // =================== 枚举定义 ===================
    
    public enum ProblemScale {
        SMALL, MEDIUM, LARGE
    }
    
    public enum ProblemComplexity {
        LOW, MEDIUM, HIGH
    }
    
    public enum ConstraintViolationLevel {
        NONE, MINOR, MODERATE, SEVERE
    }
    
    public enum OptimizationStrategy {
        LIGHTWEIGHT         // 自研轻量级优化器
    }
    
    // =================== 问题特征类 ===================
    
    public static class ProblemCharacteristics {
        private ProblemScale scale;
        private ProblemComplexity complexity;
        private ConstraintViolationLevel constraintViolations;
        private OptimizationStrategy recommendedStrategy;
        
        // 详细统计
        private int totalAccumulations;
        private int totalRoutes;
        private int numDepots;
        private int violatingRoutes;
        private int excessiveGapDepots;
        private double maxTimeViolation;
        private double maxGapViolation;
        
        // Getters and Setters
        public ProblemScale getScale() { return scale; }
        public void setScale(ProblemScale scale) { this.scale = scale; }
        
        public ProblemComplexity getComplexity() { return complexity; }
        public void setComplexity(ProblemComplexity complexity) { this.complexity = complexity; }
        
        public ConstraintViolationLevel getConstraintViolations() { return constraintViolations; }
        public void setConstraintViolations(ConstraintViolationLevel constraintViolations) { this.constraintViolations = constraintViolations; }
        
        public OptimizationStrategy getRecommendedStrategy() { return recommendedStrategy; }
        public void setRecommendedStrategy(OptimizationStrategy recommendedStrategy) { this.recommendedStrategy = recommendedStrategy; }
        
        public int getTotalAccumulations() { return totalAccumulations; }
        public void setTotalAccumulations(int totalAccumulations) { this.totalAccumulations = totalAccumulations; }
        
        public int getTotalRoutes() { return totalRoutes; }
        public void setTotalRoutes(int totalRoutes) { this.totalRoutes = totalRoutes; }
        
        public int getNumDepots() { return numDepots; }
        public void setNumDepots(int numDepots) { this.numDepots = numDepots; }
        
        public int getViolatingRoutes() { return violatingRoutes; }
        public void setViolatingRoutes(int violatingRoutes) { this.violatingRoutes = violatingRoutes; }
        
        public int getExcessiveGapDepots() { return excessiveGapDepots; }
        public void setExcessiveGapDepots(int excessiveGapDepots) { this.excessiveGapDepots = excessiveGapDepots; }
        
        public double getMaxTimeViolation() { return maxTimeViolation; }
        public void setMaxTimeViolation(double maxTimeViolation) { this.maxTimeViolation = maxTimeViolation; }
        
        public double getMaxGapViolation() { return maxGapViolation; }
        public void setMaxGapViolation(double maxGapViolation) { this.maxGapViolation = maxGapViolation; }
    }
}